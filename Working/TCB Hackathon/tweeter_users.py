
import csv
from pymongo import MongoClient

# Connect to MongoDB (adjust URI if needed)
client = MongoClient("*************************************************************************************************************")

# Connect to database and collection
db = client["cdp_db"]
collection = db["twitter_users"]

# Fields relevant to Social Scoring
fields_to_export = [
    "_id",
    "created",
    "favouritesCount",
    "friendsCount",
    "listedCount",
    "mediaCount",
    "followersCount",
    "statusesCount",
    "rawDescription",
    "verified",
    "blue"
]

# MongoDB projection format: field: 1
projection = {field: 1 for field in fields_to_export}

# Output file
filename = "1M_twitter_users.csv"

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 1000000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# Write to CSV
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(fields_to_export)  # Write header
    for doc in cursor:
        row = [doc.get(field, "") for field in fields_to_export]
        writer.writerow(row)

print(f"✅ Exported 1M records with selected fields to '{filename}'")
