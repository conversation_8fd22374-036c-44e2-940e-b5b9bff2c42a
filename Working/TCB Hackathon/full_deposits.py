import csv
from pymongo import MongoClient

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")

# --- Connect to DB and collection ---
db = client["knowledge_graph"]
collection = db["deposits"]

# --- Output file ---
filename = "1M_knowledge_graph_deposits.csv"

# --- CSV headers ---
csv_headers = [
    "walletAddress",
    "lendingPoolAddress",
    "timestamp",
    "tokenAddress",
    "valueInUSD",
    "totalNumberOfDeposit",
    "totalAmountOfDepositInUSD",
    "highestDepositInUSD",
    "lowestDepositInUSD",
    "averageDepositInUSD"
]

# --- Fields to project ---
projection = {
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "depositLogs": 1,
    "depositTokens": 1,
    "totalNumberOfDeposit": 1,
    "totalAmountOfDepositInUSD": 1,
    "highestDepositInUSD": 1,
    "lowestDepositInUSD": 1,
    "averageDepositInUSD": 1
}

# --- Randomly sample 50,000 documents ---
pipeline = [
    {"$sample": {"size": 1000000}},
    {"$project": projection}
]

cursor = collection.aggregate(pipeline)

# --- Write to CSV ---
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(csv_headers)

    for doc in cursor:
        wallet = doc.get("walletAddress", "")
        pool = doc.get("lendingPoolAddress", "")
        stats = [
            doc.get("totalNumberOfDeposit", ""),
            doc.get("totalAmountOfDepositInUSD", ""),
            doc.get("highestDepositInUSD", ""),
            doc.get("lowestDepositInUSD", ""),
            doc.get("averageDepositInUSD", "")
        ]

        deposit_logs = doc.get("depositLogs", {})
        for timestamp, log in deposit_logs.items():
            value = log.get("valueInUSD", "")
            tokens = log.get("tokens", {})
            for token_address, token_value in tokens.items():
                row = [
                    wallet,
                    pool,
                    timestamp,
                    token_address,
                    value,
                    *stats
                ]
                writer.writerow(row)

print(f"✅ Exported 1M documents from 'knowledge_graph.deposits' to '{filename}'")
