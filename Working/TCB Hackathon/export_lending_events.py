from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["arbitrum_blockchain_etl"]
collection = db["lending_events"]

# --- Projection fields ---
projection_fields = {
    "_id": 1,
    "account_tokens": 1,
    "amount": 1,
    "block_number": 1,
    "block_timestamp": 1,
    "contract_address": 1,
    "event_type": 1,
    "log_index": 1,
    "token_amount": 1,
    "total_supply": 1,
    "transaction_hash": 1,
    "type": 1,
    "user": 1,
    "wallet": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection_fields}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load to DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("arbitrum_blockchain_etl_lending_events_50K.csv", index=False)
print("✅ Exported 50,000 randomly sampled documents to arbitrum_blockchain_etl_lending_events_random_sample.csv")
