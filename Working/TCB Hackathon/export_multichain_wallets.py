from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["multichain_wallets"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "address": 1,
    "lastUpdatedAt": 1,
    "balanceInUSD": 1,
    "balanceChangeLogs.1743653794": 1,
    "balanceChangeLogs.1749681680": 1,
    "tokens.0x1_0x0000000000000000000000000000000000000000": 1,
    "tokens.0x1_0x5d3a536e4d6dbd6114cc1ead35777bab948e3643": 1,
    "tokens.0x1_0x7d1afa7b718fb893db30a3abc0cfc608aacfebb0": 1,
    "tokens.0x1_0xa0b86991c6218b36c1d19d4a2e9eb0ce3606eb48": 1,
    "tokens.0x1_0xe41d2489571d322189246dafa5ebde1f4699f498": 1,
    "tokens.0x89_0x53e0bca35ec356bd5dddfebbd1fc0fd03fabad39": 1,
    "tokenChangeLogs.0x1_0000.1743653794.valueInUSD": 1,
    "tokenChangeLogs.0x1_0000.1749681680.valueInUSD": 1,
    "tokenChangeLogs.0x1_5d3a.1730036814.valueInUSD": 1,
    "tokenChangeLogs.0x1_5d3a.1733793425.valueInUSD": 1,
    "tokenChangeLogs.0x1_7d1a.1729708101.valueInUSD": 1,
    "tokenChangeLogs.0x1_7d1a.1733357020.valueInUSD": 1,
    "tokenChangeLogs.0x1_a0b8.1729708101.valueInUSD": 1,
    "tokenChangeLogs.0x1_a0b8.1733357020.valueInUSD": 1,
    "tokenChangeLogs.0x1_e41d.1729708101.valueInUSD": 1,
    "tokenChangeLogs.0x1_e41d.1733357020.valueInUSD": 1,
    "tokenChangeLogs.0x89_53e0.1743653794.valueInUSD": 1,
    "tokenChangeLogs.0x89_53e0.1749681680.valueInUSD": 1,
    "depositInUSD": 1,
    "depositChangeLogs.1676246400": 1,
    "depositChangeLogs.1679270400": 1,
    "depositTokens.0x1_0x6b175474e89094c44da98b954eedeac495271d0f": 1,
    "depositTokenChangeLogs.0x1_6b17.1676246400.valueInUSD": 1,
    "depositTokenChangeLogs.0x1_6b17.1679270400.valueInUSD": 1,
    "borrowInUSD": 1,
    "borrowChangeLogs.1676246400": 1,
    "borrowChangeLogs.1679270400": 1,
    "totalValueOfLiquidation": 1,
    "numberOfLiquidation": 1,
    "flagged": 1,
    "dailyNumberOfTransactions.1744329600": 1,
    "dailyNumberOfTransactions.1749513600": 1,
    "dailyTransactionAmounts.1744329600": 1,
    "dailyTransactionAmounts.1749513600": 1,
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("knowledge_graph_multichain_wallets_50k.csv", index=False)
print("✅ Exported first 50,000 documents to multichain_wallets_export_10k.csv")
