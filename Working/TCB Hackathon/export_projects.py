from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["projects"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "idDApp": 1,
    "imgUrl": 1,
    "name": 1,
    "category": 1,
    "deployedChains.0x38": 1,
    "numberOfUsers": 1,
    "numberOfTransactions": 1,
    "transactionVolume": 1,
    "socialSignal": 1,
    "sources.dapp": 1
}

# --- Randomly sample 1M documents using aggregation ---
pipeline = [
    {"$sample": {"size": 1000000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("1M_knowledge_graph_projects.csv", index=False)
print("✅ Exported first 1M documents to projects_export_1M.csv")
