#!/usr/bin/env python3
"""
Blockchain Integration Module for DeFi Credit Scoring
Integrates ML pipeline with smart contracts for automated scoring and oracle updates
"""

import json
import asyncio
from typing import Dict, List, Optional, Tuple
import pandas as pd
import numpy as np
from web3 import Web3
from web3.middleware import geth_poa_middleware
import logging
from datetime import datetime, timedelta
import time

# Import our ML pipeline
from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline

logger = logging.getLogger(__name__)

class BlockchainIntegration:
    """
    Handles integration between ML pipeline and blockchain smart contracts
    Supports multiple chains and oracle-based score updates
    """
    
    def __init__(self, config: Dict):
        """
        Initialize blockchain integration
        
        Args:
            config: Configuration dictionary with RPC URLs, contract addresses, etc.
        """
        self.config = config
        self.web3_connections = {}
        self.contracts = {}
        self.ml_pipeline = None
        
        # Supported chains configuration
        self.supported_chains = {
            1: {"name": "Ethereum", "rpc": config.get("ethereum_rpc", "")},
            56: {"name": "BSC", "rpc": config.get("bsc_rpc", "")},
            137: {"name": "Polygon", "rpc": config.get("polygon_rpc", "")},
            42161: {"name": "Arbitrum", "rpc": config.get("arbitrum_rpc", "")},
            10: {"name": "Optimism", "rpc": config.get("optimism_rpc", "")}
        }
        
        # Initialize Web3 connections
        self._initialize_connections()
        
    def _initialize_connections(self):
        """Initialize Web3 connections for supported chains"""
        for chain_id, chain_config in self.supported_chains.items():
            if chain_config["rpc"]:
                try:
                    w3 = Web3(Web3.HTTPProvider(chain_config["rpc"]))
                    
                    # Add PoA middleware for BSC and Polygon
                    if chain_id in [56, 137]:
                        w3.middleware_onion.inject(geth_poa_middleware, layer=0)
                    
                    if w3.is_connected():
                        self.web3_connections[chain_id] = w3
                        logger.info(f"Connected to {chain_config['name']} (Chain ID: {chain_id})")
                    else:
                        logger.warning(f"Failed to connect to {chain_config['name']}")
                        
                except Exception as e:
                    logger.error(f"Error connecting to {chain_config['name']}: {e}")
    
    def load_ml_pipeline(self, data_path: str = "."):
        """Load and initialize the ML pipeline"""
        logger.info("Loading ML pipeline...")
        
        self.ml_pipeline = DeFiCreditScoringPipeline(data_path)
        self.ml_pipeline.load_data()
        self.ml_pipeline.preprocess_data()
        self.ml_pipeline.engineer_features()
        self.ml_pipeline.create_target_variable()
        self.ml_pipeline.train_models()
        
        logger.info("ML pipeline loaded and trained successfully")
    
    def load_contract(self, chain_id: int, contract_address: str, abi_path: str):
        """Load smart contract for interaction"""
        if chain_id not in self.web3_connections:
            raise ValueError(f"No connection available for chain {chain_id}")
        
        w3 = self.web3_connections[chain_id]
        
        # Load ABI
        with open(abi_path, 'r') as f:
            abi = json.load(f)
        
        # Create contract instance
        contract = w3.eth.contract(
            address=Web3.to_checksum_address(contract_address),
            abi=abi
        )
        
        self.contracts[f"{chain_id}_{contract_address}"] = {
            "contract": contract,
            "w3": w3,
            "chain_id": chain_id
        }
        
        logger.info(f"Loaded contract {contract_address} on chain {chain_id}")
        
        return contract
    
    def calculate_nfcs_for_wallet(self, wallet_address: str) -> Dict:
        """
        Calculate NFCS score for a wallet using the ML pipeline
        
        Args:
            wallet_address: Ethereum wallet address
            
        Returns:
            Dictionary with NFCS score and metadata
        """
        if not self.ml_pipeline:
            raise ValueError("ML pipeline not loaded. Call load_ml_pipeline() first.")
        
        try:
            # Get NFCS score from ML pipeline
            score_info = self.ml_pipeline.calculate_nfcs_score(wallet_address)
            
            if 'error' in score_info:
                return {
                    "success": False,
                    "error": score_info['error'],
                    "wallet_address": wallet_address
                }
            
            # Extract key information for blockchain
            nfcs_score = int(score_info['nfcs_score'])  # Convert to integer for smart contract
            risk_level = ['Low', 'Medium', 'High'].index(score_info['risk_level'])
            confidence = int(score_info['risk_probabilities'][score_info['risk_level']] * 100)
            
            # Get model version hash (simplified)
            model_version = Web3.keccak(text=f"v1.0_{datetime.now().strftime('%Y%m%d')}")
            
            return {
                "success": True,
                "wallet_address": wallet_address,
                "nfcs_score": nfcs_score,
                "risk_level": risk_level,
                "confidence": confidence,
                "model_version": model_version.hex(),
                "timestamp": int(time.time()),
                "raw_score_info": score_info
            }
            
        except Exception as e:
            logger.error(f"Error calculating NFCS for {wallet_address}: {e}")
            return {
                "success": False,
                "error": str(e),
                "wallet_address": wallet_address
            }
    
    def update_oracle_score(self, 
                           chain_id: int, 
                           oracle_contract_address: str,
                           wallet_address: str,
                           private_key: str) -> Dict:
        """
        Update NFCS score on oracle contract
        
        Args:
            chain_id: Blockchain chain ID
            oracle_contract_address: Oracle contract address
            wallet_address: Wallet to update score for
            private_key: Private key for transaction signing
            
        Returns:
            Transaction result
        """
        contract_key = f"{chain_id}_{oracle_contract_address}"
        
        if contract_key not in self.contracts:
            raise ValueError(f"Contract not loaded: {oracle_contract_address} on chain {chain_id}")
        
        contract_info = self.contracts[contract_key]
        contract = contract_info["contract"]
        w3 = contract_info["w3"]
        
        # Calculate NFCS score
        score_result = self.calculate_nfcs_for_wallet(wallet_address)
        
        if not score_result["success"]:
            return score_result
        
        try:
            # Prepare transaction
            account = w3.eth.account.from_key(private_key)
            
            # Build transaction
            transaction = contract.functions.updateScore(
                Web3.to_checksum_address(wallet_address),
                score_result["nfcs_score"],
                score_result["risk_level"],
                score_result["confidence"]
            ).build_transaction({
                'from': account.address,
                'nonce': w3.eth.get_transaction_count(account.address),
                'gas': 200000,  # Adjust as needed
                'gasPrice': w3.eth.gas_price
            })
            
            # Sign and send transaction
            signed_txn = w3.eth.account.sign_transaction(transaction, private_key)
            tx_hash = w3.eth.send_raw_transaction(signed_txn.rawTransaction)
            
            # Wait for confirmation
            receipt = w3.eth.wait_for_transaction_receipt(tx_hash)
            
            logger.info(f"Updated NFCS score for {wallet_address} on chain {chain_id}")
            logger.info(f"Transaction hash: {tx_hash.hex()}")
            
            return {
                "success": True,
                "transaction_hash": tx_hash.hex(),
                "block_number": receipt.blockNumber,
                "gas_used": receipt.gasUsed,
                "wallet_address": wallet_address,
                "nfcs_score": score_result["nfcs_score"],
                "risk_level": score_result["risk_level"]
            }
            
        except Exception as e:
            logger.error(f"Error updating oracle score: {e}")
            return {
                "success": False,
                "error": str(e),
                "wallet_address": wallet_address
            }
    
    def batch_update_scores(self,
                           chain_id: int,
                           oracle_contract_address: str,
                           wallet_addresses: List[str],
                           private_key: str) -> List[Dict]:
        """
        Batch update multiple wallet scores
        
        Args:
            chain_id: Blockchain chain ID
            oracle_contract_address: Oracle contract address
            wallet_addresses: List of wallet addresses
            private_key: Private key for transaction signing
            
        Returns:
            List of transaction results
        """
        results = []
        
        for wallet_address in wallet_addresses:
            try:
                result = self.update_oracle_score(
                    chain_id, oracle_contract_address, wallet_address, private_key
                )
                results.append(result)
                
                # Add delay to avoid rate limiting
                time.sleep(1)
                
            except Exception as e:
                logger.error(f"Error in batch update for {wallet_address}: {e}")
                results.append({
                    "success": False,
                    "error": str(e),
                    "wallet_address": wallet_address
                })
        
        return results
    
    def get_oracle_score(self, 
                        chain_id: int, 
                        oracle_contract_address: str,
                        wallet_address: str) -> Dict:
        """
        Get NFCS score from oracle contract
        
        Args:
            chain_id: Blockchain chain ID
            oracle_contract_address: Oracle contract address
            wallet_address: Wallet address to query
            
        Returns:
            Score information from oracle
        """
        contract_key = f"{chain_id}_{oracle_contract_address}"
        
        if contract_key not in self.contracts:
            raise ValueError(f"Contract not loaded: {oracle_contract_address} on chain {chain_id}")
        
        contract = self.contracts[contract_key]["contract"]
        
        try:
            # Call getScore function
            score_data = contract.functions.getScore(
                Web3.to_checksum_address(wallet_address)
            ).call()
            
            return {
                "success": True,
                "wallet_address": wallet_address,
                "nfcs_score": score_data[0],
                "risk_level": score_data[1],
                "timestamp": score_data[2],
                "is_valid": score_data[3],
                "confidence": score_data[4]
            }
            
        except Exception as e:
            logger.error(f"Error getting oracle score: {e}")
            return {
                "success": False,
                "error": str(e),
                "wallet_address": wallet_address
            }
    
    def check_lending_eligibility(self,
                                 chain_id: int,
                                 lending_contract_address: str,
                                 wallet_address: str,
                                 min_score: int = 400) -> Dict:
        """
        Check if wallet is eligible for lending based on NFCS score
        
        Args:
            chain_id: Blockchain chain ID
            lending_contract_address: Lending contract address
            wallet_address: Wallet address to check
            min_score: Minimum required NFCS score
            
        Returns:
            Eligibility information
        """
        contract_key = f"{chain_id}_{lending_contract_address}"
        
        if contract_key not in self.contracts:
            raise ValueError(f"Contract not loaded: {lending_contract_address} on chain {chain_id}")
        
        contract = self.contracts[contract_key]["contract"]
        
        try:
            # Call isEligibleForLending function
            eligibility_data = contract.functions.isEligibleForLending(
                Web3.to_checksum_address(wallet_address),
                min_score
            ).call()
            
            return {
                "success": True,
                "wallet_address": wallet_address,
                "is_eligible": eligibility_data[0],
                "current_score": eligibility_data[1],
                "min_required_score": min_score
            }
            
        except Exception as e:
            logger.error(f"Error checking lending eligibility: {e}")
            return {
                "success": False,
                "error": str(e),
                "wallet_address": wallet_address
            }
    
    def monitor_oracle_updates(self, 
                              chain_id: int, 
                              oracle_contract_address: str,
                              callback_function=None):
        """
        Monitor oracle contract for score updates
        
        Args:
            chain_id: Blockchain chain ID
            oracle_contract_address: Oracle contract address
            callback_function: Function to call when update detected
        """
        contract_key = f"{chain_id}_{oracle_contract_address}"
        
        if contract_key not in self.contracts:
            raise ValueError(f"Contract not loaded: {oracle_contract_address} on chain {chain_id}")
        
        contract = self.contracts[contract_key]["contract"]
        w3 = self.contracts[contract_key]["w3"]
        
        # Create event filter for ScoreUpdated events
        event_filter = contract.events.ScoreUpdated.create_filter(fromBlock='latest')
        
        logger.info(f"Monitoring oracle updates on chain {chain_id}...")
        
        while True:
            try:
                for event in event_filter.get_new_entries():
                    logger.info(f"Score updated: {event}")
                    
                    if callback_function:
                        callback_function(event)
                
                time.sleep(10)  # Check every 10 seconds
                
            except KeyboardInterrupt:
                logger.info("Monitoring stopped by user")
                break
            except Exception as e:
                logger.error(f"Error in monitoring: {e}")
                time.sleep(30)  # Wait before retrying
    
    def get_cross_chain_scores(self, wallet_address: str) -> Dict:
        """
        Get NFCS scores across all connected chains
        
        Args:
            wallet_address: Wallet address to query
            
        Returns:
            Dictionary with scores from all chains
        """
        cross_chain_scores = {}
        
        for chain_id in self.web3_connections.keys():
            # Find oracle contracts for this chain
            oracle_contracts = [
                key for key in self.contracts.keys() 
                if key.startswith(f"{chain_id}_") and "oracle" in key.lower()
            ]
            
            for contract_key in oracle_contracts:
                oracle_address = contract_key.split("_", 1)[1]
                
                try:
                    score_info = self.get_oracle_score(chain_id, oracle_address, wallet_address)
                    cross_chain_scores[f"chain_{chain_id}"] = score_info
                    
                except Exception as e:
                    logger.error(f"Error getting score from chain {chain_id}: {e}")
                    cross_chain_scores[f"chain_{chain_id}"] = {
                        "success": False,
                        "error": str(e)
                    }
        
        return cross_chain_scores
    
    def sync_scores_across_chains(self, 
                                 wallet_address: str,
                                 source_chain_id: int,
                                 target_chain_ids: List[int],
                                 private_key: str) -> Dict:
        """
        Synchronize NFCS scores across multiple chains
        
        Args:
            wallet_address: Wallet address to sync
            source_chain_id: Chain to get score from
            target_chain_ids: Chains to update score on
            private_key: Private key for transactions
            
        Returns:
            Sync results
        """
        # Get score from source chain
        source_oracle = None
        for contract_key in self.contracts.keys():
            if contract_key.startswith(f"{source_chain_id}_") and "oracle" in contract_key.lower():
                source_oracle = contract_key.split("_", 1)[1]
                break
        
        if not source_oracle:
            return {
                "success": False,
                "error": f"No oracle contract found on source chain {source_chain_id}"
            }
        
        source_score = self.get_oracle_score(source_chain_id, source_oracle, wallet_address)
        
        if not source_score["success"]:
            return {
                "success": False,
                "error": f"Failed to get score from source chain: {source_score['error']}"
            }
        
        # Update scores on target chains
        sync_results = {"source_score": source_score, "target_updates": []}
        
        for target_chain_id in target_chain_ids:
            target_oracle = None
            for contract_key in self.contracts.keys():
                if contract_key.startswith(f"{target_chain_id}_") and "oracle" in contract_key.lower():
                    target_oracle = contract_key.split("_", 1)[1]
                    break
            
            if target_oracle:
                try:
                    update_result = self.update_oracle_score(
                        target_chain_id, target_oracle, wallet_address, private_key
                    )
                    sync_results["target_updates"].append({
                        "chain_id": target_chain_id,
                        "result": update_result
                    })
                    
                except Exception as e:
                    sync_results["target_updates"].append({
                        "chain_id": target_chain_id,
                        "result": {"success": False, "error": str(e)}
                    })
        
        return sync_results
