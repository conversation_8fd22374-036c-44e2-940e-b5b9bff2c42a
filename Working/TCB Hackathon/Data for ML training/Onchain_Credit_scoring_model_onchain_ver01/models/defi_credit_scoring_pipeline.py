#!/usr/bin/env python3
"""
DeFi Credit Scoring ML Pipeline
Based on the guide for building ML models for undercollateralized DeFi lending
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
from datetime import datetime
from typing import Dict, List, Tuple, Optional
import logging

# ML Libraries
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, IsolationForest
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, roc_curve
from sklearn.cluster import KMeans, DBSCAN
from sklearn.neighbors import LocalOutlierFactor
import xgboost as xgb
from scipy import stats
from scipy.stats import boxcox
import shap

# Deep Learning Libraries for Autoencoder
try:
    import tensorflow as tf
    from tensorflow import keras
    from tensorflow.keras import layers
    TENSORFLOW_AVAILABLE = True
except ImportError:
    TENSORFLOW_AVAILABLE = False
    logger.warning("TensorFlow not available. Autoencoder anomaly detection will be disabled.")

# Suppress warnings
warnings.filterwarnings('ignore')

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DeFiCreditScoringPipeline:
    """
    Complete ML Pipeline for DeFi Credit Scoring
    Implements the methodology from Sergei Savvov's article on undercollateralized DeFi lending
    """
    
    def __init__(self, data_path: str = "."):
        """
        Initialize the pipeline
        
        Args:
            data_path: Path to the directory containing CSV files
        """
        self.data_path = data_path
        self.borrows_df = None
        self.deposits_df = None
        self.repays_df = None
        self.liquidates_df = None
        self.withdraws_df = None
        self.consolidated_df = None
        self.features_df = None
        self.scaler = StandardScaler()
        self.models = {}
        self.feature_importance = {}
        self.shap_explainers = {}
        self.shap_values = {}

        # Advanced clustering and anomaly detection
        self.clustering_models = {}
        self.anomaly_detectors = {}
        self.autoencoder = None
        self.cluster_characteristics = {}
        
        # Set plotting style
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
    def load_data(self) -> None:
        """Load all CSV files"""
        logger.info("Loading data files...")
        
        try:
            self.borrows_df = pd.read_csv(f"{self.data_path}/1. full_knowledge_graph_borrows_50K.csv")
            self.deposits_df = pd.read_csv(f"{self.data_path}/2. deposits_data_processed.csv")
            self.liquidates_df = pd.read_csv(f"{self.data_path}/3.liquidates_data_processed.csv")
            self.repays_df = pd.read_csv(f"{self.data_path}/4. repays_data_processed.csv")
            self.withdraws_df = pd.read_csv(f"{self.data_path}/4. withdraws_data_processed.csv")
            
            logger.info(f"Loaded data shapes:")
            logger.info(f"Borrows: {self.borrows_df.shape}")
            logger.info(f"Deposits: {self.deposits_df.shape}")
            logger.info(f"Repays: {self.repays_df.shape}")
            logger.info(f"Liquidates: {self.liquidates_df.shape}")
            logger.info(f"Withdraws: {self.withdraws_df.shape}")
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def preprocess_data(self) -> None:
        """
        Comprehensive data preprocessing following the guide:
        - Handle missing values
        - Remove duplicates
        - Fix data inconsistencies
        - Handle outliers
        - Handle skewed data
        - Process timestamps
        """
        logger.info("Starting data preprocessing...")
        
        # Process each dataset
        self._preprocess_borrows()
        self._preprocess_deposits()
        self._preprocess_repays()
        self._preprocess_liquidates()
        self._preprocess_withdraws()
        
        # Consolidate all data
        self._consolidate_data()
        
        logger.info("Data preprocessing completed")
    
    def _preprocess_borrows(self) -> None:
        """Preprocess borrows data"""
        logger.info("Preprocessing borrows data...")
        
        # Handle timestamp
        if 'timestamp' in self.borrows_df.columns:
            self.borrows_df['timestamp'] = pd.to_datetime(self.borrows_df['timestamp'], unit='s', errors='coerce')
            self.borrows_df['borrow_year'] = self.borrows_df['timestamp'].dt.year
            self.borrows_df['borrow_month'] = self.borrows_df['timestamp'].dt.month
            self.borrows_df['borrow_day_of_week'] = self.borrows_df['timestamp'].dt.dayofweek
        
        # Handle missing values
        numeric_cols = ['valueInUSD', 'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD', 
                       'highestBorrowInUSD', 'lowestBorrowInUSD', 'averageBorrowInUSD']
        
        for col in numeric_cols:
            if col in self.borrows_df.columns:
                self.borrows_df[col] = pd.to_numeric(self.borrows_df[col], errors='coerce')
                self.borrows_df[col].fillna(self.borrows_df[col].median(), inplace=True)
        
        # Remove duplicates based on wallet address and timestamp
        initial_shape = self.borrows_df.shape[0]
        self.borrows_df.drop_duplicates(subset=['walletAddress', 'timestamp'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.borrows_df.shape[0]} duplicate borrow records")
        
        # Handle outliers using IQR method
        self._handle_outliers(self.borrows_df, numeric_cols)
        
        # Handle skewed data
        self._handle_skewness(self.borrows_df, numeric_cols)
    
    def _preprocess_deposits(self) -> None:
        """Preprocess deposits data"""
        logger.info("Preprocessing deposits data...")
        
        # Handle missing values in deposits
        numeric_cols = ['totalNumberOfDeposit', 'totalAmountOfDepositInUSD', 
                       'highestDepositInUSD', 'lowestDepositInUSD', 'averageDepositInUSD']
        
        for col in numeric_cols:
            if col in self.deposits_df.columns:
                self.deposits_df[col] = pd.to_numeric(self.deposits_df[col], errors='coerce')
                self.deposits_df[col].fillna(self.deposits_df[col].median(), inplace=True)
        
        # Remove duplicates
        initial_shape = self.deposits_df.shape[0]
        self.deposits_df.drop_duplicates(subset=['walletAddress', 'lendingPoolAddress'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.deposits_df.shape[0]} duplicate deposit records")
        
        # Handle outliers and skewness
        self._handle_outliers(self.deposits_df, numeric_cols)
        self._handle_skewness(self.deposits_df, numeric_cols)
    
    def _preprocess_repays(self) -> None:
        """Preprocess repays data"""
        logger.info("Preprocessing repays data...")
        
        numeric_cols = ['totalAmountOfRepayInUSD', 'totalNumberOfRepay', 
                       'highestRepayInUSD', 'lowestRepayInUSD', 'averageRepayInUSD']
        
        for col in numeric_cols:
            if col in self.repays_df.columns:
                self.repays_df[col] = pd.to_numeric(self.repays_df[col], errors='coerce')
                self.repays_df[col].fillna(self.repays_df[col].median(), inplace=True)
        
        # Remove duplicates
        initial_shape = self.repays_df.shape[0]
        self.repays_df.drop_duplicates(subset=['walletAddress', 'lendingPoolAddress'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.repays_df.shape[0]} duplicate repay records")
        
        # Handle outliers and skewness
        self._handle_outliers(self.repays_df, numeric_cols)
        self._handle_skewness(self.repays_df, numeric_cols)
    
    def _preprocess_liquidates(self) -> None:
        """Preprocess liquidates data"""
        logger.info("Preprocessing liquidates data...")
        
        # Handle missing values and duplicates for liquidates
        if not self.liquidates_df.empty:
            initial_shape = self.liquidates_df.shape[0]
            self.liquidates_df.drop_duplicates(subset=['walletAddress'], keep='first', inplace=True)
            logger.info(f"Removed {initial_shape - self.liquidates_df.shape[0]} duplicate liquidation records")
    
    def _preprocess_withdraws(self) -> None:
        """Preprocess withdraws data"""
        logger.info("Preprocessing withdraws data...")
        
        numeric_cols = ['totalNumberOfWithdraw', 'totalAmountOfWithdrawInUSD', 
                       'highestWithdrawInUSD', 'lowestWithdrawInUSD', 'averageWithdrawInUSD']
        
        for col in numeric_cols:
            if col in self.withdraws_df.columns:
                self.withdraws_df[col] = pd.to_numeric(self.withdraws_df[col], errors='coerce')
                self.withdraws_df[col].fillna(self.withdraws_df[col].median(), inplace=True)
        
        # Remove duplicates
        initial_shape = self.withdraws_df.shape[0]
        self.withdraws_df.drop_duplicates(subset=['walletAddress', 'lendingPoolAddress'], keep='first', inplace=True)
        logger.info(f"Removed {initial_shape - self.withdraws_df.shape[0]} duplicate withdraw records")
        
        # Handle outliers and skewness
        self._handle_outliers(self.withdraws_df, numeric_cols)
        self._handle_skewness(self.withdraws_df, numeric_cols)
    
    def _handle_outliers(self, df: pd.DataFrame, numeric_cols: List[str]) -> None:
        """Handle outliers using IQR method"""
        for col in numeric_cols:
            if col in df.columns:
                Q1 = df[col].quantile(0.25)
                Q3 = df[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                
                # Cap outliers instead of removing them to preserve data
                df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
    
    def _handle_skewness(self, df: pd.DataFrame, numeric_cols: List[str]) -> None:
        """Handle skewed data using log transformation for right skew and box-cox for left skew"""
        for col in numeric_cols:
            if col in df.columns and df[col].dtype in ['float64', 'int64']:
                skewness = stats.skew(df[col].dropna())
                
                if abs(skewness) > 1:  # Significant skewness
                    if skewness > 1:  # Right skewed
                        # Log transformation (add 1 to handle zeros)
                        df[f'{col}_log'] = np.log1p(df[col])
                    elif skewness < -1:  # Left skewed
                        # Box-Cox transformation (requires positive values)
                        if (df[col] > 0).all():
                            try:
                                df[f'{col}_boxcox'], _ = boxcox(df[col])
                            except:
                                logger.warning(f"Box-Cox transformation failed for {col}")

    def _consolidate_data(self) -> None:
        """Consolidate all datasets into a single feature matrix"""
        logger.info("Consolidating data...")

        # Start with borrows as the base (since it has the most complete data)
        base_df = self.borrows_df.groupby('walletAddress').agg({
            'totalNumberOfBorrow': 'first',
            'totalAmountOfBorrowInUSD': 'first',
            'highestBorrowInUSD': 'first',
            'lowestBorrowInUSD': 'first',
            'averageBorrowInUSD': 'first',
            'timestamp': ['min', 'max', 'count'],
            'lendingPoolAddress': 'nunique',
            'tokenAddress': 'nunique'
        }).reset_index()

        # Flatten column names
        base_df.columns = ['walletAddress', 'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD',
                          'highestBorrowInUSD', 'lowestBorrowInUSD', 'averageBorrowInUSD',
                          'first_borrow_date', 'last_borrow_date', 'borrow_frequency',
                          'unique_lending_pools', 'unique_tokens']

        # Add deposits data
        deposits_agg = self.deposits_df.groupby('walletAddress').agg({
            'totalNumberOfDeposit': 'first',
            'totalAmountOfDepositInUSD': 'first',
            'highestDepositInUSD': 'first',
            'lowestDepositInUSD': 'first',
            'averageDepositInUSD': 'first'
        }).reset_index()

        base_df = base_df.merge(deposits_agg, on='walletAddress', how='left')

        # Add repays data
        repays_agg = self.repays_df.groupby('walletAddress').agg({
            'totalAmountOfRepayInUSD': 'first',
            'totalNumberOfRepay': 'first',
            'highestRepayInUSD': 'first',
            'lowestRepayInUSD': 'first',
            'averageRepayInUSD': 'first'
        }).reset_index()

        base_df = base_df.merge(repays_agg, on='walletAddress', how='left')

        # Add withdraws data
        withdraws_agg = self.withdraws_df.groupby('walletAddress').agg({
            'totalNumberOfWithdraw': 'first',
            'totalAmountOfWithdrawInUSD': 'first',
            'highestWithdrawInUSD': 'first',
            'lowestWithdrawInUSD': 'first',
            'averageWithdrawInUSD': 'first'
        }).reset_index()

        base_df = base_df.merge(withdraws_agg, on='walletAddress', how='left')

        # Add liquidation flag
        liquidated_wallets = set(self.liquidates_df['walletAddress'].unique()) if not self.liquidates_df.empty else set()
        base_df['has_been_liquidated'] = base_df['walletAddress'].isin(liquidated_wallets).astype(int)

        self.consolidated_df = base_df
        logger.info(f"Consolidated data shape: {self.consolidated_df.shape}")

    def engineer_features(self) -> None:
        """
        Engineer features for credit scoring following DeFi best practices
        Creates NFCS (Non-Fungible Credit Score) components
        """
        logger.info("Engineering features...")

        df = self.consolidated_df.copy()

        # Fill missing values with 0 for financial metrics
        financial_cols = [col for col in df.columns if 'USD' in col or 'Number' in col]
        df[financial_cols] = df[financial_cols].fillna(0)

        # 1. Repayment Behavior Features (Core NFCS component)
        df['repayment_ratio'] = np.where(df['totalAmountOfBorrowInUSD'] > 0,
                                        df['totalAmountOfRepayInUSD'] / df['totalAmountOfBorrowInUSD'], 0)
        df['repayment_consistency'] = np.where(df['totalNumberOfBorrow'] > 0,
                                              df['totalNumberOfRepay'] / df['totalNumberOfBorrow'], 0)

        # 2. Liquidity Management Features
        df['deposit_to_borrow_ratio'] = np.where(df['totalAmountOfBorrowInUSD'] > 0,
                                                 df['totalAmountOfDepositInUSD'] / df['totalAmountOfBorrowInUSD'], 0)
        df['withdraw_to_deposit_ratio'] = np.where(df['totalAmountOfDepositInUSD'] > 0,
                                                  df['totalAmountOfWithdrawInUSD'] / df['totalAmountOfDepositInUSD'], 0)

        # 3. Activity Diversity Features (Protocol interaction diversity)
        df['protocol_diversity_score'] = df['unique_lending_pools'] * df['unique_tokens']
        df['avg_transaction_size'] = (df['totalAmountOfBorrowInUSD'] + df['totalAmountOfDepositInUSD']) / \
                                    (df['totalNumberOfBorrow'] + df['totalNumberOfDeposit'] + 1)

        # 4. Risk Indicators
        df['liquidation_risk'] = df['has_been_liquidated']
        df['high_leverage_indicator'] = (df['totalAmountOfBorrowInUSD'] > df['totalAmountOfDepositInUSD']).astype(int)

        # 5. Temporal Features (Wallet age and activity patterns)
        df['first_borrow_date'] = pd.to_datetime(df['first_borrow_date'])
        df['last_borrow_date'] = pd.to_datetime(df['last_borrow_date'])
        df['wallet_age_days'] = (df['last_borrow_date'] - df['first_borrow_date']).dt.days
        df['activity_frequency'] = df['borrow_frequency'] / (df['wallet_age_days'] + 1)

        # 6. Composite NFCS Score Components
        # Normalize key metrics to 0-1 scale for NFCS calculation
        scaler_features = ['repayment_ratio', 'repayment_consistency', 'deposit_to_borrow_ratio',
                          'protocol_diversity_score', 'activity_frequency']

        for feature in scaler_features:
            if feature in df.columns:
                df[f'{feature}_normalized'] = (df[feature] - df[feature].min()) / (df[feature].max() - df[feature].min() + 1e-8)

        # Calculate preliminary NFCS (0-1000 scale)
        df['nfcs_score'] = (
            df['repayment_ratio_normalized'] * 0.3 +
            df['repayment_consistency_normalized'] * 0.25 +
            df['deposit_to_borrow_ratio_normalized'] * 0.2 +
            df['protocol_diversity_score_normalized'] * 0.15 +
            df['activity_frequency_normalized'] * 0.1
        ) * 1000

        # Penalize liquidated wallets
        df['nfcs_score'] = np.where(df['has_been_liquidated'] == 1,
                                   df['nfcs_score'] * 0.5, df['nfcs_score'])

        self.features_df = df
        logger.info(f"Feature engineering completed. Features shape: {self.features_df.shape}")

    def create_target_variable(self) -> None:
        """
        Create target variable for supervised learning
        Based on repayment behavior and liquidation history
        """
        logger.info("Creating target variable...")

        # Define credit risk categories based on DeFi lending criteria
        def categorize_risk(row):
            # High risk: liquidated or very poor repayment
            if row['has_been_liquidated'] == 1 or row['repayment_ratio'] < 0.5:
                return 2  # High risk
            # Medium risk: moderate repayment behavior
            elif row['repayment_ratio'] < 0.8 or row['repayment_consistency'] < 0.7:
                return 1  # Medium risk
            # Low risk: good repayment behavior
            else:
                return 0  # Low risk

        self.features_df['credit_risk'] = self.features_df.apply(categorize_risk, axis=1)

        # Also create binary classification (good vs bad borrower)
        self.features_df['is_good_borrower'] = (self.features_df['credit_risk'] == 0).astype(int)

        logger.info("Target variable created")
        logger.info(f"Credit risk distribution:\n{self.features_df['credit_risk'].value_counts()}")

    def visualize_data(self) -> None:
        """
        Create comprehensive data visualizations for EDA
        Following the guide's emphasis on understanding data distribution, outliers, and correlations
        """
        logger.info("Creating data visualizations...")

        # Set up the plotting environment
        plt.rcParams['figure.figsize'] = (15, 10)

        # 1. Distribution Analysis
        self._plot_distributions()

        # 2. Correlation Analysis
        self._plot_correlations()

        # 3. Outlier Analysis
        self._plot_outliers()

        # 4. Target Variable Analysis
        self._plot_target_analysis()

        # 5. Feature Importance Visualization (if models are trained)
        if self.models:
            self._plot_feature_importance()

        logger.info("Data visualization completed")

    def _plot_distributions(self) -> None:
        """Plot distribution of key features"""
        fig, axes = plt.subplots(3, 3, figsize=(20, 15))
        fig.suptitle('Feature Distributions', fontsize=16, fontweight='bold')

        key_features = ['totalAmountOfBorrowInUSD', 'totalAmountOfDepositInUSD', 'repayment_ratio',
                       'deposit_to_borrow_ratio', 'nfcs_score', 'protocol_diversity_score',
                       'activity_frequency', 'wallet_age_days', 'repayment_consistency']

        for i, feature in enumerate(key_features):
            if feature in self.features_df.columns:
                row, col = i // 3, i % 3

                # Histogram with KDE
                self.features_df[feature].hist(bins=50, alpha=0.7, ax=axes[row, col])
                axes[row, col].set_title(f'{feature} Distribution')
                axes[row, col].set_xlabel(feature)
                axes[row, col].set_ylabel('Frequency')

                # Add statistics
                mean_val = self.features_df[feature].mean()
                median_val = self.features_df[feature].median()
                axes[row, col].axvline(mean_val, color='red', linestyle='--', label=f'Mean: {mean_val:.2f}')
                axes[row, col].axvline(median_val, color='green', linestyle='--', label=f'Median: {median_val:.2f}')
                axes[row, col].legend()

        plt.tight_layout()
        plt.savefig('feature_distributions.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_correlations(self) -> None:
        """Plot correlation matrix"""
        # Select numeric features for correlation
        numeric_features = self.features_df.select_dtypes(include=[np.number]).columns
        correlation_matrix = self.features_df[numeric_features].corr()

        plt.figure(figsize=(16, 12))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8})
        plt.title('Feature Correlation Matrix', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('correlation_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_outliers(self) -> None:
        """Plot box plots to identify outliers"""
        key_features = ['totalAmountOfBorrowInUSD', 'totalAmountOfDepositInUSD', 'repayment_ratio',
                       'nfcs_score', 'protocol_diversity_score']

        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('Outlier Analysis - Box Plots', fontsize=16, fontweight='bold')

        for i, feature in enumerate(key_features):
            if feature in self.features_df.columns:
                row, col = i // 3, i % 3
                self.features_df.boxplot(column=feature, ax=axes[row, col])
                axes[row, col].set_title(f'{feature}')
                axes[row, col].tick_params(axis='x', rotation=45)

        # Remove empty subplot
        if len(key_features) < 6:
            fig.delaxes(axes[1, 2])

        plt.tight_layout()
        plt.savefig('outlier_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_target_analysis(self) -> None:
        """Analyze target variable distribution and relationships"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Target Variable Analysis', fontsize=16, fontweight='bold')

        # Credit risk distribution
        self.features_df['credit_risk'].value_counts().plot(kind='bar', ax=axes[0, 0])
        axes[0, 0].set_title('Credit Risk Distribution')
        axes[0, 0].set_xlabel('Risk Level (0=Low, 1=Medium, 2=High)')
        axes[0, 0].set_ylabel('Count')

        # NFCS score by risk level
        self.features_df.boxplot(column='nfcs_score', by='credit_risk', ax=axes[0, 1])
        axes[0, 1].set_title('NFCS Score by Risk Level')

        # Repayment ratio by risk level
        self.features_df.boxplot(column='repayment_ratio', by='credit_risk', ax=axes[1, 0])
        axes[1, 0].set_title('Repayment Ratio by Risk Level')

        # Liquidation vs NFCS score
        sns.scatterplot(data=self.features_df, x='nfcs_score', y='repayment_ratio',
                       hue='has_been_liquidated', ax=axes[1, 1])
        axes[1, 1].set_title('NFCS Score vs Repayment Ratio (Liquidation Status)')

        plt.tight_layout()
        plt.savefig('target_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()

    def train_models(self) -> None:
        """
        Train multiple ML models following the ensemble approach from the guide
        """
        logger.info("Training ML models...")

        # Prepare features and target
        feature_cols = [col for col in self.features_df.columns
                       if col not in ['walletAddress', 'credit_risk', 'is_good_borrower',
                                     'first_borrow_date', 'last_borrow_date']]

        X = self.features_df[feature_cols].fillna(0)
        y_multiclass = self.features_df['credit_risk']
        y_binary = self.features_df['is_good_borrower']

        # Scale features
        X_scaled = self.scaler.fit_transform(X)
        X_scaled_df = pd.DataFrame(X_scaled, columns=feature_cols)

        # Split data
        X_train, X_test, y_train_multi, y_test_multi = train_test_split(
            X_scaled_df, y_multiclass, test_size=0.2, random_state=42, stratify=y_multiclass)

        X_train_bin, X_test_bin, y_train_bin, y_test_bin = train_test_split(
            X_scaled_df, y_binary, test_size=0.2, random_state=42, stratify=y_binary)

        # 1. Random Forest (Robust for high-dimensional data)
        logger.info("Training Random Forest...")
        rf_model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
        rf_model.fit(X_train, y_train_multi)
        self.models['random_forest'] = rf_model
        self.feature_importance['random_forest'] = dict(zip(feature_cols, rf_model.feature_importances_))

        # 2. XGBoost (High performance for complex patterns)
        logger.info("Training XGBoost...")
        xgb_model = xgb.XGBClassifier(random_state=42, eval_metric='mlogloss')
        xgb_model.fit(X_train, y_train_multi)
        self.models['xgboost'] = xgb_model
        self.feature_importance['xgboost'] = dict(zip(feature_cols, xgb_model.feature_importances_))

        # 3. Isolation Forest for Anomaly Detection (Bot detection)
        logger.info("Training Isolation Forest for anomaly detection...")
        iso_forest = IsolationForest(contamination=0.1, random_state=42)
        iso_forest.fit(X_train)
        self.models['isolation_forest'] = iso_forest

        # 4. K-means Clustering for new users
        logger.info("Training K-means for user clustering...")
        kmeans = KMeans(n_clusters=5, random_state=42)
        kmeans.fit(X_train)
        self.models['kmeans'] = kmeans

        # Store training data for evaluation
        self.X_train, self.X_test = X_train, X_test
        self.y_train_multi, self.y_test_multi = y_train_multi, y_test_multi
        self.y_train_bin, self.y_test_bin = y_train_bin, y_test_bin
        self.feature_cols = feature_cols

        logger.info("Model training completed")

        # Train advanced clustering and anomaly detection models
        self.train_advanced_models()

        # Initialize SHAP explainer
        self.initialize_shap_explainer()

    def train_advanced_models(self) -> None:
        """
        Train advanced clustering and anomaly detection models
        """
        logger.info("Training advanced clustering and anomaly detection models...")

        # Prepare data for advanced models (use full dataset)
        X = self.features_df[self.feature_cols].fillna(0)
        X_scaled = self.scaler.transform(X)

        # 1. DBSCAN Clustering for New Users
        logger.info("Training DBSCAN clustering...")
        self._train_dbscan_clustering(X_scaled)

        # 2. Enhanced K-means with better parameters
        logger.info("Training enhanced K-means clustering...")
        self._train_enhanced_kmeans(X_scaled)

        # 3. Multiple Anomaly Detection Methods
        logger.info("Training anomaly detection models...")
        self._train_anomaly_detectors(X_scaled)

        # 4. Autoencoder for Deep Anomaly Detection
        if TENSORFLOW_AVAILABLE:
            logger.info("Training autoencoder for anomaly detection...")
            self._train_autoencoder(X_scaled)
        else:
            logger.warning("Skipping autoencoder training - TensorFlow not available")

        # 5. Analyze cluster characteristics
        logger.info("Analyzing cluster characteristics...")
        self._analyze_cluster_characteristics()

        logger.info("Advanced models training completed")

    def initialize_shap_explainer(self):
        """Initialize SHAP explainer for model interpretability"""
        try:
            import shap
            logger.info("SHAP library available, initializing explainer...")

            # Create a simple SHAP explainer for Random Forest
            if 'random_forest' in self.models:
                self.shap_explainer_rf = shap.TreeExplainer(self.models['random_forest'])
                logger.info("Random Forest SHAP explainer initialized")

            # Create SHAP explainer for XGBoost
            if 'xgboost' in self.models:
                self.shap_explainer_xgb = shap.TreeExplainer(self.models['xgboost'])
                logger.info("XGBoost SHAP explainer initialized")

            # Calculate SHAP values for a sample
            sample_size = min(500, len(self.X_test))
            sample_indices = np.random.choice(len(self.X_test), sample_size, replace=False)
            self.X_test_sample = self.X_test.iloc[sample_indices]

            # Calculate SHAP values for Random Forest
            if hasattr(self, 'shap_explainer_rf'):
                self.shap_values_rf = self.shap_explainer_rf.shap_values(self.X_test_sample)
                logger.info(f"Random Forest SHAP values calculated for {sample_size} samples")

            # Calculate SHAP values for XGBoost
            if hasattr(self, 'shap_explainer_xgb'):
                self.shap_values_xgb = self.shap_explainer_xgb.shap_values(self.X_test_sample)
                logger.info(f"XGBoost SHAP values calculated for {sample_size} samples")

            self.shap_available = True
            logger.info("SHAP explainer initialization completed successfully")

        except ImportError:
            logger.warning("SHAP not available. Install with: pip install shap")
            self.shap_available = False
        except Exception as e:
            logger.error(f"Error initializing SHAP explainer: {e}")
            self.shap_available = False

    def _train_dbscan_clustering(self, X_scaled: np.ndarray) -> None:
        """Train DBSCAN clustering for density-based grouping"""

        # Optimize DBSCAN parameters
        from sklearn.neighbors import NearestNeighbors

        # Find optimal eps using k-distance graph
        k = 5  # MinPts = k + 1 = 6
        nbrs = NearestNeighbors(n_neighbors=k).fit(X_scaled)
        distances, indices = nbrs.kneighbors(X_scaled)
        distances = np.sort(distances[:, k-1], axis=0)

        # Use knee point detection for eps (simplified approach)
        eps = np.percentile(distances, 90)  # Use 90th percentile as eps

        # Train DBSCAN
        dbscan = DBSCAN(eps=eps, min_samples=6, n_jobs=-1)
        dbscan_labels = dbscan.fit_predict(X_scaled)

        self.clustering_models['dbscan'] = dbscan

        # Store cluster assignments
        self.features_df['dbscan_cluster'] = dbscan_labels

        # Analyze DBSCAN results
        n_clusters = len(set(dbscan_labels)) - (1 if -1 in dbscan_labels else 0)
        n_noise = list(dbscan_labels).count(-1)

        logger.info(f"DBSCAN found {n_clusters} clusters and {n_noise} noise points")

        # Assign scores to DBSCAN clusters
        self._assign_dbscan_cluster_scores(dbscan_labels)

    def _train_enhanced_kmeans(self, X_scaled: np.ndarray) -> None:
        """Train enhanced K-means with optimized parameters"""

        # Determine optimal number of clusters using elbow method and silhouette score
        from sklearn.metrics import silhouette_score

        max_k = min(20, len(X_scaled) // 100)  # Reasonable upper bound
        inertias = []
        silhouette_scores = []
        k_range = range(2, max_k + 1)

        for k in k_range:
            kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
            kmeans.fit(X_scaled)
            inertias.append(kmeans.inertia_)
            silhouette_scores.append(silhouette_score(X_scaled, kmeans.labels_))

        # Find optimal k (highest silhouette score)
        optimal_k = k_range[np.argmax(silhouette_scores)]

        # Train final K-means model
        enhanced_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=20)
        enhanced_kmeans.fit(X_scaled)

        self.clustering_models['enhanced_kmeans'] = enhanced_kmeans
        self.features_df['enhanced_kmeans_cluster'] = enhanced_kmeans.labels_

        logger.info(f"Enhanced K-means with {optimal_k} clusters (silhouette score: {max(silhouette_scores):.3f})")

    def _train_anomaly_detectors(self, X_scaled: np.ndarray) -> None:
        """Train multiple anomaly detection models"""

        # 1. Enhanced Isolation Forest
        iso_forest_enhanced = IsolationForest(
            contamination=0.1,
            random_state=42,
            n_estimators=200,
            max_samples='auto',
            n_jobs=-1
        )
        iso_forest_enhanced.fit(X_scaled)
        self.anomaly_detectors['isolation_forest_enhanced'] = iso_forest_enhanced

        # 2. Local Outlier Factor
        lof = LocalOutlierFactor(
            n_neighbors=20,
            contamination=0.1,
            novelty=True,
            n_jobs=-1
        )
        lof.fit(X_scaled)
        self.anomaly_detectors['local_outlier_factor'] = lof

        # 3. One-Class SVM (for smaller datasets)
        if len(X_scaled) < 10000:  # Only for smaller datasets due to computational cost
            from sklearn.svm import OneClassSVM

            one_class_svm = OneClassSVM(
                kernel='rbf',
                gamma='scale',
                nu=0.1
            )
            one_class_svm.fit(X_scaled)
            self.anomaly_detectors['one_class_svm'] = one_class_svm

        # Store anomaly predictions
        self.features_df['anomaly_isolation_forest'] = iso_forest_enhanced.predict(X_scaled)
        self.features_df['anomaly_lof'] = lof.predict(X_scaled)

        if 'one_class_svm' in self.anomaly_detectors:
            self.features_df['anomaly_one_class_svm'] = self.anomaly_detectors['one_class_svm'].predict(X_scaled)

        # Create ensemble anomaly score
        anomaly_scores = []
        anomaly_scores.append((iso_forest_enhanced.predict(X_scaled) == -1).astype(int))
        anomaly_scores.append((lof.predict(X_scaled) == -1).astype(int))

        if 'one_class_svm' in self.anomaly_detectors:
            anomaly_scores.append((self.anomaly_detectors['one_class_svm'].predict(X_scaled) == -1).astype(int))

        # Ensemble anomaly score (majority vote)
        ensemble_anomaly = np.mean(anomaly_scores, axis=0)
        self.features_df['anomaly_ensemble_score'] = ensemble_anomaly
        self.features_df['is_anomaly'] = (ensemble_anomaly >= 0.5).astype(int)

        logger.info(f"Anomaly detection completed. Found {self.features_df['is_anomaly'].sum()} anomalous wallets")

    def _train_autoencoder(self, X_scaled: np.ndarray) -> None:
        """Train autoencoder for deep anomaly detection"""

        # Autoencoder architecture
        input_dim = X_scaled.shape[1]
        encoding_dim = max(8, input_dim // 4)  # Compression ratio of 4:1

        # Build autoencoder
        input_layer = keras.Input(shape=(input_dim,))

        # Encoder
        encoded = layers.Dense(encoding_dim * 2, activation='relu')(input_layer)
        encoded = layers.Dropout(0.2)(encoded)
        encoded = layers.Dense(encoding_dim, activation='relu')(encoded)

        # Decoder
        decoded = layers.Dense(encoding_dim * 2, activation='relu')(encoded)
        decoded = layers.Dropout(0.2)(decoded)
        decoded = layers.Dense(input_dim, activation='linear')(decoded)

        # Create and compile autoencoder
        autoencoder = keras.Model(input_layer, decoded)
        autoencoder.compile(optimizer='adam', loss='mse', metrics=['mae'])

        # Train autoencoder
        history = autoencoder.fit(
            X_scaled, X_scaled,
            epochs=100,
            batch_size=32,
            validation_split=0.2,
            verbose=0,
            callbacks=[
                keras.callbacks.EarlyStopping(patience=10, restore_best_weights=True),
                keras.callbacks.ReduceLROnPlateau(patience=5, factor=0.5)
            ]
        )

        self.autoencoder = autoencoder

        # Calculate reconstruction errors
        reconstructed = autoencoder.predict(X_scaled, verbose=0)
        reconstruction_errors = np.mean(np.square(X_scaled - reconstructed), axis=1)

        # Determine anomaly threshold (95th percentile)
        threshold = np.percentile(reconstruction_errors, 95)

        # Store results
        self.features_df['autoencoder_reconstruction_error'] = reconstruction_errors
        self.features_df['anomaly_autoencoder'] = (reconstruction_errors > threshold).astype(int)

        logger.info(f"Autoencoder trained. Anomaly threshold: {threshold:.4f}")
        logger.info(f"Found {self.features_df['anomaly_autoencoder'].sum()} anomalies using autoencoder")

    def _assign_dbscan_cluster_scores(self, dbscan_labels: np.ndarray) -> None:
        """Assign initial NFCS scores based on DBSCAN cluster characteristics"""

        cluster_scores = {}

        for cluster_id in set(dbscan_labels):
            if cluster_id == -1:  # Noise points
                cluster_scores[cluster_id] = 200  # Low score for noise/outliers
                continue

            # Get cluster members
            cluster_mask = dbscan_labels == cluster_id
            cluster_data = self.features_df[cluster_mask]

            # Calculate cluster characteristics
            avg_repayment_ratio = cluster_data['repayment_ratio'].mean()
            avg_liquidation_rate = cluster_data['has_been_liquidated'].mean()
            avg_borrow_amount = cluster_data['totalAmountOfBorrowInUSD'].mean()

            # Assign score based on cluster quality
            if avg_repayment_ratio > 0.8 and avg_liquidation_rate < 0.1:
                cluster_score = 600  # Good cluster
            elif avg_repayment_ratio > 0.5 and avg_liquidation_rate < 0.3:
                cluster_score = 400  # Medium cluster
            else:
                cluster_score = 250  # Poor cluster

            cluster_scores[cluster_id] = cluster_score

        # Assign scores to wallets without loan history
        no_loan_history = (self.features_df['totalAmountOfBorrowInUSD'] == 0)

        for idx, row in self.features_df[no_loan_history].iterrows():
            cluster_id = row['dbscan_cluster']
            self.features_df.loc[idx, 'dbscan_initial_score'] = cluster_scores.get(cluster_id, 300)

        logger.info(f"Assigned initial scores to {no_loan_history.sum()} wallets without loan history")

    def _analyze_cluster_characteristics(self) -> None:
        """Analyze characteristics of different clusters"""

        # Analyze enhanced K-means clusters
        if 'enhanced_kmeans_cluster' in self.features_df.columns:
            for cluster_id in self.features_df['enhanced_kmeans_cluster'].unique():
                cluster_data = self.features_df[self.features_df['enhanced_kmeans_cluster'] == cluster_id]

                characteristics = {
                    'size': len(cluster_data),
                    'avg_nfcs': cluster_data['nfcs_score'].mean(),
                    'avg_repayment_ratio': cluster_data['repayment_ratio'].mean(),
                    'liquidation_rate': cluster_data['has_been_liquidated'].mean(),
                    'avg_borrow_amount': cluster_data['totalAmountOfBorrowInUSD'].mean(),
                    'risk_distribution': cluster_data['credit_risk'].value_counts().to_dict()
                }

                self.cluster_characteristics[f'kmeans_cluster_{cluster_id}'] = characteristics

        # Analyze DBSCAN clusters
        if 'dbscan_cluster' in self.features_df.columns:
            for cluster_id in self.features_df['dbscan_cluster'].unique():
                if cluster_id == -1:
                    continue  # Skip noise points

                cluster_data = self.features_df[self.features_df['dbscan_cluster'] == cluster_id]

                characteristics = {
                    'size': len(cluster_data),
                    'avg_nfcs': cluster_data['nfcs_score'].mean(),
                    'avg_repayment_ratio': cluster_data['repayment_ratio'].mean(),
                    'liquidation_rate': cluster_data['has_been_liquidated'].mean(),
                    'avg_borrow_amount': cluster_data['totalAmountOfBorrowInUSD'].mean(),
                    'density': len(cluster_data) / len(self.features_df)
                }

                self.cluster_characteristics[f'dbscan_cluster_{cluster_id}'] = characteristics

    def get_cluster_based_score(self, wallet_address: str) -> Dict:
        """
        Get cluster-based score for wallets without loan history

        Args:
            wallet_address: Wallet address to score

        Returns:
            Dictionary with cluster-based scoring information
        """
        if wallet_address not in self.features_df['walletAddress'].values:
            return {'error': 'Wallet address not found in dataset'}

        wallet_data = self.features_df[self.features_df['walletAddress'] == wallet_address].iloc[0]

        # Check if wallet has loan history
        has_loan_history = wallet_data['totalAmountOfBorrowInUSD'] > 0

        if has_loan_history:
            return {
                'wallet_address': wallet_address,
                'has_loan_history': True,
                'message': 'Wallet has loan history, use regular NFCS scoring'
            }

        # Get cluster assignments
        result = {
            'wallet_address': wallet_address,
            'has_loan_history': False,
            'cluster_assignments': {}
        }

        # K-means cluster assignment
        if 'enhanced_kmeans_cluster' in wallet_data:
            kmeans_cluster = wallet_data['enhanced_kmeans_cluster']
            cluster_key = f'kmeans_cluster_{kmeans_cluster}'

            if cluster_key in self.cluster_characteristics:
                cluster_info = self.cluster_characteristics[cluster_key]
                result['cluster_assignments']['kmeans'] = {
                    'cluster_id': int(kmeans_cluster),
                    'cluster_size': cluster_info['size'],
                    'avg_nfcs': cluster_info['avg_nfcs'],
                    'suggested_initial_score': min(max(cluster_info['avg_nfcs'], 200), 600)
                }

        # DBSCAN cluster assignment
        if 'dbscan_cluster' in wallet_data:
            dbscan_cluster = wallet_data['dbscan_cluster']

            if dbscan_cluster == -1:
                result['cluster_assignments']['dbscan'] = {
                    'cluster_id': -1,
                    'cluster_type': 'noise/outlier',
                    'suggested_initial_score': 200
                }
            else:
                cluster_key = f'dbscan_cluster_{dbscan_cluster}'
                if cluster_key in self.cluster_characteristics:
                    cluster_info = self.cluster_characteristics[cluster_key]
                    result['cluster_assignments']['dbscan'] = {
                        'cluster_id': int(dbscan_cluster),
                        'cluster_size': cluster_info['size'],
                        'density': cluster_info['density'],
                        'avg_nfcs': cluster_info['avg_nfcs'],
                        'suggested_initial_score': min(max(cluster_info['avg_nfcs'], 200), 600)
                    }

        # Get initial score from DBSCAN if available
        if 'dbscan_initial_score' in wallet_data:
            result['dbscan_initial_score'] = wallet_data['dbscan_initial_score']

        return result

    def get_advanced_anomaly_analysis(self, wallet_address: str) -> Dict:
        """
        Get comprehensive anomaly analysis for a wallet

        Args:
            wallet_address: Wallet address to analyze

        Returns:
            Dictionary with anomaly analysis results
        """
        if wallet_address not in self.features_df['walletAddress'].values:
            return {'error': 'Wallet address not found in dataset'}

        wallet_data = self.features_df[self.features_df['walletAddress'] == wallet_address].iloc[0]

        result = {
            'wallet_address': wallet_address,
            'anomaly_analysis': {}
        }

        # Isolation Forest (Enhanced)
        if 'anomaly_isolation_forest' in wallet_data:
            result['anomaly_analysis']['isolation_forest'] = {
                'is_anomaly': wallet_data['anomaly_isolation_forest'] == -1,
                'method': 'Enhanced Isolation Forest'
            }

        # Local Outlier Factor
        if 'anomaly_lof' in wallet_data:
            result['anomaly_analysis']['local_outlier_factor'] = {
                'is_anomaly': wallet_data['anomaly_lof'] == -1,
                'method': 'Local Outlier Factor'
            }

        # One-Class SVM (if available)
        if 'anomaly_one_class_svm' in wallet_data:
            result['anomaly_analysis']['one_class_svm'] = {
                'is_anomaly': wallet_data['anomaly_one_class_svm'] == -1,
                'method': 'One-Class SVM'
            }

        # Autoencoder (if available)
        if 'anomaly_autoencoder' in wallet_data:
            result['anomaly_analysis']['autoencoder'] = {
                'is_anomaly': wallet_data['anomaly_autoencoder'] == 1,
                'reconstruction_error': wallet_data['autoencoder_reconstruction_error'],
                'method': 'Deep Autoencoder'
            }

        # Ensemble anomaly score
        if 'anomaly_ensemble_score' in wallet_data:
            result['anomaly_analysis']['ensemble'] = {
                'anomaly_score': wallet_data['anomaly_ensemble_score'],
                'is_anomaly': wallet_data['is_anomaly'] == 1,
                'confidence': wallet_data['anomaly_ensemble_score'],
                'method': 'Ensemble (Majority Vote)'
            }

        # Risk assessment based on anomaly detection
        if 'is_anomaly' in wallet_data and wallet_data['is_anomaly'] == 1:
            result['risk_assessment'] = {
                'risk_level': 'High',
                'reason': 'Flagged as anomalous by multiple detection methods',
                'recommended_action': 'Require additional verification or higher collateral'
            }
        else:
            result['risk_assessment'] = {
                'risk_level': 'Normal',
                'reason': 'No anomalous behavior detected',
                'recommended_action': 'Standard processing'
            }

        return result

    def _get_comprehensive_anomaly_detection(self, feature_vector_scaled: np.ndarray, wallet_data: pd.Series) -> Dict:
        """Get comprehensive anomaly detection results"""
        anomaly_results = {}

        # Traditional Isolation Forest
        if 'isolation_forest' in self.models:
            anomaly_score = self.models['isolation_forest'].decision_function(feature_vector_scaled)[0]
            is_anomaly = self.models['isolation_forest'].predict(feature_vector_scaled)[0] == -1
            anomaly_results['isolation_forest'] = {
                'is_anomaly': is_anomaly,
                'anomaly_score': anomaly_score
            }

        # Enhanced Isolation Forest
        if 'isolation_forest_enhanced' in self.anomaly_detectors:
            enhanced_anomaly = self.anomaly_detectors['isolation_forest_enhanced'].predict(feature_vector_scaled)[0] == -1
            anomaly_results['isolation_forest_enhanced'] = {
                'is_anomaly': enhanced_anomaly
            }

        # Local Outlier Factor
        if 'local_outlier_factor' in self.anomaly_detectors:
            lof_anomaly = self.anomaly_detectors['local_outlier_factor'].predict(feature_vector_scaled)[0] == -1
            anomaly_results['local_outlier_factor'] = {
                'is_anomaly': lof_anomaly
            }

        # One-Class SVM
        if 'one_class_svm' in self.anomaly_detectors:
            svm_anomaly = self.anomaly_detectors['one_class_svm'].predict(feature_vector_scaled)[0] == -1
            anomaly_results['one_class_svm'] = {
                'is_anomaly': svm_anomaly
            }

        # Autoencoder
        if self.autoencoder is not None:
            reconstructed = self.autoencoder.predict(feature_vector_scaled, verbose=0)
            reconstruction_error = np.mean(np.square(feature_vector_scaled - reconstructed))

            # Use stored threshold or calculate on-the-fly
            if 'autoencoder_reconstruction_error' in self.features_df.columns:
                threshold = np.percentile(self.features_df['autoencoder_reconstruction_error'], 95)
            else:
                threshold = 0.1  # Default threshold

            autoencoder_anomaly = reconstruction_error > threshold
            anomaly_results['autoencoder'] = {
                'is_anomaly': autoencoder_anomaly,
                'reconstruction_error': reconstruction_error,
                'threshold': threshold
            }

        # Ensemble result from stored data
        if 'is_anomaly' in wallet_data:
            anomaly_results['ensemble'] = {
                'is_anomaly': wallet_data['is_anomaly'] == 1,
                'ensemble_score': wallet_data.get('anomaly_ensemble_score', 0)
            }

        return anomaly_results

    def _get_comprehensive_clustering(self, feature_vector_scaled: np.ndarray, wallet_data: pd.Series) -> Dict:
        """Get comprehensive clustering results"""
        clustering_results = {}

        # Traditional K-means
        if 'kmeans' in self.models:
            cluster = self.models['kmeans'].predict(feature_vector_scaled)[0]
            clustering_results['kmeans'] = {
                'cluster_id': int(cluster)
            }

        # Enhanced K-means
        if 'enhanced_kmeans' in self.clustering_models:
            enhanced_cluster = self.clustering_models['enhanced_kmeans'].predict(feature_vector_scaled)[0]
            clustering_results['enhanced_kmeans'] = {
                'cluster_id': int(enhanced_cluster)
            }

            # Add cluster characteristics if available
            cluster_key = f'kmeans_cluster_{enhanced_cluster}'
            if cluster_key in self.cluster_characteristics:
                clustering_results['enhanced_kmeans']['characteristics'] = self.cluster_characteristics[cluster_key]

        # DBSCAN (from stored data since DBSCAN doesn't predict new points easily)
        if 'dbscan_cluster' in wallet_data:
            dbscan_cluster = wallet_data['dbscan_cluster']
            clustering_results['dbscan'] = {
                'cluster_id': int(dbscan_cluster),
                'is_noise': dbscan_cluster == -1
            }

            if dbscan_cluster != -1:
                cluster_key = f'dbscan_cluster_{dbscan_cluster}'
                if cluster_key in self.cluster_characteristics:
                    clustering_results['dbscan']['characteristics'] = self.cluster_characteristics[cluster_key]

        return clustering_results

    def evaluate_models(self) -> Dict:
        """Evaluate trained models"""
        logger.info("Evaluating models...")

        evaluation_results = {}

        # Evaluate Random Forest
        rf_pred = self.models['random_forest'].predict(self.X_test)
        rf_prob = self.models['random_forest'].predict_proba(self.X_test)

        evaluation_results['random_forest'] = {
            'classification_report': classification_report(self.y_test_multi, rf_pred),
            'confusion_matrix': confusion_matrix(self.y_test_multi, rf_pred),
            'accuracy': (rf_pred == self.y_test_multi).mean()
        }

        # Evaluate XGBoost
        xgb_pred = self.models['xgboost'].predict(self.X_test)
        xgb_prob = self.models['xgboost'].predict_proba(self.X_test)

        evaluation_results['xgboost'] = {
            'classification_report': classification_report(self.y_test_multi, xgb_pred),
            'confusion_matrix': confusion_matrix(self.y_test_multi, xgb_pred),
            'accuracy': (xgb_pred == self.y_test_multi).mean()
        }

        # Anomaly detection evaluation
        anomaly_pred = self.models['isolation_forest'].predict(self.X_test)
        anomaly_scores = self.models['isolation_forest'].decision_function(self.X_test)

        evaluation_results['isolation_forest'] = {
            'anomaly_ratio': (anomaly_pred == -1).mean(),
            'anomaly_scores_stats': {
                'mean': anomaly_scores.mean(),
                'std': anomaly_scores.std(),
                'min': anomaly_scores.min(),
                'max': anomaly_scores.max()
            }
        }

        # Clustering evaluation
        cluster_labels = self.models['kmeans'].predict(self.X_test)
        evaluation_results['kmeans'] = {
            'cluster_distribution': pd.Series(cluster_labels).value_counts().to_dict(),
            'inertia': self.models['kmeans'].inertia_
        }

        self.evaluation_results = evaluation_results
        logger.info("Model evaluation completed")

        return evaluation_results

    def setup_shap_explainers(self) -> None:
        """
        Setup SHAP explainers for model interpretability
        """
        logger.info("Setting up SHAP explainers...")

        # Sample data for SHAP (use subset for performance)
        sample_size = min(1000, len(self.X_train))
        X_sample = self.X_train.sample(n=sample_size, random_state=42)

        # Random Forest SHAP explainer
        logger.info("Creating SHAP explainer for Random Forest...")
        self.shap_explainers['random_forest'] = shap.TreeExplainer(self.models['random_forest'])
        self.shap_values['random_forest'] = self.shap_explainers['random_forest'].shap_values(X_sample)

        # XGBoost SHAP explainer
        logger.info("Creating SHAP explainer for XGBoost...")
        self.shap_explainers['xgboost'] = shap.TreeExplainer(self.models['xgboost'])
        self.shap_values['xgboost'] = self.shap_explainers['xgboost'].shap_values(X_sample)

        # Store sample data for plotting
        self.X_shap_sample = X_sample

        logger.info("SHAP explainers setup completed")

    def plot_shap_analysis(self) -> None:
        """
        Create comprehensive SHAP analysis plots
        """
        logger.info("Creating SHAP analysis plots...")

        if not self.shap_explainers:
            self.setup_shap_explainers()

        # SHAP Summary plots
        self._plot_shap_summary()

        # SHAP Feature importance
        self._plot_shap_feature_importance()

        # SHAP Waterfall plots for sample predictions
        self._plot_shap_waterfall()

        # SHAP Dependence plots
        self._plot_shap_dependence()

        logger.info("SHAP analysis plots completed")

    def _plot_shap_summary(self) -> None:
        """Plot SHAP summary plots"""
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        fig.suptitle('SHAP Summary Analysis', fontsize=16, fontweight='bold')

        # Random Forest SHAP summary
        plt.sca(axes[0])
        if len(self.shap_values['random_forest'].shape) == 3:  # Multi-class
            shap.summary_plot(self.shap_values['random_forest'][:,:,1],
                            self.X_shap_sample,
                            feature_names=self.feature_cols,
                            show=False, max_display=15)
        else:  # Binary or single output
            shap.summary_plot(self.shap_values['random_forest'],
                            self.X_shap_sample,
                            feature_names=self.feature_cols,
                            show=False, max_display=15)
        axes[0].set_title('Random Forest - SHAP Summary')

        # XGBoost SHAP summary
        plt.sca(axes[1])
        if len(self.shap_values['xgboost'].shape) == 3:  # Multi-class
            shap.summary_plot(self.shap_values['xgboost'][:,:,1],
                            self.X_shap_sample,
                            feature_names=self.feature_cols,
                            show=False, max_display=15)
        else:  # Binary or single output
            shap.summary_plot(self.shap_values['xgboost'],
                            self.X_shap_sample,
                            feature_names=self.feature_cols,
                            show=False, max_display=15)
        axes[1].set_title('XGBoost - SHAP Summary')

        plt.tight_layout()
        plt.savefig('shap_summary_plots.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_shap_feature_importance(self) -> None:
        """Plot SHAP-based feature importance"""
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        fig.suptitle('SHAP Feature Importance', fontsize=16, fontweight='bold')

        # Random Forest SHAP feature importance
        plt.sca(axes[0])
        if len(self.shap_values['random_forest'].shape) == 3:  # Multi-class
            shap_vals = self.shap_values['random_forest'][:,:,1]
        else:
            shap_vals = self.shap_values['random_forest']

        shap.summary_plot(shap_vals, self.X_shap_sample,
                         feature_names=self.feature_cols,
                         plot_type="bar", show=False, max_display=15)
        axes[0].set_title('Random Forest - SHAP Feature Importance')

        # XGBoost SHAP feature importance
        plt.sca(axes[1])
        if len(self.shap_values['xgboost'].shape) == 3:  # Multi-class
            shap_vals = self.shap_values['xgboost'][:,:,1]
        else:
            shap_vals = self.shap_values['xgboost']

        shap.summary_plot(shap_vals, self.X_shap_sample,
                         feature_names=self.feature_cols,
                         plot_type="bar", show=False, max_display=15)
        axes[1].set_title('XGBoost - SHAP Feature Importance')

        plt.tight_layout()
        plt.savefig('shap_feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_shap_waterfall(self) -> None:
        """Plot SHAP waterfall plots for sample predictions"""
        fig, axes = plt.subplots(2, 2, figsize=(20, 16))
        fig.suptitle('SHAP Waterfall Plots - Sample Predictions', fontsize=16, fontweight='bold')

        # Select sample instances for different risk levels
        sample_indices = []
        for risk_level in [0, 1, 2]:  # Low, Medium, High risk
            risk_samples = self.X_shap_sample.index[
                self.features_df.loc[self.X_shap_sample.index, 'credit_risk'] == risk_level
            ]
            if len(risk_samples) > 0:
                sample_indices.append(risk_samples[0])

        # Add one more random sample
        if len(sample_indices) < 4:
            remaining_samples = [idx for idx in self.X_shap_sample.index if idx not in sample_indices]
            if remaining_samples:
                sample_indices.append(remaining_samples[0])

        # Plot waterfall for each sample
        for i, idx in enumerate(sample_indices[:4]):
            row, col = i // 2, i % 2
            plt.sca(axes[row, col])

            # Get the position in the sample data
            sample_pos = self.X_shap_sample.index.get_loc(idx)

            # Create waterfall plot
            if len(self.shap_values['random_forest'].shape) == 3:  # Multi-class
                shap_vals = self.shap_values['random_forest'][sample_pos, :, 1]
            else:
                shap_vals = self.shap_values['random_forest'][sample_pos, :]

            # Create explanation object
            explanation = shap.Explanation(
                values=shap_vals,
                base_values=self.shap_explainers['random_forest'].expected_value[1] if len(self.shap_values['random_forest'].shape) == 3 else self.shap_explainers['random_forest'].expected_value,
                data=self.X_shap_sample.iloc[sample_pos].values,
                feature_names=self.feature_cols
            )

            shap.waterfall_plot(explanation, max_display=10, show=False)
            risk_level = self.features_df.loc[idx, 'credit_risk']
            nfcs_score = self.features_df.loc[idx, 'nfcs_score']
            axes[row, col].set_title(f'Wallet Risk: {risk_level}, NFCS: {nfcs_score:.1f}')

        plt.tight_layout()
        plt.savefig('shap_waterfall_plots.png', dpi=300, bbox_inches='tight')
        plt.show()

    def _plot_shap_dependence(self) -> None:
        """Plot SHAP dependence plots for key features"""
        key_features = ['nfcs_score', 'repayment_ratio', 'repayment_consistency',
                       'totalAmountOfBorrowInUSD', 'deposit_to_borrow_ratio']

        # Filter features that exist in our dataset
        available_features = [f for f in key_features if f in self.feature_cols]

        if len(available_features) >= 4:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('SHAP Dependence Plots - Key Features', fontsize=16, fontweight='bold')

            for i, feature in enumerate(available_features[:4]):
                row, col = i // 2, i % 2
                plt.sca(axes[row, col])

                feature_idx = self.feature_cols.index(feature)

                if len(self.shap_values['random_forest'].shape) == 3:  # Multi-class
                    shap_vals = self.shap_values['random_forest'][:, feature_idx, 1]
                else:
                    shap_vals = self.shap_values['random_forest'][:, feature_idx]

                shap.dependence_plot(feature_idx, shap_vals, self.X_shap_sample,
                                   feature_names=self.feature_cols, show=False)
                axes[row, col].set_title(f'SHAP Dependence: {feature}')

            plt.tight_layout()
            plt.savefig('shap_dependence_plots.png', dpi=300, bbox_inches='tight')
            plt.show()

    def get_shap_explanation(self, wallet_address: str, model_name: str = 'random_forest') -> Dict:
        """
        Get SHAP explanation for a specific wallet prediction

        Args:
            wallet_address: Wallet address to explain
            model_name: Model to use for explanation ('random_forest' or 'xgboost')

        Returns:
            Dictionary containing SHAP values and explanation
        """
        if wallet_address not in self.features_df['walletAddress'].values:
            return {'error': 'Wallet address not found in dataset'}

        if model_name not in self.shap_explainers:
            logger.info(f"Setting up SHAP explainer for {model_name}...")
            self.setup_shap_explainers()

        # Get wallet data
        wallet_data = self.features_df[self.features_df['walletAddress'] == wallet_address].iloc[0]
        feature_vector = wallet_data[self.feature_cols].fillna(0).values.reshape(1, -1)
        feature_vector_scaled = self.scaler.transform(feature_vector)
        feature_vector_df = pd.DataFrame(feature_vector_scaled, columns=self.feature_cols)

        # Get SHAP values
        explainer = self.shap_explainers[model_name]
        shap_values = explainer.shap_values(feature_vector_df)

        # Handle multi-class output
        if len(shap_values.shape) == 3:  # Multi-class
            shap_values_class = shap_values[0, :, 1]  # Use class 1 (medium risk)
            expected_value = explainer.expected_value[1]
        else:
            shap_values_class = shap_values[0, :]
            expected_value = explainer.expected_value

        # Create feature contribution analysis
        feature_contributions = []
        for i, (feature, shap_val) in enumerate(zip(self.feature_cols, shap_values_class)):
            feature_contributions.append({
                'feature': feature,
                'shap_value': float(shap_val),
                'feature_value': float(feature_vector_scaled[0, i]),
                'original_value': float(wallet_data[feature]) if feature in wallet_data else 0,
                'contribution_type': 'positive' if shap_val > 0 else 'negative',
                'abs_contribution': abs(float(shap_val))
            })

        # Sort by absolute contribution
        feature_contributions.sort(key=lambda x: x['abs_contribution'], reverse=True)

        # Get model prediction
        model_pred = self.models[model_name].predict(feature_vector_scaled)[0]
        model_prob = self.models[model_name].predict_proba(feature_vector_scaled)[0]

        return {
            'wallet_address': wallet_address,
            'model_name': model_name,
            'prediction': {
                'risk_class': int(model_pred),
                'risk_level': ['Low', 'Medium', 'High'][model_pred],
                'probabilities': model_prob.tolist()
            },
            'shap_explanation': {
                'expected_value': float(expected_value),
                'prediction_value': float(expected_value + sum(shap_values_class)),
                'total_shap_contribution': float(sum(shap_values_class))
            },
            'top_positive_features': [f for f in feature_contributions if f['contribution_type'] == 'positive'][:10],
            'top_negative_features': [f for f in feature_contributions if f['contribution_type'] == 'negative'][:10],
            'all_contributions': feature_contributions
        }

    def _plot_feature_importance(self) -> None:
        """Plot feature importance for tree-based models"""
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        fig.suptitle('Feature Importance Analysis', fontsize=16, fontweight='bold')

        # Random Forest feature importance
        rf_importance = pd.DataFrame(list(self.feature_importance['random_forest'].items()),
                                   columns=['feature', 'importance']).sort_values('importance', ascending=True)

        rf_importance.tail(15).plot(x='feature', y='importance', kind='barh', ax=axes[0])
        axes[0].set_title('Random Forest - Top 15 Features')
        axes[0].set_xlabel('Importance')

        # XGBoost feature importance
        xgb_importance = pd.DataFrame(list(self.feature_importance['xgboost'].items()),
                                    columns=['feature', 'importance']).sort_values('importance', ascending=True)

        xgb_importance.tail(15).plot(x='feature', y='importance', kind='barh', ax=axes[1])
        axes[1].set_title('XGBoost - Top 15 Features')
        axes[1].set_xlabel('Importance')

        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()

    def calculate_nfcs_score(self, wallet_address: str) -> Dict:
        """
        Calculate NFCS (Non-Fungible Credit Score) for a specific wallet
        Returns comprehensive scoring information
        """
        if wallet_address not in self.features_df['walletAddress'].values:
            return {'error': 'Wallet address not found in dataset'}

        wallet_data = self.features_df[self.features_df['walletAddress'] == wallet_address].iloc[0]

        # Get model predictions
        feature_vector = wallet_data[self.feature_cols].fillna(0).values.reshape(1, -1)
        feature_vector_scaled = self.scaler.transform(feature_vector)

        # Random Forest prediction
        rf_risk_pred = self.models['random_forest'].predict(feature_vector_scaled)[0]
        rf_risk_prob = self.models['random_forest'].predict_proba(feature_vector_scaled)[0]

        # XGBoost prediction
        xgb_risk_pred = self.models['xgboost'].predict(feature_vector_scaled)[0]
        xgb_risk_prob = self.models['xgboost'].predict_proba(feature_vector_scaled)[0]

        # Advanced Anomaly detection
        anomaly_results = self._get_comprehensive_anomaly_detection(feature_vector_scaled, wallet_data)

        # Cluster assignments
        cluster_results = self._get_comprehensive_clustering(feature_vector_scaled, wallet_data)

        # Ensemble prediction (weighted average)
        ensemble_prob = (rf_risk_prob + xgb_risk_prob) / 2
        ensemble_risk = np.argmax(ensemble_prob)

        # Calculate final NFCS score (0-1000 scale)
        base_nfcs = wallet_data['nfcs_score']

        # Adjust based on model predictions
        risk_adjustment = {0: 1.0, 1: 0.7, 2: 0.3}  # Low, Medium, High risk
        model_adjusted_nfcs = base_nfcs * risk_adjustment[ensemble_risk]

        # Anomaly penalty
        is_anomaly = anomaly_results.get('ensemble', {}).get('is_anomaly', False)
        if is_anomaly:
            model_adjusted_nfcs *= 0.5

        # Final score
        final_nfcs = max(0, min(1000, model_adjusted_nfcs))

        return {
            'wallet_address': wallet_address,
            'nfcs_score': final_nfcs,
            'risk_level': ['Low', 'Medium', 'High'][ensemble_risk],
            'enhanced_credit_rating': self._get_enhanced_credit_rating(final_nfcs),
            'risk_probabilities': {
                'low': ensemble_prob[0],
                'medium': ensemble_prob[1],
                'high': ensemble_prob[2]
            },
            'model_predictions': {
                'random_forest': {
                    'risk_level': ['Low', 'Medium', 'High'][rf_risk_pred],
                    'probabilities': rf_risk_prob.tolist()
                },
                'xgboost': {
                    'risk_level': ['Low', 'Medium', 'High'][xgb_risk_pred],
                    'probabilities': xgb_risk_prob.tolist()
                }
            },
            'advanced_anomaly_detection': anomaly_results,
            'clustering_analysis': cluster_results,
            'shap_explanation': self._get_shap_explanation(wallet_address),
            'key_metrics': {
                'repayment_ratio': wallet_data['repayment_ratio'],
                'repayment_consistency': wallet_data['repayment_consistency'],
                'deposit_to_borrow_ratio': wallet_data['deposit_to_borrow_ratio'],
                'protocol_diversity_score': wallet_data['protocol_diversity_score'],
                'has_been_liquidated': bool(wallet_data['has_been_liquidated']),
                'total_borrow_amount': wallet_data['totalAmountOfBorrowInUSD'],
                'total_repay_amount': wallet_data['totalAmountOfRepayInUSD']
            }
        }

    def _get_enhanced_credit_rating(self, nfcs_score):
        """
        Get enhanced credit rating based on NFCS score

        Args:
            nfcs_score: NFCS score (0-1000)

        Returns:
            Dictionary with enhanced credit rating information
        """
        if nfcs_score >= 900:
            return {
                'rating': 'AAA',
                'description': 'Excellent',
                'detail': 'Sophisticated cross-chain DeFi users',
                'score_range': '[900, 1000]',
                'risk_level': 'Very Low',
                'lending_recommendation': 'Eligible for undercollateralized loans with premium rates'
            }
        elif nfcs_score >= 800:
            return {
                'rating': 'AA',
                'description': 'Very Good',
                'detail': 'Active multi-chain users with good repayment',
                'score_range': '[800, 899]',
                'risk_level': 'Low',
                'lending_recommendation': 'Eligible for undercollateralized loans with standard rates'
            }
        elif nfcs_score >= 700:
            return {
                'rating': 'A',
                'description': 'Good',
                'detail': 'Consistent DeFi users with smart contract activity',
                'score_range': '[700, 799]',
                'risk_level': 'Low',
                'lending_recommendation': 'Eligible for undercollateralized loans with monitoring'
            }
        elif nfcs_score >= 600:
            return {
                'rating': 'BBB',
                'description': 'Fair',
                'detail': 'Regular DeFi users with moderate SC activity',
                'score_range': '[600, 699]',
                'risk_level': 'Medium',
                'lending_recommendation': 'Partial collateral required (50-75%)'
            }
        elif nfcs_score >= 500:
            return {
                'rating': 'BB',
                'description': 'Poor',
                'detail': 'Limited activity or poor repayment history',
                'score_range': '[500, 599]',
                'risk_level': 'Medium-High',
                'lending_recommendation': 'Higher collateral required (75-100%)'
            }
        elif nfcs_score >= 400:
            return {
                'rating': 'B',
                'description': 'Very Poor',
                'detail': 'Minimal activity or significant risks',
                'score_range': '[400, 499]',
                'risk_level': 'High',
                'lending_recommendation': 'Full collateral required (100%+)'
            }
        else:
            return {
                'rating': 'C',
                'description': 'Unacceptable',
                'detail': 'High risk or dormant wallets',
                'score_range': '[0, 399]',
                'risk_level': 'Very High',
                'lending_recommendation': 'Lending not recommended'
            }

    def _get_shap_explanation(self, wallet_address):
        """
        Get SHAP explanation for individual wallet prediction

        Args:
            wallet_address: Wallet address to explain

        Returns:
            Dictionary with SHAP explanation or None if not available
        """
        if not hasattr(self, 'shap_available') or not self.shap_available:
            return None

        try:
            # Get wallet data
            wallet_data = self.features_df[self.features_df['walletAddress'] == wallet_address]
            if wallet_data.empty:
                return None

            # Prepare features
            wallet_features = wallet_data[self.feature_cols].fillna(0)

            # Get SHAP values for Random Forest
            if hasattr(self, 'shap_explainer_rf'):
                shap_values = self.shap_explainer_rf.shap_values(wallet_features)

                # Handle multi-class case (take high risk class)
                if isinstance(shap_values, list):
                    shap_values_high_risk = shap_values[2]  # High risk class
                else:
                    shap_values_high_risk = shap_values

                # Create feature contributions
                feature_contributions = []
                for i, feature in enumerate(self.feature_cols):
                    feature_contributions.append({
                        'feature': feature,
                        'shap_value': float(shap_values_high_risk[0][i]),
                        'feature_value': float(wallet_features.iloc[0, i])
                    })

                # Sort by absolute SHAP value
                feature_contributions.sort(key=lambda x: abs(x['shap_value']), reverse=True)

                # Split into positive and negative contributors
                positive_contributors = [f for f in feature_contributions if f['shap_value'] > 0][:5]
                negative_contributors = [f for f in feature_contributions if f['shap_value'] < 0][:5]

                return {
                    'top_features': feature_contributions[:10],
                    'feature_contributions': {
                        'positive': positive_contributors,
                        'negative': negative_contributors
                    },
                    'prediction_explanation': f"The model prediction is primarily driven by {feature_contributions[0]['feature']} with a SHAP value of {feature_contributions[0]['shap_value']:.4f}"
                }

            return None

        except Exception as e:
            logger.warning(f"Could not generate SHAP explanation for {wallet_address}: {e}")
            return None

    def run_full_pipeline(self) -> None:
        """Run the complete ML pipeline"""
        logger.info("Starting full DeFi Credit Scoring Pipeline...")

        # 1. Load and preprocess data
        self.load_data()
        self.preprocess_data()

        # 2. Feature engineering
        self.engineer_features()
        self.create_target_variable()

        # 3. Data visualization
        self.visualize_data()

        # 4. Train models
        self.train_models()

        # 5. Evaluate models
        evaluation_results = self.evaluate_models()

        # 6. Setup SHAP explainers and create analysis
        self.setup_shap_explainers()
        self.plot_shap_analysis()

        # 7. Print summary
        self._print_pipeline_summary(evaluation_results)

        logger.info("Pipeline completed successfully!")

    def _print_pipeline_summary(self, evaluation_results: Dict) -> None:
        """Print a summary of the pipeline results"""
        print("\n" + "="*80)
        print("DEFI CREDIT SCORING PIPELINE SUMMARY")
        print("="*80)

        print(f"\nDataset Summary:")
        print(f"- Total wallets analyzed: {len(self.features_df)}")
        print(f"- Features engineered: {len(self.feature_cols)}")
        print(f"- Risk distribution: {dict(self.features_df['credit_risk'].value_counts())}")

        print(f"\nModel Performance:")
        for model_name, results in evaluation_results.items():
            if 'accuracy' in results:
                print(f"- {model_name.title()}: {results['accuracy']:.3f} accuracy")

        print(f"\nTop 5 Most Important Features:")
        if 'random_forest' in self.feature_importance:
            top_features = sorted(self.feature_importance['random_forest'].items(),
                                key=lambda x: x[1], reverse=True)[:5]
            for i, (feature, importance) in enumerate(top_features, 1):
                print(f"{i}. {feature}: {importance:.3f}")

        print(f"\nNFCS Score Statistics:")
        print(f"- Mean NFCS: {self.features_df['nfcs_score'].mean():.2f}")
        print(f"- Median NFCS: {self.features_df['nfcs_score'].median():.2f}")
        print(f"- Std NFCS: {self.features_df['nfcs_score'].std():.2f}")

        print("\n" + "="*80)

# Example usage and testing
if __name__ == "__main__":
    # Initialize pipeline
    pipeline = DeFiCreditScoringPipeline(".")

    # Run full pipeline
    pipeline.run_full_pipeline()

    # Example: Calculate NFCS for a specific wallet
    # Replace with an actual wallet address from your data
    sample_wallets = pipeline.features_df['walletAddress'].head(3).tolist()

    print("\nSample NFCS Calculations:")
    print("-" * 50)
    for wallet in sample_wallets:
        score_info = pipeline.calculate_nfcs_score(wallet)
        if 'error' not in score_info:
            print(f"\nWallet: {wallet[:10]}...")
            print(f"NFCS Score: {score_info['nfcs_score']:.2f}")
            print(f"Risk Level: {score_info['risk_level']}")
            print(f"Repayment Ratio: {score_info['key_metrics']['repayment_ratio']:.3f}")
        else:
            print(f"Error for wallet {wallet}: {score_info['error']}")
