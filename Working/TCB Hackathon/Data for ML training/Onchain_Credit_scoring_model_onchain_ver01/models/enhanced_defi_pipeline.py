#!/usr/bin/env python3
"""
Enhanced DeFi Credit Scoring Pipeline with Smart Contract Integration
Combines traditional DeFi lending data with smart contract interaction patterns
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, List, Optional
import pickle
import json

# Import our modules
from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
from smart_contract_processor import SmartContractProcessor
from blockchain_integration import BlockchainIntegration

logger = logging.getLogger(__name__)

class EnhancedDeFiPipeline:
    """
    Enhanced DeFi Credit Scoring Pipeline that integrates:
    1. Traditional DeFi lending data (borrows, repays, deposits, etc.)
    2. Smart contract interaction patterns
    3. Cross-chain activity analysis
    4. Blockchain integration for real-time scoring
    """
    
    def __init__(self, data_path: str = ".", blockchain_config: Optional[Dict] = None):
        """
        Initialize the enhanced pipeline
        
        Args:
            data_path: Path to data files
            blockchain_config: Configuration for blockchain integration
        """
        self.data_path = data_path
        self.blockchain_config = blockchain_config or {}
        
        # Initialize components
        self.defi_pipeline = DeFiCreditScoringPipeline(data_path)
        self.sc_processor = SmartContractProcessor()
        self.blockchain_integration = None
        
        # Enhanced features
        self.enhanced_features_df = None
        self.enhanced_models = {}
        self.feature_importance_enhanced = {}
        
        if blockchain_config:
            self.blockchain_integration = BlockchainIntegration(blockchain_config)
    
    def run_enhanced_pipeline(self) -> None:
        """Run the complete enhanced pipeline"""
        logger.info("Starting Enhanced DeFi Credit Scoring Pipeline...")
        
        # 1. Run traditional DeFi pipeline
        logger.info("Step 1: Running traditional DeFi pipeline...")
        self.defi_pipeline.load_data()
        self.defi_pipeline.preprocess_data()
        self.defi_pipeline.engineer_features()
        self.defi_pipeline.create_target_variable()
        
        # 2. Process smart contract data
        logger.info("Step 2: Processing smart contract data...")
        self.sc_processor.load_smart_contract_data()
        self.sc_processor.preprocess_smart_contract_data()
        sc_features = self.sc_processor.extract_smart_contract_features()
        
        # 3. Merge features
        logger.info("Step 3: Merging DeFi and smart contract features...")
        self.enhanced_features_df = self.sc_processor.merge_with_defi_features(
            self.defi_pipeline.features_df
        )
        
        # 4. Enhanced feature engineering
        logger.info("Step 4: Enhanced feature engineering...")
        self._engineer_enhanced_features()
        
        # 5. Train enhanced models
        logger.info("Step 5: Training enhanced models...")
        self._train_enhanced_models()
        
        # 6. Generate comprehensive analysis
        logger.info("Step 6: Generating enhanced analysis...")
        self._generate_enhanced_analysis()
        
        logger.info("Enhanced pipeline completed successfully!")
    
    def _engineer_enhanced_features(self) -> None:
        """Engineer additional features combining DeFi and smart contract data"""
        logger.info("Engineering enhanced features...")
        
        df = self.enhanced_features_df.copy()
        
        # Cross-domain interaction features
        df['defi_sc_activity_ratio'] = (
            df['totalNumberOfBorrow'] + df['totalNumberOfRepay'] + df['totalNumberOfDeposit']
        ) / (df['sc_total_contracts_interacted'] + 1)
        
        df['sc_defi_value_ratio'] = (
            df['sc_total_market_cap'] / (df['totalAmountOfBorrowInUSD'] + df['totalAmountOfDepositInUSD'] + 1)
        )
        
        # Enhanced risk indicators
        df['multi_chain_defi_user'] = (
            (df['sc_unique_chains'] > 1) & 
            (df['totalAmountOfBorrowInUSD'] > 0)
        ).astype(int)
        
        df['sophisticated_user_score'] = (
            df['sc_unique_contract_types'] * 0.3 +
            df['sc_unique_projects'] * 0.3 +
            df['protocol_diversity_score'] * 0.4
        )
        
        # Temporal consistency features
        df['activity_consistency_score'] = np.where(
            (df['sc_days_since_last_activity'] < 30) & (df['activity_frequency'] > 0.01),
            1.0,
            np.where(
                (df['sc_days_since_last_activity'] > 365) | (df['activity_frequency'] < 0.001),
                0.0,
                0.5
            )
        )
        
        # Enhanced NFCS calculation with smart contract features
        df['enhanced_nfcs_score'] = self._calculate_enhanced_nfcs(df)
        
        self.enhanced_features_df = df
        logger.info("Enhanced feature engineering completed")
    
    def _calculate_enhanced_nfcs(self, df: pd.DataFrame) -> pd.Series:
        """Calculate enhanced NFCS score incorporating smart contract features"""
        
        # Normalize smart contract features
        sc_features = [
            'sc_activity_intensity', 'sc_cross_chain_ratio', 'sophisticated_user_score',
            'activity_consistency_score'
        ]
        
        for feature in sc_features:
            if feature in df.columns:
                df[f'{feature}_normalized'] = (
                    (df[feature] - df[feature].min()) / 
                    (df[feature].max() - df[feature].min() + 1e-8)
                )
        
        # Enhanced NFCS formula
        enhanced_nfcs = (
            # Traditional DeFi components (70% weight)
            df['repayment_ratio_normalized'] * 0.25 +
            df['repayment_consistency_normalized'] * 0.20 +
            df['deposit_to_borrow_ratio_normalized'] * 0.15 +
            df['protocol_diversity_score_normalized'] * 0.10 +
            
            # Smart contract components (30% weight)
            df['sc_activity_intensity_normalized'] * 0.10 +
            df['sc_cross_chain_ratio_normalized'] * 0.05 +
            df['sophisticated_user_score_normalized'] * 0.10 +
            df['activity_consistency_score_normalized'] * 0.05
        ) * 1000
        
        # Apply penalties
        enhanced_nfcs = np.where(df['has_been_liquidated'] == 1, enhanced_nfcs * 0.5, enhanced_nfcs)
        enhanced_nfcs = np.where(df['sc_high_activity_flag'] == 1, enhanced_nfcs * 1.1, enhanced_nfcs)  # Bonus for high activity
        enhanced_nfcs = np.where(df['sc_dormant_wallet_flag'] == 1, enhanced_nfcs * 0.8, enhanced_nfcs)  # Penalty for dormancy
        
        return enhanced_nfcs
    
    def _train_enhanced_models(self) -> None:
        """Train enhanced models with smart contract features"""
        logger.info("Training enhanced models...")
        
        # Prepare enhanced feature set
        enhanced_feature_cols = [col for col in self.enhanced_features_df.columns 
                               if col not in ['walletAddress', 'credit_risk', 'is_good_borrower', 
                                            'first_borrow_date', 'last_borrow_date']]
        
        X_enhanced = self.enhanced_features_df[enhanced_feature_cols].fillna(0)
        y = self.enhanced_features_df['credit_risk']
        
        # Scale features
        from sklearn.preprocessing import StandardScaler
        from sklearn.model_selection import train_test_split
        from sklearn.ensemble import RandomForestClassifier
        import xgboost as xgb
        
        scaler_enhanced = StandardScaler()
        X_enhanced_scaled = scaler_enhanced.fit_transform(X_enhanced)
        X_enhanced_scaled_df = pd.DataFrame(X_enhanced_scaled, columns=enhanced_feature_cols)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_enhanced_scaled_df, y, test_size=0.2, random_state=42, stratify=y
        )
        
        # Train Enhanced Random Forest
        rf_enhanced = RandomForestClassifier(n_estimators=150, random_state=42, n_jobs=-1)
        rf_enhanced.fit(X_train, y_train)
        self.enhanced_models['random_forest_enhanced'] = rf_enhanced
        self.feature_importance_enhanced['random_forest_enhanced'] = dict(
            zip(enhanced_feature_cols, rf_enhanced.feature_importances_)
        )
        
        # Train Enhanced XGBoost
        xgb_enhanced = xgb.XGBClassifier(random_state=42, eval_metric='mlogloss')
        xgb_enhanced.fit(X_train, y_train)
        self.enhanced_models['xgboost_enhanced'] = xgb_enhanced
        self.feature_importance_enhanced['xgboost_enhanced'] = dict(
            zip(enhanced_feature_cols, xgb_enhanced.feature_importances_)
        )
        
        # Store for evaluation
        self.X_train_enhanced = X_train
        self.X_test_enhanced = X_test
        self.y_train_enhanced = y_train
        self.y_test_enhanced = y_test
        self.enhanced_feature_cols = enhanced_feature_cols
        self.scaler_enhanced = scaler_enhanced
        
        logger.info("Enhanced model training completed")
    
    def calculate_enhanced_nfcs_score(self, wallet_address: str) -> Dict:
        """
        Calculate enhanced NFCS score for a wallet including smart contract features
        
        Args:
            wallet_address: Wallet address to score
            
        Returns:
            Enhanced scoring information
        """
        if wallet_address not in self.enhanced_features_df['walletAddress'].values:
            return {'error': 'Wallet address not found in enhanced dataset'}
        
        wallet_data = self.enhanced_features_df[
            self.enhanced_features_df['walletAddress'] == wallet_address
        ].iloc[0]
        
        # Get enhanced model predictions
        feature_vector = wallet_data[self.enhanced_feature_cols].fillna(0).values.reshape(1, -1)
        feature_vector_scaled = self.scaler_enhanced.transform(feature_vector)
        
        # Enhanced Random Forest prediction
        rf_enhanced_pred = self.enhanced_models['random_forest_enhanced'].predict(feature_vector_scaled)[0]
        rf_enhanced_prob = self.enhanced_models['random_forest_enhanced'].predict_proba(feature_vector_scaled)[0]
        
        # Enhanced XGBoost prediction
        xgb_enhanced_pred = self.enhanced_models['xgboost_enhanced'].predict(feature_vector_scaled)[0]
        xgb_enhanced_prob = self.enhanced_models['xgboost_enhanced'].predict_proba(feature_vector_scaled)[0]
        
        # Ensemble prediction
        ensemble_prob = (rf_enhanced_prob + xgb_enhanced_prob) / 2
        ensemble_risk = np.argmax(ensemble_prob)
        
        # Enhanced NFCS score
        enhanced_nfcs = wallet_data['enhanced_nfcs_score']
        
        # Smart contract insights
        sc_insights = {
            'smart_contract_activity': {
                'total_contracts': wallet_data['sc_total_contracts_interacted'],
                'unique_chains': wallet_data['sc_unique_chains'],
                'cross_chain_user': bool(wallet_data['sc_cross_chain_activity']),
                'contract_creator': bool(wallet_data['sc_is_contract_creator']),
                'activity_intensity': wallet_data['sc_activity_intensity']
            },
            'defi_integration': {
                'sophisticated_user_score': wallet_data['sophisticated_user_score'],
                'multi_chain_defi_user': bool(wallet_data['multi_chain_defi_user']),
                'activity_consistency': wallet_data['activity_consistency_score']
            }
        }
        
        return {
            'wallet_address': wallet_address,
            'enhanced_nfcs_score': enhanced_nfcs,
            'traditional_nfcs_score': wallet_data['nfcs_score'],
            'risk_level': ['Low', 'Medium', 'High'][ensemble_risk],
            'risk_probabilities': {
                'low': ensemble_prob[0],
                'medium': ensemble_prob[1],
                'high': ensemble_prob[2]
            },
            'enhanced_model_predictions': {
                'random_forest_enhanced': {
                    'risk_level': ['Low', 'Medium', 'High'][rf_enhanced_pred],
                    'probabilities': rf_enhanced_prob.tolist()
                },
                'xgboost_enhanced': {
                    'risk_level': ['Low', 'Medium', 'High'][xgb_enhanced_pred],
                    'probabilities': xgb_enhanced_prob.tolist()
                }
            },
            'smart_contract_insights': sc_insights,
            'key_metrics': {
                'repayment_ratio': wallet_data['repayment_ratio'],
                'repayment_consistency': wallet_data['repayment_consistency'],
                'deposit_to_borrow_ratio': wallet_data['deposit_to_borrow_ratio'],
                'protocol_diversity_score': wallet_data['protocol_diversity_score'],
                'has_been_liquidated': bool(wallet_data['has_been_liquidated']),
                'total_borrow_amount': wallet_data['totalAmountOfBorrowInUSD'],
                'total_repay_amount': wallet_data['totalAmountOfRepayInUSD']
            }
        }
    
    def _generate_enhanced_analysis(self) -> None:
        """Generate comprehensive enhanced analysis"""
        logger.info("Generating enhanced analysis...")
        
        # Model performance comparison
        from sklearn.metrics import accuracy_score, classification_report
        
        # Enhanced model performance
        rf_enhanced_pred = self.enhanced_models['random_forest_enhanced'].predict(self.X_test_enhanced)
        xgb_enhanced_pred = self.enhanced_models['xgboost_enhanced'].predict(self.X_test_enhanced)
        
        rf_enhanced_accuracy = accuracy_score(self.y_test_enhanced, rf_enhanced_pred)
        xgb_enhanced_accuracy = accuracy_score(self.y_test_enhanced, xgb_enhanced_pred)
        
        # Compare with traditional models
        traditional_accuracy = {
            'random_forest': 0.962,  # From previous results
            'xgboost': 0.985
        }
        
        enhanced_accuracy = {
            'random_forest_enhanced': rf_enhanced_accuracy,
            'xgboost_enhanced': xgb_enhanced_accuracy
        }
        
        logger.info("Enhanced Model Performance:")
        logger.info(f"Enhanced Random Forest: {rf_enhanced_accuracy:.3f}")
        logger.info(f"Enhanced XGBoost: {xgb_enhanced_accuracy:.3f}")
        
        # Feature importance analysis
        self._analyze_enhanced_feature_importance()
        
        # Smart contract insights
        self._generate_smart_contract_insights()
    
    def _analyze_enhanced_feature_importance(self) -> None:
        """Analyze feature importance in enhanced models"""
        logger.info("Analyzing enhanced feature importance...")
        
        # Get top features from enhanced models
        rf_importance = sorted(
            self.feature_importance_enhanced['random_forest_enhanced'].items(),
            key=lambda x: x[1], reverse=True
        )[:15]
        
        xgb_importance = sorted(
            self.feature_importance_enhanced['xgboost_enhanced'].items(),
            key=lambda x: x[1], reverse=True
        )[:15]
        
        logger.info("Top 10 Enhanced Random Forest Features:")
        for i, (feature, importance) in enumerate(rf_importance[:10], 1):
            logger.info(f"{i}. {feature}: {importance:.4f}")
        
        logger.info("Top 10 Enhanced XGBoost Features:")
        for i, (feature, importance) in enumerate(xgb_importance[:10], 1):
            logger.info(f"{i}. {feature}: {importance:.4f}")
    
    def _generate_smart_contract_insights(self) -> None:
        """Generate insights from smart contract data"""
        logger.info("Generating smart contract insights...")
        
        sc_report = self.sc_processor.generate_smart_contract_report()
        
        logger.info("Smart Contract Data Summary:")
        logger.info(f"Total records: {sc_report['data_summary']['total_records']:,}")
        logger.info(f"Unique wallets: {sc_report['data_summary']['unique_wallets']:,}")
        logger.info(f"Unique chains: {sc_report['data_summary']['unique_chains']}")
        logger.info(f"Cross-chain wallets: {sc_report['cross_chain_wallets']:,}")
        
        # Chain distribution
        logger.info("Top 5 Chains by Activity:")
        chain_dist = sc_report['chain_distribution']
        for i, (chain, count) in enumerate(sorted(chain_dist.items(), key=lambda x: x[1], reverse=True)[:5], 1):
            logger.info(f"{i}. Chain {chain}: {count:,} interactions")
    
    def save_enhanced_pipeline(self, filepath: str) -> None:
        """Save the enhanced pipeline to disk"""
        logger.info(f"Saving enhanced pipeline to {filepath}")
        
        pipeline_data = {
            'enhanced_models': self.enhanced_models,
            'feature_importance_enhanced': self.feature_importance_enhanced,
            'enhanced_feature_cols': self.enhanced_feature_cols,
            'scaler_enhanced': self.scaler_enhanced
        }
        
        with open(filepath, 'wb') as f:
            pickle.dump(pipeline_data, f)
        
        logger.info("Enhanced pipeline saved successfully")
    
    def load_enhanced_pipeline(self, filepath: str) -> None:
        """Load the enhanced pipeline from disk"""
        logger.info(f"Loading enhanced pipeline from {filepath}")
        
        with open(filepath, 'rb') as f:
            pipeline_data = pickle.load(f)
        
        self.enhanced_models = pipeline_data['enhanced_models']
        self.feature_importance_enhanced = pipeline_data['feature_importance_enhanced']
        self.enhanced_feature_cols = pipeline_data['enhanced_feature_cols']
        self.scaler_enhanced = pipeline_data['scaler_enhanced']
        
        logger.info("Enhanced pipeline loaded successfully")

# Example usage
if __name__ == "__main__":
    # Initialize enhanced pipeline
    enhanced_pipeline = EnhancedDeFiPipeline(".")
    
    # Run enhanced pipeline
    enhanced_pipeline.run_enhanced_pipeline()
    
    # Test enhanced scoring
    sample_wallets = enhanced_pipeline.enhanced_features_df['walletAddress'].head(3).tolist()
    
    print("\nEnhanced NFCS Scoring Results:")
    print("-" * 60)
    for wallet in sample_wallets:
        enhanced_score = enhanced_pipeline.calculate_enhanced_nfcs_score(wallet)
        if 'error' not in enhanced_score:
            print(f"\nWallet: {wallet[:10]}...")
            print(f"Enhanced NFCS: {enhanced_score['enhanced_nfcs_score']:.2f}")
            print(f"Traditional NFCS: {enhanced_score['traditional_nfcs_score']:.2f}")
            print(f"Risk Level: {enhanced_score['risk_level']}")
            print(f"Cross-chain User: {enhanced_score['smart_contract_insights']['smart_contract_activity']['cross_chain_user']}")
            print(f"Sophisticated Score: {enhanced_score['smart_contract_insights']['defi_integration']['sophisticated_user_score']:.3f}")
        else:
            print(f"Error for wallet {wallet}: {enhanced_score['error']}")
