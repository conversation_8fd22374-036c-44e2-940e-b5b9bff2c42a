#!/usr/bin/env python3
"""
Script to run the DeFi Credit Scoring Pipeline
"""

import sys
import os
from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline

def main():
    print("🏦 DeFi Credit Scoring Pipeline")
    print("=" * 50)
    
    try:
        # Initialize pipeline
        print("Initializing pipeline...")
        pipeline = DeFiCreditScoringPipeline(".")
        
        # Run full pipeline
        print("Running full pipeline...")
        pipeline.run_full_pipeline()
        
        print("\n✅ Pipeline completed successfully!")
        print("\nNext steps:")
        print("1. Check the generated visualization files:")
        print("   - feature_distributions.png")
        print("   - correlation_matrix.png") 
        print("   - outlier_analysis.png")
        print("   - target_analysis.png")
        print("   - feature_importance.png")
        print("\n2. Run the web UI:")
        print("   streamlit run defi_scoring_ui.py")
        
        # Test scoring for a few wallets
        print("\n" + "=" * 50)
        print("Testing wallet scoring...")
        sample_wallets = pipeline.features_df['walletAddress'].head(5).tolist()
        
        for i, wallet in enumerate(sample_wallets, 1):
            print(f"\n{i}. Testing wallet: {wallet[:10]}...")
            score_info = pipeline.calculate_nfcs_score(wallet)
            
            if 'error' not in score_info:
                print(f"   NFCS Score: {score_info['nfcs_score']:.2f}/1000")
                print(f"   Risk Level: {score_info['risk_level']}")
                print(f"   Repayment Ratio: {score_info['key_metrics']['repayment_ratio']:.3f}")
                print(f"   Liquidated: {'Yes' if score_info['key_metrics']['has_been_liquidated'] else 'No'}")
            else:
                print(f"   Error: {score_info['error']}")
        
        print("\n" + "=" * 50)
        print("🎉 All tests completed!")
        
    except Exception as e:
        print(f"❌ Error running pipeline: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
