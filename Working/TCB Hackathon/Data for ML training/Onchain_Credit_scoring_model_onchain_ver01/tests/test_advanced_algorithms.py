#!/usr/bin/env python3
"""
Test script for advanced clustering and anomaly detection algorithms
Demonstrates DBSCAN, autoencoder, and enhanced anomaly detection
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
import warnings
warnings.filterwarnings('ignore')

def test_advanced_algorithms():
    """Test the advanced clustering and anomaly detection algorithms"""
    
    print("🚀 Testing Advanced DeFi Credit Scoring Algorithms")
    print("=" * 60)
    
    # Initialize pipeline
    print("\n1. Initializing Enhanced Pipeline...")
    pipeline = DeFiCreditScoringPipeline(".")
    
    # Load and preprocess data
    print("2. Loading and preprocessing data...")
    pipeline.load_data()
    pipeline.preprocess_data()
    pipeline.engineer_features()
    pipeline.create_target_variable()
    
    # Train models (including advanced algorithms)
    print("3. Training models with advanced algorithms...")
    pipeline.train_models()
    
    # Test clustering for new users
    print("\n" + "=" * 60)
    print("🔍 TESTING CLUSTERING FOR NEW USERS")
    print("=" * 60)
    
    # Find wallets without loan history
    no_loan_wallets = pipeline.features_df[
        pipeline.features_df['totalAmountOfBorrowInUSD'] == 0
    ]['walletAddress'].head(5).tolist()
    
    print(f"\nFound {len(no_loan_wallets)} wallets without loan history for testing:")
    
    for i, wallet in enumerate(no_loan_wallets, 1):
        print(f"\n--- Wallet {i}: {wallet[:10]}...{wallet[-8:]} ---")
        
        # Get cluster-based score
        cluster_result = pipeline.get_cluster_based_score(wallet)
        
        if 'error' not in cluster_result:
            print(f"Has loan history: {cluster_result['has_loan_history']}")
            
            if 'cluster_assignments' in cluster_result:
                assignments = cluster_result['cluster_assignments']
                
                # K-means cluster
                if 'kmeans' in assignments:
                    kmeans_info = assignments['kmeans']
                    print(f"K-means Cluster: {kmeans_info['cluster_id']}")
                    print(f"  - Cluster Size: {kmeans_info['cluster_size']}")
                    print(f"  - Avg NFCS: {kmeans_info['avg_nfcs']:.1f}")
                    print(f"  - Suggested Score: {kmeans_info['suggested_initial_score']:.1f}")
                
                # DBSCAN cluster
                if 'dbscan' in assignments:
                    dbscan_info = assignments['dbscan']
                    if dbscan_info['cluster_id'] == -1:
                        print(f"DBSCAN: Noise/Outlier (ID: {dbscan_info['cluster_id']})")
                        print(f"  - Suggested Score: {dbscan_info['suggested_initial_score']:.1f}")
                    else:
                        print(f"DBSCAN Cluster: {dbscan_info['cluster_id']}")
                        print(f"  - Cluster Size: {dbscan_info['cluster_size']}")
                        print(f"  - Density: {dbscan_info['density']:.4f}")
                        print(f"  - Suggested Score: {dbscan_info['suggested_initial_score']:.1f}")
        else:
            print(f"Error: {cluster_result['error']}")
    
    # Test anomaly detection
    print("\n" + "=" * 60)
    print("🚨 TESTING ADVANCED ANOMALY DETECTION")
    print("=" * 60)
    
    # Get sample wallets for anomaly testing
    sample_wallets = pipeline.features_df['walletAddress'].head(5).tolist()
    
    print(f"\nTesting anomaly detection on {len(sample_wallets)} sample wallets:")
    
    for i, wallet in enumerate(sample_wallets, 1):
        print(f"\n--- Wallet {i}: {wallet[:10]}...{wallet[-8:]} ---")
        
        # Get advanced anomaly analysis
        anomaly_result = pipeline.get_advanced_anomaly_analysis(wallet)
        
        if 'error' not in anomaly_result:
            analysis = anomaly_result['anomaly_analysis']
            
            print("Anomaly Detection Results:")
            
            # Isolation Forest
            if 'isolation_forest' in analysis:
                iso_result = analysis['isolation_forest']
                print(f"  - Isolation Forest: {'🚨 ANOMALY' if iso_result['is_anomaly'] else '✅ Normal'}")
            
            # Local Outlier Factor
            if 'local_outlier_factor' in analysis:
                lof_result = analysis['local_outlier_factor']
                print(f"  - Local Outlier Factor: {'🚨 ANOMALY' if lof_result['is_anomaly'] else '✅ Normal'}")
            
            # One-Class SVM
            if 'one_class_svm' in analysis:
                svm_result = analysis['one_class_svm']
                print(f"  - One-Class SVM: {'🚨 ANOMALY' if svm_result['is_anomaly'] else '✅ Normal'}")
            
            # Autoencoder
            if 'autoencoder' in analysis:
                ae_result = analysis['autoencoder']
                print(f"  - Autoencoder: {'🚨 ANOMALY' if ae_result['is_anomaly'] else '✅ Normal'}")
                print(f"    Reconstruction Error: {ae_result['reconstruction_error']:.6f}")
            
            # Ensemble
            if 'ensemble' in analysis:
                ensemble_result = analysis['ensemble']
                print(f"  - Ensemble: {'🚨 ANOMALY' if ensemble_result['is_anomaly'] else '✅ Normal'}")
                print(f"    Confidence: {ensemble_result['confidence']:.3f}")
            
            # Risk assessment
            risk_assessment = anomaly_result['risk_assessment']
            print(f"\nRisk Assessment: {risk_assessment['risk_level']}")
            print(f"Reason: {risk_assessment['reason']}")
            print(f"Action: {risk_assessment['recommended_action']}")
        else:
            print(f"Error: {anomaly_result['error']}")
    
    # Test enhanced NFCS scoring
    print("\n" + "=" * 60)
    print("📊 TESTING ENHANCED NFCS SCORING")
    print("=" * 60)
    
    test_wallets = pipeline.features_df['walletAddress'].head(3).tolist()
    
    for i, wallet in enumerate(test_wallets, 1):
        print(f"\n--- Enhanced Scoring for Wallet {i}: {wallet[:10]}...{wallet[-8:]} ---")
        
        # Get enhanced NFCS score
        score_result = pipeline.calculate_nfcs_score(wallet)
        
        if 'error' not in score_result:
            print(f"NFCS Score: {score_result['nfcs_score']:.1f}/1000")
            print(f"Risk Level: {score_result['risk_level']}")
            
            # Advanced anomaly detection results
            if 'advanced_anomaly_detection' in score_result:
                anomaly_info = score_result['advanced_anomaly_detection']
                anomaly_count = sum(1 for method, result in anomaly_info.items() 
                                  if isinstance(result, dict) and result.get('is_anomaly', False))
                print(f"Anomaly Flags: {anomaly_count}/{len(anomaly_info)} methods")
            
            # Clustering analysis results
            if 'clustering_analysis' in score_result:
                cluster_info = score_result['clustering_analysis']
                print("Cluster Assignments:")
                for method, result in cluster_info.items():
                    if isinstance(result, dict) and 'cluster_id' in result:
                        cluster_id = result['cluster_id']
                        if method == 'dbscan' and cluster_id == -1:
                            print(f"  - {method.upper()}: Noise/Outlier")
                        else:
                            print(f"  - {method.upper()}: Cluster {cluster_id}")
        else:
            print(f"Error: {score_result['error']}")
    
    # Generate summary statistics
    print("\n" + "=" * 60)
    print("📈 ALGORITHM PERFORMANCE SUMMARY")
    print("=" * 60)
    
    # Clustering statistics
    if hasattr(pipeline, 'clustering_models'):
        print("\nClustering Results:")
        
        # Enhanced K-means
        if 'enhanced_kmeans' in pipeline.clustering_models:
            kmeans_clusters = pipeline.features_df['enhanced_kmeans_cluster'].nunique()
            print(f"  - Enhanced K-means: {kmeans_clusters} clusters")
        
        # DBSCAN
        if 'dbscan_cluster' in pipeline.features_df.columns:
            dbscan_clusters = pipeline.features_df['dbscan_cluster'].nunique()
            noise_points = (pipeline.features_df['dbscan_cluster'] == -1).sum()
            print(f"  - DBSCAN: {dbscan_clusters-1} clusters, {noise_points} noise points")
    
    # Anomaly detection statistics
    if hasattr(pipeline, 'anomaly_detectors'):
        print("\nAnomaly Detection Results:")
        
        anomaly_methods = []
        if 'anomaly_isolation_forest' in pipeline.features_df.columns:
            iso_anomalies = (pipeline.features_df['anomaly_isolation_forest'] == -1).sum()
            anomaly_methods.append(f"Isolation Forest: {iso_anomalies} anomalies")
        
        if 'anomaly_lof' in pipeline.features_df.columns:
            lof_anomalies = (pipeline.features_df['anomaly_lof'] == -1).sum()
            anomaly_methods.append(f"Local Outlier Factor: {lof_anomalies} anomalies")
        
        if 'anomaly_autoencoder' in pipeline.features_df.columns:
            ae_anomalies = (pipeline.features_df['anomaly_autoencoder'] == 1).sum()
            anomaly_methods.append(f"Autoencoder: {ae_anomalies} anomalies")
        
        if 'is_anomaly' in pipeline.features_df.columns:
            ensemble_anomalies = (pipeline.features_df['is_anomaly'] == 1).sum()
            anomaly_methods.append(f"Ensemble: {ensemble_anomalies} anomalies")
        
        for method in anomaly_methods:
            print(f"  - {method}")
    
    # Cluster characteristics summary
    if hasattr(pipeline, 'cluster_characteristics') and pipeline.cluster_characteristics:
        print("\nCluster Characteristics (Top 3 by size):")
        
        # Sort clusters by size
        sorted_clusters = sorted(
            pipeline.cluster_characteristics.items(),
            key=lambda x: x[1].get('size', 0),
            reverse=True
        )
        
        for cluster_name, characteristics in sorted_clusters[:3]:
            print(f"\n  {cluster_name}:")
            print(f"    - Size: {characteristics.get('size', 'N/A')}")
            print(f"    - Avg NFCS: {characteristics.get('avg_nfcs', 0):.1f}")
            print(f"    - Liquidation Rate: {characteristics.get('liquidation_rate', 0):.1%}")
            print(f"    - Avg Repayment Ratio: {characteristics.get('avg_repayment_ratio', 0):.3f}")
    
    print("\n" + "=" * 60)
    print("✅ ADVANCED ALGORITHMS TESTING COMPLETED!")
    print("=" * 60)
    
    return pipeline

if __name__ == "__main__":
    # Run the test
    pipeline = test_advanced_algorithms()
    
    print("\n🎯 Key Features Implemented:")
    print("  ✅ DBSCAN clustering for new users")
    print("  ✅ Enhanced K-means with optimal cluster selection")
    print("  ✅ Multiple anomaly detection methods:")
    print("      - Enhanced Isolation Forest")
    print("      - Local Outlier Factor")
    print("      - One-Class SVM")
    print("      - Deep Autoencoder")
    print("      - Ensemble anomaly detection")
    print("  ✅ Cluster-based initial scoring for new users")
    print("  ✅ Comprehensive anomaly analysis")
    print("  ✅ Enhanced NFCS scoring with advanced features")
    
    print("\n🚀 Ready for production use!")
