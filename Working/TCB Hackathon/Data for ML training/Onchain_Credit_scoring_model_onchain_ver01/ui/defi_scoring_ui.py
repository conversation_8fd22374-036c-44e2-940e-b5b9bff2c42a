#!/usr/bin/env python3
"""
DeFi Credit Scoring Web UI
Interactive dashboard for credit scoring and data visualization
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import pickle
import json
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Import our pipelines
from defi_credit_scoring_pipeline import DeFiCreditScoringPipeline
from enhanced_defi_pipeline import EnhancedDeFiPipeline
from smart_contract_processor import SmartContractProcessor

# Page configuration
st.set_page_config(
    page_title="DeFi Credit Scoring Dashboard",
    page_icon="🏦",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
    .main-header {
        font-size: 3rem;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 5px solid #1f77b4;
    }
    .risk-low {
        color: #28a745;
        font-weight: bold;
    }
    .risk-medium {
        color: #ffc107;
        font-weight: bold;
    }
    .risk-high {
        color: #dc3545;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)

@st.cache_data
def load_pipeline():
    """Load the trained pipeline"""
    try:
        pipeline = DeFiCreditScoringPipeline(".")
        pipeline.load_data()
        pipeline.preprocess_data()
        pipeline.engineer_features()
        pipeline.create_target_variable()
        pipeline.train_models()
        return pipeline
    except Exception as e:
        st.error(f"Error loading pipeline: {e}")
        return None

def main():
    # Header
    st.markdown('<h1 class="main-header">🏦 DeFi Credit Scoring Dashboard</h1>', unsafe_allow_html=True)
    st.markdown("---")
    
    # Load pipeline
    with st.spinner("Loading ML pipeline..."):
        pipeline = load_pipeline()
    
    if pipeline is None:
        st.error("Failed to load the ML pipeline. Please check your data files.")
        return
    
    # Sidebar
    st.sidebar.title("Navigation")
    page = st.sidebar.selectbox(
        "Choose a page",
        ["Overview", "Wallet Scoring", "SHAP Explainability", "Data Visualization", "Model Performance", "Batch Analysis"]
    )
    
    if page == "Overview":
        show_overview(pipeline)
    elif page == "Wallet Scoring":
        show_wallet_scoring(pipeline)
    elif page == "SHAP Explainability":
        show_shap_explainability(pipeline)
    elif page == "Data Visualization":
        show_data_visualization(pipeline)
    elif page == "Model Performance":
        show_model_performance(pipeline)
    elif page == "Batch Analysis":
        show_batch_analysis(pipeline)

def show_overview(pipeline):
    """Show overview dashboard"""
    st.header("📊 Dataset Overview")
    
    # Key metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Total Wallets", f"{len(pipeline.features_df):,}")
    
    with col2:
        avg_nfcs = pipeline.features_df['nfcs_score'].mean()
        st.metric("Average NFCS Score", f"{avg_nfcs:.1f}")
    
    with col3:
        liquidation_rate = pipeline.features_df['has_been_liquidated'].mean() * 100
        st.metric("Liquidation Rate", f"{liquidation_rate:.1f}%")
    
    with col4:
        high_risk_pct = (pipeline.features_df['credit_risk'] == 2).mean() * 100
        st.metric("High Risk Wallets", f"{high_risk_pct:.1f}%")
    
    # Risk distribution
    st.subheader("Risk Distribution")
    risk_counts = pipeline.features_df['credit_risk'].value_counts().sort_index()
    risk_labels = ['Low Risk', 'Medium Risk', 'High Risk']
    
    fig = px.pie(
        values=risk_counts.values,
        names=risk_labels,
        title="Credit Risk Distribution",
        color_discrete_sequence=['#28a745', '#ffc107', '#dc3545']
    )
    st.plotly_chart(fig, use_container_width=True)
    
    # NFCS Score distribution
    st.subheader("NFCS Score Distribution")
    fig = px.histogram(
        pipeline.features_df,
        x='nfcs_score',
        nbins=50,
        title="Distribution of NFCS Scores",
        labels={'nfcs_score': 'NFCS Score', 'count': 'Number of Wallets'}
    )
    fig.add_vline(x=avg_nfcs, line_dash="dash", line_color="red", 
                  annotation_text=f"Mean: {avg_nfcs:.1f}")
    st.plotly_chart(fig, use_container_width=True)

def show_wallet_scoring(pipeline):
    """Show individual wallet scoring interface"""
    st.header("🔍 Individual Wallet Scoring")
    
    # Wallet selection
    wallet_options = pipeline.features_df['walletAddress'].tolist()
    
    col1, col2 = st.columns([2, 1])
    
    with col1:
        selected_wallet = st.selectbox(
            "Select a wallet address:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )
    
    with col2:
        if st.button("Calculate NFCS Score", type="primary"):
            with st.spinner("Calculating score..."):
                score_info = pipeline.calculate_nfcs_score(selected_wallet)
                st.session_state.score_info = score_info
    
    # Display results
    if hasattr(st.session_state, 'score_info') and 'error' not in st.session_state.score_info:
        score_info = st.session_state.score_info
        
        # Main score display
        col1, col2, col3 = st.columns(3)
        
        with col1:
            nfcs_score = score_info['nfcs_score']
            st.metric("NFCS Score", f"{nfcs_score:.1f}/1000")
            
            # Score gauge
            fig = go.Figure(go.Indicator(
                mode = "gauge+number",
                value = nfcs_score,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "NFCS Score"},
                gauge = {
                    'axis': {'range': [None, 1000]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 300], 'color': "lightgray"},
                        {'range': [300, 700], 'color': "gray"},
                        {'range': [700, 1000], 'color': "lightgreen"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 500
                    }
                }
            ))
            fig.update_layout(height=300)
            st.plotly_chart(fig, use_container_width=True)
        
        with col2:
            risk_level = score_info['risk_level']
            risk_class = f"risk-{risk_level.lower()}"
            st.markdown(f'<p class="{risk_class}">Risk Level: {risk_level}</p>', unsafe_allow_html=True)
            
            # Risk probabilities
            probs = score_info['risk_probabilities']
            prob_df = pd.DataFrame({
                'Risk Level': ['Low', 'Medium', 'High'],
                'Probability': [probs['low'], probs['medium'], probs['high']]
            })
            
            fig = px.bar(prob_df, x='Risk Level', y='Probability', 
                        title="Risk Probabilities",
                        color='Risk Level',
                        color_discrete_map={'Low': '#28a745', 'Medium': '#ffc107', 'High': '#dc3545'})
            st.plotly_chart(fig, use_container_width=True)
        
        with col3:
            st.subheader("Key Metrics")
            metrics = score_info['key_metrics']
            
            st.metric("Repayment Ratio", f"{metrics['repayment_ratio']:.3f}")
            st.metric("Repayment Consistency", f"{metrics['repayment_consistency']:.3f}")
            st.metric("Deposit/Borrow Ratio", f"{metrics['deposit_to_borrow_ratio']:.3f}")
            st.metric("Protocol Diversity", f"{metrics['protocol_diversity_score']:.1f}")
            
            if metrics['has_been_liquidated']:
                st.error("⚠️ Wallet has been liquidated")
            else:
                st.success("✅ No liquidation history")
        
        # Detailed breakdown
        st.subheader("Detailed Analysis")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.write("**Model Predictions:**")
            for model, pred in score_info['model_predictions'].items():
                st.write(f"- {model.title()}: {pred['risk_level']}")
        
        with col2:
            st.write("**Anomaly Detection:**")
            if score_info['anomaly_detection']['is_anomaly']:
                st.error("🚨 Flagged as anomalous behavior")
            else:
                st.success("✅ Normal behavior pattern")
            
            st.write(f"Anomaly Score: {score_info['anomaly_detection']['anomaly_score']:.3f}")
            st.write(f"Cluster: {score_info['cluster']}")

def show_shap_explainability(pipeline):
    """Show SHAP model explainability interface"""
    st.header("🔍 SHAP Model Explainability")

    st.markdown("""
    **SHAP (SHapley Additive exPlanations)** provides detailed explanations of model predictions by showing
    how each feature contributes to the final prediction. This helps understand why a wallet received a specific credit score.
    """)

    # Setup SHAP if not already done
    if not pipeline.shap_explainers:
        with st.spinner("Setting up SHAP explainers..."):
            pipeline.setup_shap_explainers()

    # Wallet selection for individual explanation
    st.subheader("Individual Wallet Explanation")

    col1, col2 = st.columns([2, 1])

    with col1:
        wallet_options = pipeline.features_df['walletAddress'].tolist()
        selected_wallet = st.selectbox(
            "Select a wallet for SHAP explanation:",
            options=wallet_options,
            format_func=lambda x: f"{x[:10]}...{x[-8:]}"
        )

    with col2:
        model_choice = st.selectbox(
            "Select model:",
            options=['random_forest', 'xgboost'],
            format_func=lambda x: x.replace('_', ' ').title()
        )

    if st.button("Generate SHAP Explanation", type="primary"):
        with st.spinner("Generating SHAP explanation..."):
            shap_explanation = pipeline.get_shap_explanation(selected_wallet, model_choice)
            st.session_state.shap_explanation = shap_explanation

    # Display SHAP explanation
    if hasattr(st.session_state, 'shap_explanation') and 'error' not in st.session_state.shap_explanation:
        shap_exp = st.session_state.shap_explanation

        # Prediction summary
        st.subheader("Prediction Summary")
        col1, col2, col3 = st.columns(3)

        with col1:
            pred = shap_exp['prediction']
            risk_color = {'Low': 'green', 'Medium': 'orange', 'High': 'red'}[pred['risk_level']]
            st.markdown(f"**Risk Level**: <span style='color:{risk_color}'>{pred['risk_level']}</span>",
                       unsafe_allow_html=True)

        with col2:
            st.metric("Risk Class", pred['risk_class'])

        with col3:
            max_prob = max(pred['probabilities'])
            st.metric("Confidence", f"{max_prob:.1%}")

        # SHAP values summary
        st.subheader("SHAP Explanation Summary")
        shap_info = shap_exp['shap_explanation']

        col1, col2, col3 = st.columns(3)
        with col1:
            st.metric("Expected Value", f"{shap_info['expected_value']:.3f}")
        with col2:
            st.metric("Prediction Value", f"{shap_info['prediction_value']:.3f}")
        with col3:
            st.metric("Total SHAP Impact", f"{shap_info['total_shap_contribution']:.3f}")

        # Feature contributions
        st.subheader("Feature Contributions")

        # Top positive contributors
        if shap_exp['top_positive_features']:
            st.write("**🟢 Top Positive Contributors (increase risk score):**")
            pos_df = pd.DataFrame(shap_exp['top_positive_features'][:10])
            pos_df = pos_df[['feature', 'shap_value', 'original_value']].round(4)

            fig = px.bar(pos_df, x='shap_value', y='feature', orientation='h',
                        title="Top 10 Positive SHAP Contributions",
                        color='shap_value', color_continuous_scale='Reds')
            st.plotly_chart(fig, use_container_width=True)

            with st.expander("View detailed positive contributions"):
                st.dataframe(pos_df)

        # Top negative contributors
        if shap_exp['top_negative_features']:
            st.write("**🔴 Top Negative Contributors (decrease risk score):**")
            neg_df = pd.DataFrame(shap_exp['top_negative_features'][:10])
            neg_df = neg_df[['feature', 'shap_value', 'original_value']].round(4)

            fig = px.bar(neg_df, x='shap_value', y='feature', orientation='h',
                        title="Top 10 Negative SHAP Contributions",
                        color='shap_value', color_continuous_scale='Blues_r')
            st.plotly_chart(fig, use_container_width=True)

            with st.expander("View detailed negative contributions"):
                st.dataframe(neg_df)

        # Feature importance waterfall
        st.subheader("SHAP Waterfall Analysis")

        # Create waterfall-style data
        all_contribs = shap_exp['all_contributions'][:15]  # Top 15 features
        waterfall_data = []
        cumulative = shap_info['expected_value']

        for contrib in all_contribs:
            waterfall_data.append({
                'feature': contrib['feature'],
                'contribution': contrib['shap_value'],
                'cumulative': cumulative + contrib['shap_value']
            })
            cumulative += contrib['shap_value']

        waterfall_df = pd.DataFrame(waterfall_data)

        fig = go.Figure()

        # Add bars for contributions
        colors = ['red' if x > 0 else 'blue' for x in waterfall_df['contribution']]
        fig.add_trace(go.Bar(
            x=waterfall_df['feature'],
            y=waterfall_df['contribution'],
            marker_color=colors,
            name='SHAP Contribution'
        ))

        fig.update_layout(
            title="SHAP Waterfall Chart - Feature Contributions",
            xaxis_title="Features",
            yaxis_title="SHAP Value",
            xaxis_tickangle=-45
        )

        st.plotly_chart(fig, use_container_width=True)

    # Global SHAP analysis
    st.subheader("Global SHAP Analysis")

    if st.button("Show Global Feature Importance"):
        st.info("Global SHAP analysis shows overall feature importance across all predictions.")

        # Display information about generated SHAP plots
        st.markdown("""
        **Generated SHAP Visualization Files:**
        - `shap_summary_plots.png` - SHAP summary plots for both models
        - `shap_feature_importance.png` - Global feature importance
        - `shap_waterfall_plots.png` - Sample prediction explanations
        - `shap_dependence_plots.png` - Feature dependence analysis

        These files are generated when running the full pipeline and provide comprehensive
        model interpretability analysis.
        """)

    # NFCS Formula explanation
    st.subheader("📊 NFCS Formula Breakdown")

    if st.button("Show NFCS Mathematical Formulas"):
        st.markdown("""
        ### Core NFCS Formula

        ```
        NFCS_base = (
            Repayment_Ratio_Normalized × 0.30 +
            Repayment_Consistency_Normalized × 0.25 +
            Deposit_Borrow_Ratio_Normalized × 0.20 +
            Protocol_Diversity_Normalized × 0.15 +
            Activity_Frequency_Normalized × 0.10
        ) × 1000
        ```

        ### Final NFCS with Adjustments

        ```
        NFCS_final = NFCS_base × Liquidation_Penalty × Anomaly_Penalty × Model_Adjustment
        ```

        **Where:**
        - `Liquidation_Penalty = 0.5` if liquidated, else `1.0`
        - `Anomaly_Penalty = 0.5` if anomalous, else `1.0`
        - `Model_Adjustment` based on ensemble risk prediction

        For complete mathematical formulas, see the `NFCS_FORMULAS.md` file.
        """)

    # Model comparison
    st.subheader("Model Interpretability Comparison")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        **Random Forest Interpretability:**
        - Tree-based feature importance
        - Natural feature interactions
        - Robust to outliers
        - Easy to interpret splits
        """)

    with col2:
        st.markdown("""
        **XGBoost Interpretability:**
        - Gradient-based importance
        - Complex feature interactions
        - Higher predictive accuracy
        - More sophisticated patterns
        """)

def show_data_visualization(pipeline):
    """Show data visualization dashboard"""
    st.header("📈 Data Visualization")
    
    # Feature correlation heatmap
    st.subheader("Feature Correlations")
    numeric_features = pipeline.features_df.select_dtypes(include=[np.number]).columns
    corr_matrix = pipeline.features_df[numeric_features].corr()
    
    fig = px.imshow(corr_matrix, 
                    title="Feature Correlation Matrix",
                    color_continuous_scale='RdBu_r',
                    aspect="auto")
    st.plotly_chart(fig, use_container_width=True)
    
    # Feature distributions
    st.subheader("Feature Distributions")
    
    feature_to_plot = st.selectbox(
        "Select feature to visualize:",
        options=['nfcs_score', 'repayment_ratio', 'totalAmountOfBorrowInUSD', 
                'totalAmountOfDepositInUSD', 'protocol_diversity_score']
    )
    
    col1, col2 = st.columns(2)
    
    with col1:
        # Histogram
        fig = px.histogram(pipeline.features_df, x=feature_to_plot, 
                          title=f"Distribution of {feature_to_plot}")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        # Box plot by risk level
        fig = px.box(pipeline.features_df, x='credit_risk', y=feature_to_plot,
                    title=f"{feature_to_plot} by Risk Level")
        st.plotly_chart(fig, use_container_width=True)
    
    # Scatter plot
    st.subheader("Feature Relationships")
    
    col1, col2 = st.columns(2)
    with col1:
        x_feature = st.selectbox("X-axis:", options=numeric_features, index=0)
    with col2:
        y_feature = st.selectbox("Y-axis:", options=numeric_features, index=1)
    
    fig = px.scatter(pipeline.features_df, x=x_feature, y=y_feature, 
                    color='credit_risk', title=f"{x_feature} vs {y_feature}",
                    color_discrete_map={0: '#28a745', 1: '#ffc107', 2: '#dc3545'})
    st.plotly_chart(fig, use_container_width=True)

def show_model_performance(pipeline):
    """Show model performance metrics"""
    st.header("🎯 Model Performance")
    
    # Train models if not already trained
    if not pipeline.models:
        with st.spinner("Training models..."):
            pipeline.train_models()
    
    # Evaluate models
    with st.spinner("Evaluating models..."):
        evaluation_results = pipeline.evaluate_models()
    
    # Model accuracy comparison
    st.subheader("Model Accuracy Comparison")
    
    accuracies = {}
    for model_name, results in evaluation_results.items():
        if 'accuracy' in results:
            accuracies[model_name] = results['accuracy']
    
    if accuracies:
        acc_df = pd.DataFrame(list(accuracies.items()), columns=['Model', 'Accuracy'])
        fig = px.bar(acc_df, x='Model', y='Accuracy', title="Model Accuracy Comparison")
        st.plotly_chart(fig, use_container_width=True)
    
    # Feature importance
    if pipeline.feature_importance:
        st.subheader("Feature Importance")
        
        model_choice = st.selectbox("Select model:", options=list(pipeline.feature_importance.keys()))
        
        importance_data = pipeline.feature_importance[model_choice]
        importance_df = pd.DataFrame(list(importance_data.items()), 
                                   columns=['Feature', 'Importance']).sort_values('Importance', ascending=True)
        
        # Top 15 features
        top_features = importance_df.tail(15)
        fig = px.bar(top_features, x='Importance', y='Feature', orientation='h',
                    title=f"Top 15 Features - {model_choice.title()}")
        st.plotly_chart(fig, use_container_width=True)

def show_batch_analysis(pipeline):
    """Show batch analysis interface"""
    st.header("📊 Batch Analysis")
    
    # Risk level analysis
    st.subheader("Risk Level Analysis")
    
    risk_summary = pipeline.features_df.groupby('credit_risk').agg({
        'nfcs_score': ['mean', 'median', 'std'],
        'repayment_ratio': ['mean', 'median'],
        'totalAmountOfBorrowInUSD': ['mean', 'sum'],
        'has_been_liquidated': 'sum'
    }).round(3)
    
    st.dataframe(risk_summary)
    
    # Top and bottom performers
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Top 10 NFCS Scores")
        top_performers = pipeline.features_df.nlargest(10, 'nfcs_score')[
            ['walletAddress', 'nfcs_score', 'credit_risk', 'repayment_ratio']
        ]
        st.dataframe(top_performers)
    
    with col2:
        st.subheader("Bottom 10 NFCS Scores")
        bottom_performers = pipeline.features_df.nsmallest(10, 'nfcs_score')[
            ['walletAddress', 'nfcs_score', 'credit_risk', 'repayment_ratio']
        ]
        st.dataframe(bottom_performers)
    
    # Export functionality
    st.subheader("Export Data")
    
    if st.button("Generate Full Report"):
        # Create comprehensive report
        report_data = pipeline.features_df.copy()
        
        # Add model predictions for all wallets
        feature_matrix = report_data[pipeline.feature_cols].fillna(0)
        feature_matrix_scaled = pipeline.scaler.transform(feature_matrix)
        
        rf_predictions = pipeline.models['random_forest'].predict(feature_matrix_scaled)
        xgb_predictions = pipeline.models['xgboost'].predict(feature_matrix_scaled)
        
        report_data['rf_risk_prediction'] = rf_predictions
        report_data['xgb_risk_prediction'] = xgb_predictions
        
        # Download link
        csv = report_data.to_csv(index=False)
        st.download_button(
            label="Download Full Report (CSV)",
            data=csv,
            file_name=f"defi_credit_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )

if __name__ == "__main__":
    main()
