# DeFi Credit Scoring ML Pipeline

A comprehensive machine learning pipeline for credit scoring in undercollateralized DeFi lending, based on the methodology from <PERSON>'s article on AI-powered credit scoring on the blockchain.

## 🎯 Overview

This pipeline implements a complete ML solution for DeFi credit scoring that includes:

- **Data Preprocessing**: Handle missing values, duplicates, outliers, and skewed data
- **Feature Engineering**: Create NFCS (Non-Fungible Credit Score) components
- **Multiple ML Models**: Random Forest, XGBoost, Isolation Forest, K-means clustering
- **Comprehensive Visualization**: Data distribution, correlations, outliers analysis
- **Interactive Web UI**: Streamlit-based dashboard for scoring and analysis
- **Real-time Scoring**: Calculate NFCS scores for individual wallets

## 📊 Features

### Core ML Pipeline
- **Ensemble Approach**: Combines multiple models for robust predictions
- **NFCS Scoring**: Dynamic credit scoring based on on-chain behavior
- **Anomaly Detection**: Identifies bot activity and suspicious patterns
- **Risk Categorization**: Low, Medium, High risk classification
- **Temporal Analysis**: Wallet age and activity patterns

### Data Processing
- **Missing Value Handling**: Median imputation for numerical features
- **Duplicate Removal**: Based on wallet address and timestamp
- **Outlier Treatment**: IQR-based capping method
- **Skewness Correction**: Log transformation for right skew, Box-Cox for left skew
- **Feature Scaling**: StandardScaler for model training

### Visualization
- **Distribution Analysis**: Feature distributions and statistics
- **Correlation Matrix**: Feature relationships heatmap
- **Outlier Detection**: Box plots for outlier identification
- **Target Analysis**: Risk level distributions and relationships
- **Feature Importance**: Model-based feature ranking

## 🚀 Quick Start

### 1. Install Dependencies

```bash
pip install -r requirements.txt
```

### 2. Run the Pipeline

```bash
python run_pipeline.py
```

This will:
- Load and preprocess all CSV files
- Engineer features and create target variables
- Train multiple ML models
- Generate visualization plots
- Test scoring on sample wallets

### 3. Launch Web UI

```bash
streamlit run defi_scoring_ui.py
```

Access the dashboard at `http://localhost:8501`

## 📁 File Structure

```
├── defi_credit_scoring_pipeline.py  # Main ML pipeline
├── defi_scoring_ui.py              # Streamlit web interface
├── run_pipeline.py                 # Pipeline execution script
├── requirements.txt                # Python dependencies
├── README.md                       # This file
├── 1. full_knowledge_graph_borrows_50K.csv
├── 2. deposits_data_processed.csv
├── 3.liquidates_data_processed.csv
├── 4. repays_data_processed.csv
└── 4. withdraws_data_processed.csv
```

## 🔧 Pipeline Components

### 1. Data Loading
- Loads 5 CSV files containing DeFi transaction data
- Handles different data schemas and formats
- Validates data integrity

### 2. Preprocessing
- **Timestamp Processing**: Convert Unix timestamps to datetime
- **Missing Values**: Median imputation for numerical features
- **Duplicates**: Remove based on wallet address and relevant keys
- **Outliers**: IQR-based outlier capping
- **Skewness**: Log and Box-Cox transformations

### 3. Feature Engineering
- **Repayment Behavior**: Ratios and consistency metrics
- **Liquidity Management**: Deposit/borrow relationships
- **Activity Diversity**: Protocol interaction patterns
- **Risk Indicators**: Liquidation history and leverage
- **Temporal Features**: Wallet age and activity frequency
- **NFCS Score**: Composite credit score (0-1000 scale)

### 4. Model Training
- **Random Forest**: Robust for high-dimensional data
- **XGBoost**: High performance for complex patterns
- **Isolation Forest**: Anomaly detection for bot activity
- **K-means**: Clustering for new user categorization

### 5. Evaluation
- **Classification Metrics**: Accuracy, precision, recall, F1-score
- **Confusion Matrix**: Detailed prediction analysis
- **ROC-AUC**: Model discrimination ability
- **Feature Importance**: Model interpretability

## 🎛️ Web Dashboard Features

### Overview Page
- Dataset statistics and key metrics
- Risk distribution visualization
- NFCS score distribution

### Wallet Scoring
- Individual wallet NFCS calculation
- Risk level assessment with probabilities
- Key metrics breakdown
- Anomaly detection results

### Data Visualization
- Feature correlation heatmaps
- Distribution plots and box plots
- Interactive scatter plots
- Feature relationship analysis

### Model Performance
- Model accuracy comparison
- Feature importance rankings
- Cross-validation results

### Batch Analysis
- Risk level summaries
- Top/bottom performers
- Export functionality

## 📈 NFCS Score Calculation

The Non-Fungible Credit Score (NFCS) is calculated using:

```
NFCS = (
    Repayment Ratio × 0.30 +
    Repayment Consistency × 0.25 +
    Deposit/Borrow Ratio × 0.20 +
    Protocol Diversity × 0.15 +
    Activity Frequency × 0.10
) × 1000
```

**Adjustments:**
- Liquidated wallets: Score × 0.5
- Anomalous behavior: Score × 0.5
- Model ensemble predictions: Risk-based adjustments

## 🔍 Key Metrics

### Repayment Behavior
- **Repayment Ratio**: Total repaid / Total borrowed
- **Repayment Consistency**: Number of repays / Number of borrows

### Liquidity Management
- **Deposit/Borrow Ratio**: Total deposits / Total borrows
- **Withdraw/Deposit Ratio**: Total withdraws / Total deposits

### Activity Patterns
- **Protocol Diversity**: Unique lending pools × Unique tokens
- **Activity Frequency**: Borrow frequency / Wallet age
- **Transaction Size**: Average transaction amount

## 🛡️ Risk Assessment

### Risk Categories
- **Low Risk (0)**: Good repayment history, no liquidations
- **Medium Risk (1)**: Moderate repayment behavior
- **High Risk (2)**: Poor repayment or liquidation history

### Anomaly Detection
- Identifies unusual transaction patterns
- Flags potential bot activity
- Detects wash trading or spam transactions

## 📊 Visualization Outputs

The pipeline generates several visualization files:
- `feature_distributions.png`: Feature distribution analysis
- `correlation_matrix.png`: Feature correlation heatmap
- `outlier_analysis.png`: Box plots for outlier detection
- `target_analysis.png`: Risk level analysis
- `feature_importance.png`: Model feature rankings

## 🔧 Customization

### Adding New Features
1. Modify `engineer_features()` method
2. Update feature selection in `train_models()`
3. Adjust NFCS calculation weights

### Model Tuning
1. Modify hyperparameters in `train_models()`
2. Add new models to the ensemble
3. Adjust evaluation metrics

### UI Customization
1. Modify Streamlit components in `defi_scoring_ui.py`
2. Add new visualization pages
3. Customize styling with CSS

## 📝 Notes

- Designed for Ethereum, BNB Chain, and Polygon data
- Handles pseudoanonymous wallet addresses
- Optimized for real-time scoring applications
- Privacy-preserving by design
- Scalable for large datasets

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🙏 Acknowledgments

Based on the methodology from Sergei Savvov's article: "AI-Powered Credit Scoring on the Blockchain: Building an ML Model for Undercollateralized DeFi Lending"
