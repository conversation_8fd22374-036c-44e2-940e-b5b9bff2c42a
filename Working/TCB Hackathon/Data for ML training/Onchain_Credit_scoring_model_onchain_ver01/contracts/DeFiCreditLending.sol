// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@openzeppelin/contracts/token/ERC20/IERC20.sol";
import "@openzeppelin/contracts/token/ERC20/utils/SafeERC20.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";
import "./NFCSOracle.sol";

/**
 * @title DeFiCreditLending
 * @dev DeFi lending protocol with NFCS-based credit scoring
 * Supports undercollateralized lending based on credit scores
 */
contract DeFiCreditLending is Ownable, ReentrancyGuard, Pausable {
    using SafeERC20 for IERC20;
    
    // Loan structure
    struct Loan {
        address borrower;
        address token;
        uint256 amount;
        uint256 interestRate;      // Annual interest rate in basis points (100 = 1%)
        uint256 startTime;
        uint256 duration;          // Loan duration in seconds
        uint256 repaidAmount;
        bool isActive;
        bool isRepaid;
        uint256 nfcsScore;         // NFCS score at loan creation
        uint8 riskLevel;           // Risk level at loan creation
        uint256 collateralAmount; // Collateral amount (can be 0 for undercollateralized)
        address collateralToken;
    }
    
    // Risk-based lending parameters
    struct RiskParameters {
        uint256 minScore;          // Minimum NFCS score required
        uint256 maxLoanAmount;     // Maximum loan amount for this risk level
        uint256 baseInterestRate;  // Base interest rate in basis points
        uint256 collateralRatio;   // Required collateral ratio (0 = no collateral)
        uint256 maxDuration;       // Maximum loan duration
        bool isActive;             // Whether this risk level is active
    }
    
    // Events
    event LoanCreated(
        uint256 indexed loanId,
        address indexed borrower,
        address indexed token,
        uint256 amount,
        uint256 interestRate,
        uint256 nfcsScore
    );
    
    event LoanRepaid(
        uint256 indexed loanId,
        address indexed borrower,
        uint256 repaidAmount,
        uint256 timestamp
    );
    
    event LoanLiquidated(
        uint256 indexed loanId,
        address indexed borrower,
        address indexed liquidator,
        uint256 liquidationAmount
    );
    
    event RiskParametersUpdated(uint8 indexed riskLevel, RiskParameters params);
    
    // State variables
    NFCSOracle public nfcsOracle;
    
    mapping(uint256 => Loan) public loans;
    mapping(address => uint256[]) public userLoans;
    mapping(address => bool) public supportedTokens;
    mapping(uint8 => RiskParameters) public riskParameters;
    
    uint256 public nextLoanId = 1;
    uint256 public totalLoansCreated;
    uint256 public totalAmountLent;
    uint256 public totalAmountRepaid;
    
    // Protocol parameters
    uint256 public liquidationThreshold = 8000; // 80% in basis points
    uint256 public liquidationBonus = 500;      // 5% bonus for liquidators
    uint256 public protocolFee = 100;           // 1% protocol fee
    address public feeRecipient;
    
    modifier validToken(address _token) {
        require(supportedTokens[_token], "Token not supported");
        _;
    }
    
    modifier loanExists(uint256 _loanId) {
        require(_loanId < nextLoanId && loans[_loanId].borrower != address(0), "Loan does not exist");
        _;
    }
    
    constructor(
        address _nfcsOracle,
        address _feeRecipient
    ) {
        nfcsOracle = NFCSOracle(_nfcsOracle);
        feeRecipient = _feeRecipient;
        
        // Initialize default risk parameters
        _initializeRiskParameters();
    }
    
    /**
     * @dev Create a new loan based on NFCS score
     * @param _token Token to borrow
     * @param _amount Amount to borrow
     * @param _duration Loan duration in seconds
     * @param _collateralToken Collateral token (can be address(0) for uncollateralized)
     * @param _collateralAmount Collateral amount
     */
    function createLoan(
        address _token,
        uint256 _amount,
        uint256 _duration,
        address _collateralToken,
        uint256 _collateralAmount
    ) 
        external 
        nonReentrant 
        whenNotPaused 
        validToken(_token) 
    {
        // Get NFCS score from oracle
        (uint256 score, uint8 riskLevel, , bool isValid, uint256 confidence) = 
            nfcsOracle.getScore(msg.sender);
        
        require(isValid, "NFCS score not valid or expired");
        require(confidence >= 70, "NFCS confidence too low"); // Require at least 70% confidence
        
        RiskParameters memory riskParams = riskParameters[riskLevel];
        require(riskParams.isActive, "Risk level not supported");
        require(score >= riskParams.minScore, "NFCS score too low");
        require(_amount <= riskParams.maxLoanAmount, "Loan amount exceeds limit");
        require(_duration <= riskParams.maxDuration, "Duration exceeds limit");
        
        // Check collateral requirements
        if (riskParams.collateralRatio > 0) {
            require(_collateralToken != address(0), "Collateral required");
            require(_collateralAmount >= (_amount * riskParams.collateralRatio) / 10000, 
                   "Insufficient collateral");
            
            // Transfer collateral
            IERC20(_collateralToken).safeTransferFrom(msg.sender, address(this), _collateralAmount);
        }
        
        // Calculate interest rate based on risk and score
        uint256 interestRate = _calculateInterestRate(riskLevel, score);
        
        // Create loan
        uint256 loanId = nextLoanId++;
        loans[loanId] = Loan({
            borrower: msg.sender,
            token: _token,
            amount: _amount,
            interestRate: interestRate,
            startTime: block.timestamp,
            duration: _duration,
            repaidAmount: 0,
            isActive: true,
            isRepaid: false,
            nfcsScore: score,
            riskLevel: riskLevel,
            collateralAmount: _collateralAmount,
            collateralToken: _collateralToken
        });
        
        userLoans[msg.sender].push(loanId);
        totalLoansCreated++;
        totalAmountLent += _amount;
        
        // Transfer loan amount to borrower
        IERC20(_token).safeTransfer(msg.sender, _amount);
        
        emit LoanCreated(loanId, msg.sender, _token, _amount, interestRate, score);
    }
    
    /**
     * @dev Repay a loan
     * @param _loanId Loan ID to repay
     * @param _amount Amount to repay
     */
    function repayLoan(uint256 _loanId, uint256 _amount) 
        external 
        nonReentrant 
        loanExists(_loanId) 
    {
        Loan storage loan = loans[_loanId];
        require(loan.borrower == msg.sender, "Not loan borrower");
        require(loan.isActive, "Loan not active");
        require(!loan.isRepaid, "Loan already repaid");
        
        uint256 totalOwed = calculateTotalOwed(_loanId);
        require(_amount <= totalOwed - loan.repaidAmount, "Amount exceeds debt");
        
        // Transfer repayment
        IERC20(loan.token).safeTransferFrom(msg.sender, address(this), _amount);
        
        loan.repaidAmount += _amount;
        totalAmountRepaid += _amount;
        
        // Check if loan is fully repaid
        if (loan.repaidAmount >= totalOwed) {
            loan.isRepaid = true;
            loan.isActive = false;
            
            // Return collateral if any
            if (loan.collateralAmount > 0) {
                IERC20(loan.collateralToken).safeTransfer(msg.sender, loan.collateralAmount);
            }
        }
        
        emit LoanRepaid(_loanId, msg.sender, _amount, block.timestamp);
    }
    
    /**
     * @dev Liquidate an overdue loan
     * @param _loanId Loan ID to liquidate
     */
    function liquidateLoan(uint256 _loanId) 
        external 
        nonReentrant 
        loanExists(_loanId) 
    {
        Loan storage loan = loans[_loanId];
        require(loan.isActive, "Loan not active");
        require(block.timestamp > loan.startTime + loan.duration, "Loan not overdue");
        
        uint256 totalOwed = calculateTotalOwed(_loanId);
        uint256 remainingDebt = totalOwed - loan.repaidAmount;
        
        if (loan.collateralAmount > 0) {
            // Liquidate collateral
            uint256 liquidationAmount = (loan.collateralAmount * liquidationThreshold) / 10000;
            uint256 liquidatorBonus = (liquidationAmount * liquidationBonus) / 10000;
            
            // Transfer to liquidator
            IERC20(loan.collateralToken).safeTransfer(msg.sender, liquidationAmount + liquidatorBonus);
            
            // Remaining collateral to protocol
            uint256 remaining = loan.collateralAmount - liquidationAmount - liquidatorBonus;
            if (remaining > 0) {
                IERC20(loan.collateralToken).safeTransfer(feeRecipient, remaining);
            }
        }
        
        loan.isActive = false;
        
        emit LoanLiquidated(_loanId, loan.borrower, msg.sender, remainingDebt);
    }
    
    /**
     * @dev Calculate total amount owed for a loan
     * @param _loanId Loan ID
     * @return Total amount owed including interest
     */
    function calculateTotalOwed(uint256 _loanId) public view loanExists(_loanId) returns (uint256) {
        Loan memory loan = loans[_loanId];
        
        uint256 timeElapsed = block.timestamp - loan.startTime;
        if (timeElapsed > loan.duration) {
            timeElapsed = loan.duration;
        }
        
        uint256 interest = (loan.amount * loan.interestRate * timeElapsed) / (365 days * 10000);
        uint256 protocolFeeAmount = (interest * protocolFee) / 10000;
        
        return loan.amount + interest + protocolFeeAmount;
    }
    
    /**
     * @dev Calculate interest rate based on risk level and NFCS score
     * @param _riskLevel Risk level (0=Low, 1=Medium, 2=High)
     * @param _score NFCS score
     * @return Interest rate in basis points
     */
    function _calculateInterestRate(uint8 _riskLevel, uint256 _score) internal view returns (uint256) {
        RiskParameters memory riskParams = riskParameters[_riskLevel];
        uint256 baseRate = riskParams.baseInterestRate;
        
        // Adjust rate based on score within risk level
        // Higher score = lower rate
        uint256 scoreAdjustment = 0;
        if (_score < 500) {
            scoreAdjustment = 500; // +5% for very low scores
        } else if (_score < 700) {
            scoreAdjustment = 200; // +2% for medium scores
        }
        // No adjustment for high scores (700+)
        
        return baseRate + scoreAdjustment;
    }
    
    /**
     * @dev Initialize default risk parameters
     */
    function _initializeRiskParameters() internal {
        // Low Risk (NFCS 700+)
        riskParameters[0] = RiskParameters({
            minScore: 700,
            maxLoanAmount: 100000 * 10**18, // 100K tokens
            baseInterestRate: 500,           // 5% APR
            collateralRatio: 0,              // No collateral required
            maxDuration: 365 days,
            isActive: true
        });
        
        // Medium Risk (NFCS 400-699)
        riskParameters[1] = RiskParameters({
            minScore: 400,
            maxLoanAmount: 50000 * 10**18,   // 50K tokens
            baseInterestRate: 1000,          // 10% APR
            collateralRatio: 5000,           // 50% collateral
            maxDuration: 180 days,
            isActive: true
        });
        
        // High Risk (NFCS 0-399)
        riskParameters[2] = RiskParameters({
            minScore: 200,
            maxLoanAmount: 10000 * 10**18,   // 10K tokens
            baseInterestRate: 2000,          // 20% APR
            collateralRatio: 8000,           // 80% collateral
            maxDuration: 90 days,
            isActive: true
        });
    }
    
    // Admin functions
    function updateRiskParameters(
        uint8 _riskLevel,
        uint256 _minScore,
        uint256 _maxLoanAmount,
        uint256 _baseInterestRate,
        uint256 _collateralRatio,
        uint256 _maxDuration,
        bool _isActive
    ) external onlyOwner {
        riskParameters[_riskLevel] = RiskParameters({
            minScore: _minScore,
            maxLoanAmount: _maxLoanAmount,
            baseInterestRate: _baseInterestRate,
            collateralRatio: _collateralRatio,
            maxDuration: _maxDuration,
            isActive: _isActive
        });
        
        emit RiskParametersUpdated(_riskLevel, riskParameters[_riskLevel]);
    }
    
    function addSupportedToken(address _token) external onlyOwner {
        supportedTokens[_token] = true;
    }
    
    function removeSupportedToken(address _token) external onlyOwner {
        supportedTokens[_token] = false;
    }
    
    function updateNFCSOracle(address _newOracle) external onlyOwner {
        nfcsOracle = NFCSOracle(_newOracle);
    }
    
    function updateProtocolParameters(
        uint256 _liquidationThreshold,
        uint256 _liquidationBonus,
        uint256 _protocolFee,
        address _feeRecipient
    ) external onlyOwner {
        liquidationThreshold = _liquidationThreshold;
        liquidationBonus = _liquidationBonus;
        protocolFee = _protocolFee;
        feeRecipient = _feeRecipient;
    }
    
    function pause() external onlyOwner {
        _pause();
    }
    
    function unpause() external onlyOwner {
        _unpause();
    }
    
    // View functions
    function getUserLoans(address _user) external view returns (uint256[] memory) {
        return userLoans[_user];
    }
    
    function getProtocolStats() external view returns (
        uint256 totalLoans,
        uint256 totalLent,
        uint256 totalRepaid,
        uint256 activeLoans
    ) {
        uint256 active = 0;
        for (uint256 i = 1; i < nextLoanId; i++) {
            if (loans[i].isActive) active++;
        }
        
        return (totalLoansCreated, totalAmountLent, totalAmountRepaid, active);
    }
}
