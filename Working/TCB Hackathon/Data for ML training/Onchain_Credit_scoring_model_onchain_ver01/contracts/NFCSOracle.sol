// SPDX-License-Identifier: MIT
pragma solidity ^0.8.19;

import "@chainlink/contracts/src/v0.8/interfaces/AggregatorV3Interface.sol";
import "@openzeppelin/contracts/access/Ownable.sol";
import "@openzeppelin/contracts/security/ReentrancyGuard.sol";
import "@openzeppelin/contracts/utils/Pausable.sol";

/**
 * @title NFCSOracle
 * @dev Oracle contract for NFCS (Non-Fungible Credit Score) data
 * Provides credit scores for DeFi lending protocols with cross-chain compatibility
 */
contract NFCSOracle is Ownable, ReentrancyGuard, Pausable {
    
    // NFCS Score structure
    struct NFCSScore {
        uint256 score;              // Score from 0-1000
        uint8 riskLevel;           // 0=Low, 1=Medium, 2=High
        uint256 timestamp;         // Last update timestamp
        bool isActive;             // Score validity flag
        uint256 confidence;        // Model confidence (0-100)
        bytes32 modelVersion;      // ML model version hash
    }
    
    // Events
    event ScoreUpdated(
        address indexed wallet,
        uint256 indexed score,
        uint8 riskLevel,
        uint256 timestamp,
        uint256 confidence
    );
    
    event OracleUpdated(address indexed newOracle, uint256 timestamp);
    event ModelVersionUpdated(bytes32 indexed oldVersion, bytes32 indexed newVersion);
    event CrossChainScoreRequested(address indexed wallet, uint256 indexed chainId);
    
    // State variables
    mapping(address => NFCSScore) public walletScores;
    mapping(address => bool) public authorizedUpdaters;
    mapping(uint256 => address) public chainOracles; // chainId => oracle address
    
    bytes32 public currentModelVersion;
    uint256 public totalScoresUpdated;
    uint256 public scoreValidityPeriod = 7 days; // Scores valid for 7 days
    
    // Cross-chain compatibility
    mapping(uint256 => bool) public supportedChains;
    uint256[] public chainIds;
    
    // Oracle configuration
    struct OracleConfig {
        uint256 minConfidence;     // Minimum confidence threshold
        uint256 maxScoreAge;       // Maximum age for score validity
        bool requireSignature;     // Require signature verification
        address fallbackOracle;    // Fallback oracle address
    }
    
    OracleConfig public config;
    
    modifier onlyAuthorized() {
        require(authorizedUpdaters[msg.sender] || msg.sender == owner(), "Not authorized");
        _;
    }
    
    modifier validScore(uint256 _score) {
        require(_score <= 1000, "Score must be <= 1000");
        _;
    }
    
    modifier validRiskLevel(uint8 _riskLevel) {
        require(_riskLevel <= 2, "Risk level must be 0, 1, or 2");
        _;
    }
    
    constructor(
        bytes32 _initialModelVersion,
        uint256 _minConfidence,
        uint256 _maxScoreAge
    ) {
        currentModelVersion = _initialModelVersion;
        config = OracleConfig({
            minConfidence: _minConfidence,
            maxScoreAge: _maxScoreAge,
            requireSignature: false,
            fallbackOracle: address(0)
        });
        
        // Add initial supported chains
        _addSupportedChain(1);     // Ethereum
        _addSupportedChain(56);    // BSC
        _addSupportedChain(137);   // Polygon
        _addSupportedChain(42161); // Arbitrum
        _addSupportedChain(10);    // Optimism
    }
    
    /**
     * @dev Update NFCS score for a wallet
     * @param _wallet Wallet address
     * @param _score NFCS score (0-1000)
     * @param _riskLevel Risk level (0=Low, 1=Medium, 2=High)
     * @param _confidence Model confidence (0-100)
     */
    function updateScore(
        address _wallet,
        uint256 _score,
        uint8 _riskLevel,
        uint256 _confidence
    ) 
        external 
        onlyAuthorized 
        whenNotPaused 
        validScore(_score) 
        validRiskLevel(_riskLevel) 
    {
        require(_confidence >= config.minConfidence, "Confidence too low");
        require(_wallet != address(0), "Invalid wallet address");
        
        walletScores[_wallet] = NFCSScore({
            score: _score,
            riskLevel: _riskLevel,
            timestamp: block.timestamp,
            isActive: true,
            confidence: _confidence,
            modelVersion: currentModelVersion
        });
        
        totalScoresUpdated++;
        
        emit ScoreUpdated(_wallet, _score, _riskLevel, block.timestamp, _confidence);
    }
    
    /**
     * @dev Batch update multiple wallet scores
     * @param _wallets Array of wallet addresses
     * @param _scores Array of NFCS scores
     * @param _riskLevels Array of risk levels
     * @param _confidences Array of confidence values
     */
    function batchUpdateScores(
        address[] calldata _wallets,
        uint256[] calldata _scores,
        uint8[] calldata _riskLevels,
        uint256[] calldata _confidences
    ) external onlyAuthorized whenNotPaused {
        require(
            _wallets.length == _scores.length &&
            _scores.length == _riskLevels.length &&
            _riskLevels.length == _confidences.length,
            "Array lengths mismatch"
        );
        
        for (uint256 i = 0; i < _wallets.length; i++) {
            if (_scores[i] <= 1000 && 
                _riskLevels[i] <= 2 && 
                _confidences[i] >= config.minConfidence &&
                _wallets[i] != address(0)) {
                
                walletScores[_wallets[i]] = NFCSScore({
                    score: _scores[i],
                    riskLevel: _riskLevels[i],
                    timestamp: block.timestamp,
                    isActive: true,
                    confidence: _confidences[i],
                    modelVersion: currentModelVersion
                });
                
                emit ScoreUpdated(_wallets[i], _scores[i], _riskLevels[i], block.timestamp, _confidences[i]);
            }
        }
        
        totalScoresUpdated += _wallets.length;
    }
    
    /**
     * @dev Get NFCS score for a wallet
     * @param _wallet Wallet address
     * @return score NFCS score
     * @return riskLevel Risk level
     * @return timestamp Last update timestamp
     * @return isValid Whether score is still valid
     * @return confidence Model confidence
     */
    function getScore(address _wallet) 
        external 
        view 
        returns (
            uint256 score,
            uint8 riskLevel,
            uint256 timestamp,
            bool isValid,
            uint256 confidence
        ) 
    {
        NFCSScore memory nfcsScore = walletScores[_wallet];
        
        bool scoreIsValid = nfcsScore.isActive && 
                           (block.timestamp - nfcsScore.timestamp) <= config.maxScoreAge;
        
        return (
            nfcsScore.score,
            nfcsScore.riskLevel,
            nfcsScore.timestamp,
            scoreIsValid,
            nfcsScore.confidence
        );
    }
    
    /**
     * @dev Check if wallet is eligible for lending based on NFCS score
     * @param _wallet Wallet address
     * @param _minScore Minimum required score
     * @return eligible Whether wallet is eligible
     * @return currentScore Current NFCS score
     */
    function isEligibleForLending(address _wallet, uint256 _minScore) 
        external 
        view 
        returns (bool eligible, uint256 currentScore) 
    {
        NFCSScore memory nfcsScore = walletScores[_wallet];
        
        bool scoreIsValid = nfcsScore.isActive && 
                           (block.timestamp - nfcsScore.timestamp) <= config.maxScoreAge;
        
        eligible = scoreIsValid && nfcsScore.score >= _minScore;
        currentScore = nfcsScore.score;
    }
    
    /**
     * @dev Request cross-chain score update
     * @param _wallet Wallet address
     * @param _targetChain Target chain ID
     */
    function requestCrossChainScore(address _wallet, uint256 _targetChain) 
        external 
        whenNotPaused 
    {
        require(supportedChains[_targetChain], "Chain not supported");
        require(chainOracles[_targetChain] != address(0), "No oracle for chain");
        
        emit CrossChainScoreRequested(_wallet, _targetChain);
        
        // In a real implementation, this would trigger cross-chain communication
        // using protocols like LayerZero, Chainlink CCIP, or Axelar
    }
    
    // Admin functions
    function addAuthorizedUpdater(address _updater) external onlyOwner {
        authorizedUpdaters[_updater] = true;
    }
    
    function removeAuthorizedUpdater(address _updater) external onlyOwner {
        authorizedUpdaters[_updater] = false;
    }
    
    function updateModelVersion(bytes32 _newVersion) external onlyOwner {
        bytes32 oldVersion = currentModelVersion;
        currentModelVersion = _newVersion;
        emit ModelVersionUpdated(oldVersion, _newVersion);
    }
    
    function updateConfig(
        uint256 _minConfidence,
        uint256 _maxScoreAge,
        bool _requireSignature,
        address _fallbackOracle
    ) external onlyOwner {
        config.minConfidence = _minConfidence;
        config.maxScoreAge = _maxScoreAge;
        config.requireSignature = _requireSignature;
        config.fallbackOracle = _fallbackOracle;
    }
    
    function addSupportedChain(uint256 _chainId, address _oracle) external onlyOwner {
        _addSupportedChain(_chainId);
        chainOracles[_chainId] = _oracle;
    }
    
    function _addSupportedChain(uint256 _chainId) internal {
        if (!supportedChains[_chainId]) {
            supportedChains[_chainId] = true;
            chainIds.push(_chainId);
        }
    }
    
    function pause() external onlyOwner {
        _pause();
    }
    
    function unpause() external onlyOwner {
        _unpause();
    }
    
    // View functions
    function getSupportedChains() external view returns (uint256[] memory) {
        return chainIds;
    }
    
    function getOracleStats() external view returns (
        uint256 totalUpdates,
        uint256 activeScores,
        bytes32 modelVersion,
        uint256 validityPeriod
    ) {
        return (totalScoresUpdated, totalScoresUpdated, currentModelVersion, config.maxScoreAge);
    }
}
