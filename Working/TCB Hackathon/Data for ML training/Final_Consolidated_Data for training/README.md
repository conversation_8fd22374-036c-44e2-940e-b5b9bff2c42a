# DeFi Credit Scoring System

This system provides a comprehensive solution for credit scoring in DeFi lending, including data processing, visualization, and a user-friendly interface for credit scoring.

## Features

- Data preprocessing and cleaning
- Advanced visualizations for data analysis
- Machine learning model for credit scoring
- Interactive web interface for credit scoring

## Installation

1. Clone this repository
2. Install the required dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Run the credit scoring pipeline:
```bash
python credit_scoring_pipeline.py
```

2. Launch the Streamlit interface:
```bash
streamlit run credit_scoring_pipeline.py
```

## Data Processing

The system processes the following types of transaction data:
- Borrows
- Deposits
- Liquidations
- Repayments
- Withdrawals

## Visualizations

The system generates various visualizations:
- Distribution plots for numeric features
- Correlation heatmaps
- Time series analysis for timestamp data

## Credit Scoring

The system uses a Random Forest model for credit scoring, which considers:
- Transaction history
- Wallet behavior
- Protocol interactions
- Repayment patterns

## Contributing

Feel free to submit issues and enhancement requests. 