import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, confusion_matrix
import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
from datetime import datetime
import warnings
import gc
warnings.filterwarnings('ignore')

def optimize_dtypes(df):
    """Optimize DataFrame memory usage by downcasting numeric types"""
    try:
        for col in df.columns:
            if df[col].dtype == 'float64':
                df[col] = pd.to_numeric(df[col], downcast='float')
            elif df[col].dtype == 'int64':
                df[col] = pd.to_numeric(df[col], downcast='integer')
            elif df[col].dtype == 'object':
                # Convert object columns to category if they have few unique values
                if df[col].nunique() < len(df) * 0.5:  # If less than 50% unique values
                    df[col] = df[col].astype('category')
        return df
    except Exception as e:
        print(f"Warning: Error in optimize_dtypes: {str(e)}")
        return df

def load_and_merge_data():
    """Load and merge all transaction data files efficiently"""
    try:
        # Define file mappings
        file_mappings = {
            'borrows': '1. full_knowledge_graph_borrows_50K.csv',
            'deposits': '2. deposits_data_processed.csv',
            'liquidates': '3.liquidates_data_processed.csv',
            'repays': '4. repays_data_processed.csv',
            'withdraws': '4. withdraws_data_processed.csv'
        }
        
        # Define essential columns to load
        essential_columns = ['walletAddress']
        amount_columns = ['amount', 'borrow_amount', 'repay_amount', 'deposit_amount', 'withdraw_amount']
        timestamp_columns = ['timestamp', 'borrow_timestamp', 'repay_timestamp', 'deposit_timestamp', 'withdraw_timestamp']
        
        # Start with the first file (borrows) in chunks
        print("Loading borrows data...")
        chunk_size = 5000  # Reduced chunk size
        merged_df = None
        
        try:
            for chunk in pd.read_csv(file_mappings['borrows'], 
                                   usecols=essential_columns + amount_columns + timestamp_columns,
                                   chunksize=chunk_size):
                
                chunk = optimize_dtypes(chunk)
                
                if merged_df is None:
                    merged_df = chunk
                else:
                    merged_df = pd.concat([merged_df, chunk], ignore_index=True)
                
                # Force garbage collection
                del chunk
                gc.collect()
            
            print("Successfully loaded borrows data")
            
        except Exception as e:
            print(f"Error loading borrows data: {str(e)}")
            return None
        
        # Process other files in smaller chunks
        for name, filename in file_mappings.items():
            if name == 'borrows':
                continue
                
            try:
                print(f"Processing {name} data...")
                temp_df = None
                
                for chunk in pd.read_csv(filename, 
                                       usecols=essential_columns + amount_columns + timestamp_columns,
                                       chunksize=chunk_size):
                    
                    chunk = optimize_dtypes(chunk)
                    
                    if temp_df is None:
                        temp_df = chunk
                    else:
                        temp_df = pd.concat([temp_df, chunk], ignore_index=True)
                    
                    # Force garbage collection
                    del chunk
                    gc.collect()
                
                # Merge with main dataframe
                if temp_df is not None:
                    print(f"Merging {name} data...")
                    merged_df = merged_df.merge(
                        temp_df,
                        on='walletAddress',
                        how='left',
                        suffixes=('', f'_{name}')
                    )
                    
                    # Clean up
                    del temp_df
                    gc.collect()
                
                print(f"Successfully processed {name} data")
                
            except Exception as e:
                print(f"Warning: Could not process {name} data: {str(e)}")
                continue
        
        print("Data loading and merging completed successfully")
        return merged_df
        
    except Exception as e:
        print(f"Error in load_and_merge_data: {str(e)}")
        return None

def preprocess_data(df):
    """Clean and preprocess the data"""
    if df is None:
        return None
    
    try:
        print("Starting data preprocessing...")
        
        # Process in chunks to save memory
        chunk_size = 5000
        processed_chunks = []
        
        for i in range(0, len(df), chunk_size):
            chunk = df.iloc[i:i + chunk_size].copy()
            
            # Handle missing values
            numeric_columns = chunk.select_dtypes(include=[np.number]).columns
            chunk[numeric_columns] = chunk[numeric_columns].fillna(chunk[numeric_columns].mean())
            
            # Convert timestamp columns to datetime
            timestamp_columns = [col for col in chunk.columns if 'timestamp' in col.lower()]
            for col in timestamp_columns:
                chunk[col] = pd.to_datetime(chunk[col])
            
            # Handle outliers using IQR method
            for col in numeric_columns:
                Q1 = chunk[col].quantile(0.25)
                Q3 = chunk[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR
                chunk[col] = chunk[col].clip(lower_bound, upper_bound)
            
            # Handle skewed data
            for col in numeric_columns:
                if chunk[col].skew() > 1:
                    chunk[col] = np.log1p(chunk[col])
                elif chunk[col].skew() < -1:
                    chunk[col] = np.log1p(-chunk[col])
            
            processed_chunks.append(chunk)
            
            # Force garbage collection
            del chunk
            gc.collect()
        
        # Combine processed chunks
        processed_df = pd.concat(processed_chunks, ignore_index=True)
        
        # Remove duplicates
        processed_df = processed_df.drop_duplicates(subset=['walletAddress'])
        
        print("Data preprocessing completed successfully")
        return processed_df
        
    except Exception as e:
        print(f"Error in preprocessing: {str(e)}")
        return None

def create_visualizations(df):
    """Create various visualizations for data analysis"""
    if df is None:
        return
    
    try:
        print("Creating visualizations...")
        
        # Create a directory for visualizations
        import os
        if not os.path.exists('visualizations'):
            os.makedirs('visualizations')
        
        # Process visualizations in chunks to save memory
        chunk_size = 5000
        
        # Distribution plots for numeric columns
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        for col in numeric_columns[:3]:  # Reduced to 3 columns
            plt.figure(figsize=(10, 6))
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i + chunk_size]
                sns.histplot(data=chunk, x=col)
            plt.title(f'Distribution of {col}')
            plt.savefig(f'visualizations/dist_{col}.png')
            plt.close()
            gc.collect()
        
        # Correlation heatmap (using a sample of the data)
        sample_size = min(10000, len(df))
        sample_df = df.sample(n=sample_size, random_state=42)
        plt.figure(figsize=(12, 8))
        correlation_matrix = sample_df[numeric_columns].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm')
        plt.title('Correlation Heatmap')
        plt.tight_layout()
        plt.savefig('visualizations/correlation_heatmap.png')
        plt.close()
        del sample_df
        gc.collect()
        
        # Time series analysis for timestamp columns
        timestamp_columns = [col for col in df.columns if 'timestamp' in col.lower()]
        for col in timestamp_columns[:1]:  # Reduced to 1 column
            plt.figure(figsize=(12, 6))
            for i in range(0, len(df), chunk_size):
                chunk = df.iloc[i:i + chunk_size]
                chunk[col].value_counts().sort_index().plot()
            plt.title(f'Time Series Analysis of {col}')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig(f'visualizations/timeseries_{col}.png')
            plt.close()
            gc.collect()
        
        print("Visualizations created successfully")
        
    except Exception as e:
        print(f"Error creating visualizations: {str(e)}")

def train_model(df):
    """Train the credit scoring model"""
    if df is None or df.empty:
        print("Error: No valid data available for training")
        return None, None
    
    try:
        print("Starting model training...")
        
        # Prepare features and target
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        if len(numeric_columns) == 0:
            print("Error: No numeric columns found for training")
            return None, None
        
        # Create target variable based on repayment behavior
        if 'repay_amount' in df.columns and 'borrow_amount' in df.columns:
            df['default_status'] = (df['repay_amount'] < df['borrow_amount']).astype(int)
        else:
            print("Warning: Required columns for target variable not found")
            df['default_status'] = np.random.randint(0, 2, size=len(df))
        
        X = df[numeric_columns].drop('default_status', axis=1, errors='ignore')
        y = df['default_status']
        
        # Split the data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
        
        # Train Random Forest model
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        print("Model training completed successfully")
        return model, (X_test, y_test)
    
    except Exception as e:
        print(f"Error in model training: {str(e)}")
        return None, None

def create_scoring_ui():
    """Create a Streamlit UI for credit scoring"""
    st.title('DeFi Credit Scoring System')
    
    # Add file uploader
    uploaded_file = st.file_uploader("Upload transaction data", type=['csv'])
    
    if uploaded_file is not None:
        try:
            df = pd.read_csv(uploaded_file)
            st.write("Data Preview:")
            st.dataframe(df.head())
            
            # Add visualization options
            st.subheader("Data Visualizations")
            viz_option = st.selectbox(
                "Select visualization type",
                ["Distribution", "Correlation", "Time Series"]
            )
            
            if viz_option == "Distribution":
                column = st.selectbox("Select column", df.select_dtypes(include=[np.number]).columns)
                fig = px.histogram(df, x=column, title=f'Distribution of {column}')
                st.plotly_chart(fig)
            
            elif viz_option == "Correlation":
                corr_matrix = df.select_dtypes(include=[np.number]).corr()
                fig = px.imshow(corr_matrix, title='Correlation Heatmap')
                st.plotly_chart(fig)
            
            elif viz_option == "Time Series":
                timestamp_cols = [col for col in df.columns if 'timestamp' in col.lower()]
                if timestamp_cols:
                    column = st.selectbox("Select timestamp column", timestamp_cols)
                    df[column] = pd.to_datetime(df[column])
                    fig = px.line(df[column].value_counts().sort_index(), title=f'Time Series of {column}')
                    st.plotly_chart(fig)
        
        except Exception as e:
            st.error(f"Error processing uploaded file: {str(e)}")

def main():
    try:
        # Load and process data
        print("Loading data...")
        df = load_and_merge_data()
        
        if df is None:
            print("Error: Failed to load data")
            return
        
        print("Preprocessing data...")
        df = preprocess_data(df)
        
        if df is None:
            print("Error: Failed to preprocess data")
            return
        
        print("Creating visualizations...")
        create_visualizations(df)
        
        print("Training model...")
        model_result = train_model(df)
        
        if model_result is None:
            print("Error: Failed to train model")
            return
        
        model, (X_test, y_test) = model_result
        
        print("Creating scoring UI...")
        create_scoring_ui()
        
    except Exception as e:
        print(f"Error in main execution: {str(e)}")

if __name__ == "__main__":
    main() 