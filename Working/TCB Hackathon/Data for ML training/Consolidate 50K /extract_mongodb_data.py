import pandas as pd
from pymongo import MongoClient
import csv
from tqdm import tqdm

# MongoDB connection
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]

# Read wallet addresses from preprocessed_data_borrows.csv
print("Reading wallet addresses from preprocessed_data_borrows.csv...")
borrows_df = pd.read_csv('preprocessed_data_borrows.csv')
wallet_addresses = borrows_df['walletAddress'].unique().tolist()
print(f"Found {len(wallet_addresses)} unique wallet addresses")

# Collections to process
collections = {
    'deposits': 'deposits',
    'liquidates': 'liquidates',
    'repays': 'repays',
    'smart_contracts': 'smart_contracts',
    'wallets': 'wallets',
    'withdraws': 'withdraws',
    'multichain_wallets': 'multichain_wallets'
}

# Process each collection
for collection_name, collection_key in collections.items():
    print(f"\nProcessing {collection_name}...")
    collection = db[collection_key]
    
    # Query MongoDB
    if collection_name == 'liquidates':
        query = {'liquidatedWallet': {'$in': wallet_addresses}}
    elif collection_name in ['smart_contracts', 'wallets', 'multichain_wallets']:
        query = {'address': {'$in': wallet_addresses}}
    else:
        query = {'walletAddress': {'$in': wallet_addresses}}
    
    # Get all matching documents
    documents = list(collection.find(query))
    print(f"Found {len(documents)} documents in {collection_name}")
    
    if documents:
        # Convert to DataFrame
        df = pd.DataFrame(documents)
        
        # Rename fields if needed
        if collection_name == 'liquidates':
            df = df.rename(columns={'liquidatedWallet': 'walletAddress'})
        elif collection_name in ['smart_contracts', 'wallets', 'multichain_wallets']:
            df = df.rename(columns={'address': 'walletAddress'})
        
        # Remove MongoDB _id field
        if '_id' in df.columns:
            df = df.drop('_id', axis=1)
        
        # Save to CSV
        output_file = f'{collection_name}_data.csv'
        df.to_csv(output_file, index=False)
        print(f"Saved {len(df)} records to {output_file}")

print("\nExtraction complete!") 