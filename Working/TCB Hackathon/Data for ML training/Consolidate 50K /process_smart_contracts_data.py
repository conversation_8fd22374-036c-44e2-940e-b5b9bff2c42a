import pandas as pd
import ast
import json
from tqdm import tqdm

def try_parse_json(val):
    if pd.isna(val) or not str(val).strip():
        return val
    try:
        # Try to parse as JSON
        return json.loads(val)
    except Exception:
        try:
            # Try to parse as Python literal (for single quotes, etc.)
            return ast.literal_eval(val)
        except Exception:
            return val

def flatten_column(df, col):
    # Only flatten if the column contains at least one dict or list
    needs_flatten = df[col].apply(lambda x: isinstance(x, (dict, list))).any()
    if not needs_flatten:
        return df
    # If it's a list, join as string; if dict, expand keys
    if df[col].apply(lambda x: isinstance(x, list)).any():
        df[col] = df[col].apply(lambda x: ','.join(map(str, x)) if isinstance(x, list) else x)
    if df[col].apply(lambda x: isinstance(x, dict)).any():
        dict_df = df[col].apply(pd.Series)
        dict_df = dict_df.add_prefix(f'{col}_')
        df = pd.concat([df.drop(columns=[col]), dict_df], axis=1)
    return df

# Read the original file
print('Reading smart_contracts_data.csv...')
df = pd.read_csv('smart_contracts_data.csv', low_memory=False)

# Try to parse possible JSON/list columns
possible_json_cols = ['abi', 'tags_token', 'categories_Ethereum_Ecosystem']
for col in possible_json_cols:
    if col in df.columns:
        df[col] = df[col].apply(try_parse_json)
        df = flatten_column(df, col)

# Save the processed file
df.to_csv('smart_contracts_data_processed.csv', index=False)
print(f"Saved processed data to smart_contracts_data_processed.csv with {len(df)} rows.") 