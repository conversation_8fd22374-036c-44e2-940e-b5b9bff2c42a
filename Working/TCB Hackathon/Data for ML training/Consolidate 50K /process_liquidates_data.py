import pandas as pd
import ast
from tqdm import tqdm

def extract_liquidation_logs(row):
    logs = row.get('liquidationLogs')
    if pd.isna(logs) or not str(logs).strip():
        return []
    # Convert string to dict if needed
    if isinstance(logs, str):
        try:
            logs = ast.literal_eval(logs)
        except Exception:
            return []
    results = []
    debt_buyer = row.get('debtBuyerWallet')
    liquidated_wallet = row.get('walletAddress')
    # Both debtBuyerWallet and liquidatedWallet should be in logs
    if not logs or 'debtBuyerWallet' not in logs or 'liquidatedWallet' not in logs:
        return []
    for ts, buyer_data in logs['debtBuyerWallet'].items():
        liquidated_data = logs['liquidatedWallet'].get(ts, {})
        results.append({
            'walletAddress': liquidated_wallet,
            'debtBuyerWallet': debt_buyer,
            'timestamp': ts,
            'collateralAmount': buyer_data.get('collateralAmount'),
            'collateralAsset': buyer_data.get('collateralAsset'),
            'collateralAssetInUSD': buyer_data.get('collateralAssetInUSD'),
            'collateralProtocol': buyer_data.get('protocol'),
            'debtAmount': liquidated_data.get('debtAmount'),
            'debtAsset': liquidated_data.get('debtAsset'),
            'debtAssetInUSD': liquidated_data.get('debtAssetInUSD'),
            'debtProtocol': liquidated_data.get('protocol')
        })
    return results

# Read the original file
print('Reading liquidates_data.csv...')
df = pd.read_csv('liquidates_data.csv')

# Extract and flatten liquidation logs
all_rows = []
for _, row in tqdm(df.iterrows(), total=len(df)):
    extracted = extract_liquidation_logs(row)
    all_rows.extend(extracted)

# Create new DataFrame and save
enhanced_df = pd.DataFrame(all_rows)
enhanced_df.to_csv('liquidates_data_processed.csv', index=False)
print(f"Saved processed data to liquidates_data_processed.csv with {len(enhanced_df)} rows.") 