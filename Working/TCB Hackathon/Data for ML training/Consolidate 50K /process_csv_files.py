import pandas as pd
import os
from tqdm import tqdm

def process_csv_file(input_file):
    print(f"\nProcessing {input_file}...")
    
    # Read the CSV file
    df = pd.read_csv(input_file)
    
    # Get the base name without extension
    base_name = os.path.splitext(input_file)[0]
    
    # Create a new directory for processed files if it doesn't exist
    processed_dir = 'processed_data'
    os.makedirs(processed_dir, exist_ok=True)
    
    # Process each collection type
    if 'deposits' in input_file:
        # Ensure all required columns exist
        required_columns = [
            'walletAddress', 'lendingPoolAddress', 'timestamp', 'tokenAddress',
            'valueInUSD', 'totalNumberOfDeposit', 'totalAmountOfDepositInUSD',
            'highestDepositInUSD', 'lowestDepositInUSD', 'averageDepositInUSD'
        ]
        df = df.reindex(columns=required_columns)
        
    elif 'liquidates' in input_file:
        required_columns = [
            'walletAddress', 'debtBuyerWallet', 'timestamp', 'collateralAmount',
            'collateralAsset', 'collateralAssetInUSD', 'collateralProtocol',
            'debtAmount', 'debtAsset', 'debtAssetInUSD', 'debtProtocol'
        ]
        df = df.reindex(columns=required_columns)
        
    elif 'repays' in input_file:
        required_columns = [
            'walletAddress', 'lendingPoolAddress', 'totalAmountOfRepayInUSD',
            'totalNumberOfRepay', 'highestRepayInUSD', 'lowestRepayInUSD',
            'averageRepayInUSD', 'timestamp', 'type', 'token', 'value'
        ]
        df = df.reindex(columns=required_columns)
        
    elif 'smart_contracts' in input_file:
        required_columns = [
            'walletAddress', 'abi', 'tags_token', 'name', 'symbol', 'decimals',
            'categories_Ethereum_Ecosystem', 'marketCap', 'totalSupply',
            'tradingVolume', 'price', 'numberOfHolders', 'lastUpdatedAt',
            'keyABI', 'idCoingecko', 'imgUrl', 'chainId', 'timestamp',
            'priceChange', 'numberOfDailyCalls', 'numberOfDailyActiveUsers',
            'tokenDailyTransfers', 'numberOfLastDayCalls',
            'numberOfThisMonthCalls', 'numberOfLastDayActiveUsers'
        ]
        df = df.reindex(columns=required_columns)
        
    elif 'wallets' in input_file:
        required_columns = [
            'walletAddress', 'chainId', 'lastUpdatedAt', 'flagged',
            'timestamp', 'dailyAllTransactions', 'theFirstToHash',
            'theFirstToTimestamp', 'theFirstFromHash', 'theFirstFromTimestamp',
            'type'
        ]
        df = df.reindex(columns=required_columns)
        
    elif 'withdraws' in input_file:
        required_columns = [
            'walletAddress', 'lendingPoolAddress', 'timestamp', 'token',
            'tokenValue', 'valueInUSD', 'totalNumberOfWithdraw',
            'totalAmountOfWithdrawInUSD', 'highestWithdrawInUSD',
            'lowestWithdrawInUSD', 'averageWithdrawInUSD'
        ]
        df = df.reindex(columns=required_columns)
        
    elif 'multichain_wallets' in input_file:
        required_columns = [
            'walletAddress', 'lastUpdatedAt', 'balanceInUSD', 'depositInUSD',
            'borrowInUSD', 'totalValueOfLiquidation', 'numberOfLiquidation',
            'flagged', 'type', 'timestamp', 'value', 'token'
        ]
        df = df.reindex(columns=required_columns)
    
    # Save the processed file
    output_file = os.path.join(processed_dir, f"{base_name}_processed.csv")
    df.to_csv(output_file, index=False)
    print(f"Saved processed file to {output_file}")
    print(f"Number of records: {len(df)}")
    print(f"Columns: {', '.join(df.columns)}")

def main():
    # List of CSV files to process
    csv_files = [
        'deposits_data.csv',
        'liquidates_data.csv',
        'repays_data.csv',
        'smart_contracts_data.csv',
        'wallets_data.csv',
        'withdraws_data.csv',
        'multichain_wallets_data.csv'
    ]
    
    # Process each file
    for csv_file in csv_files:
        if os.path.exists(csv_file):
            process_csv_file(csv_file)
        else:
            print(f"\nWarning: {csv_file} not found. Skipping...")

if __name__ == "__main__":
    main() 