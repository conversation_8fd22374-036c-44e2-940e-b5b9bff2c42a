import pandas as pd
import ast
from datetime import datetime

# Read the data
df = pd.read_csv('wallets_data_processed.csv')

# Parse the dailyAllTransactions column (string to dict)
def parse_dict(val):
    if pd.isna(val):
        return {}
    if isinstance(val, dict):
        return val
    try:
        return ast.literal_eval(val)
    except Exception:
        return {}

# Apply parsing
df['dailyAllTransactions'] = df['dailyAllTransactions'].apply(parse_dict)

# Collect all unique timestamps
all_dates = set()
for d in df['dailyAllTransactions']:
    all_dates.update(d.keys())

# Convert to sorted list and to readable date
all_dates = sorted(all_dates, key=int)
date_map = {ts: datetime.utcfromtimestamp(int(ts)).strftime('%Y-%m-%d') for ts in all_dates}

# Create columns for each date
def extract_counts(row):
    d = row['dailyAllTransactions']
    return [d.get(ts, 0) for ts in all_dates]

counts = df.apply(extract_counts, axis=1, result_type='expand')
counts.columns = [f'tx_{date_map[ts]}' for ts in all_dates]

# Concatenate with original DataFrame (drop original column)
df_expanded = pd.concat([df.drop(columns=['dailyAllTransactions']), counts], axis=1)

df_expanded.to_csv('wallets_data_expanded.csv', index=False)
print('Expanded wallets data saved to wallets_data_expanded.csv') 