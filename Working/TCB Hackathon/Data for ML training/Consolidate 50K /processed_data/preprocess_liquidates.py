import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import boxcox

# Read the data
df = pd.read_csv('3.liquidates_data_processed.csv')

# Remove specified columns
columns_to_remove = [
    'debtBuyerWallet', 'timestamp', 'collateralAsset',
    'collateralProtocol', 'debtAsset', 'debtProtocol'
]
df = df.drop(columns=columns_to_remove, errors='ignore')

# Handle missing values
numeric_columns = df.select_dtypes(include=[np.number]).columns
categorical_columns = df.select_dtypes(include=['object']).columns

df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].median())
df[categorical_columns] = df[categorical_columns].fillna(df[categorical_columns].mode().iloc[0])

# Remove duplicates based on walletAddress
df = df.drop_duplicates(subset=['walletAddress'])

# Fix data inconsistencies (date formats, categories) - placeholder
# (Add specific fixes if you have date/category columns)

# Skewness handling: log for right-skew, Box-Cox for left-skew
def safe_log(series):
    return np.log1p(series)

def safe_boxcox(series):
    min_val = series.min()
    shift = 1 - min_val if min_val <= 0 else 0
    transformed, _ = boxcox(series + shift)
    return transformed

transformed_cols = []
for col in numeric_columns:
    skew = df[col].skew()
    if skew > 1:
        df[f'{col}_log'] = safe_log(df[col])
        transformed_cols.append((col, f'{col}_log', 'log'))
    elif skew < -1:
        df[f'{col}_boxcox'] = safe_boxcox(df[col])
        transformed_cols.append((col, f'{col}_boxcox', 'boxcox'))

# Outlier removal using IQR on transformed columns
def remove_outliers_transformed(df, col):
    Q1 = df[col].quantile(0.25)
    Q3 = df[col].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    return df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]

for orig, trans, method in transformed_cols:
    df = remove_outliers_transformed(df, trans)

# Visualizations
# 1. Transformed distributions after outlier removal
if transformed_cols:
    plt.figure(figsize=(5 * len(transformed_cols), 4))
    for i, (orig, trans, method) in enumerate(transformed_cols, 1):
        plt.subplot(1, len(transformed_cols), i)
        sns.histplot(df[trans], kde=True)
        plt.title(f'{method.title()} of {orig} (Outliers Removed)')
    plt.tight_layout()
    plt.savefig('liq_transformed_distributions_no_outliers.png')
    plt.close()

# 2. Box plots for transformed columns
if transformed_cols:
    plt.figure(figsize=(5 * len(transformed_cols), 4))
    df[[trans for _, trans, _ in transformed_cols]].boxplot()
    plt.title('Box Plots of Transformed Variables (Outliers Removed)')
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('liq_boxplots_transformed_no_outliers.png')
    plt.close()

# 3. Correlation heatmap for transformed columns
if transformed_cols:
    plt.figure(figsize=(8, 6))
    correlation_matrix = df[[trans for _, trans, _ in transformed_cols]].corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
    plt.title('Correlation Heatmap (Transformed, Outliers Removed)')
    plt.tight_layout()
    plt.savefig('liq_correlation_heatmap_transformed_no_outliers.png')
    plt.close()

# Save the processed data
df.to_csv('liquidates_data_cleaned_no_outliers.csv', index=False)

print("Liquidates data preprocessing completed. Outliers removed after transformation. Visualizations have been saved.")
print("\nSummary of the cleaned, transformed data:")
if transformed_cols:
    print(df[[trans for _, trans, _ in transformed_cols]].describe())
print("\nMissing values after cleaning:")
print(df.isnull().sum()) 