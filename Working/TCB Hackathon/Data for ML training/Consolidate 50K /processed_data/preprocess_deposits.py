import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.stats import boxcox

# Read the data
df = pd.read_csv('2. deposits_data_processed.csv')

# Remove specified columns
columns_to_remove = [
    'lendingPoolAddress', 'timestamp', 'tokenAddress',
    'valueInUSD', 'highestDepositInUSD', 'lowestDepositInUSD', 'averageDepositInUSD'
]
df = df.drop(columns=columns_to_remove, errors='ignore')

# Handle missing values
numeric_columns = df.select_dtypes(include=[np.number]).columns
categorical_columns = df.select_dtypes(include=['object']).columns

df[numeric_columns] = df[numeric_columns].fillna(df[numeric_columns].median())
df[categorical_columns] = df[categorical_columns].fillna(df[categorical_columns].mode().iloc[0])

# Remove duplicates based on walletAddress
df = df.drop_duplicates(subset=['walletAddress'])

# Fix data inconsistencies (date formats, categories) - placeholder
# (Add specific fixes if you have date/category columns)

# Outlier removal using IQR
def remove_outliers(df, column):
    Q1 = df[column].quantile(0.25)
    Q3 = df[column].quantile(0.75)
    IQR = Q3 - Q1
    lower_bound = Q1 - 1.5 * IQR
    upper_bound = Q3 + 1.5 * IQR
    return df[(df[column] >= lower_bound) & (df[column] <= upper_bound)]

for col in numeric_columns:
    df = remove_outliers(df, col)

# Skewness handling: log for right-skew, Box-Cox for left-skew
def safe_log(series):
    return np.log1p(series)

def safe_boxcox(series):
    min_val = series.min()
    shift = 1 - min_val if min_val <= 0 else 0
    transformed, _ = boxcox(series + shift)
    return transformed

transformed_cols = []
for col in numeric_columns:
    skew = df[col].skew()
    if skew > 1:
        df[f'{col}_log'] = safe_log(df[col])
        transformed_cols.append((col, f'{col}_log', 'log'))
    elif skew < -1:
        df[f'{col}_boxcox'] = safe_boxcox(df[col])
        transformed_cols.append((col, f'{col}_boxcox', 'boxcox'))

# Visualizations
# 1. Original distributions
plt.figure(figsize=(5 * len(numeric_columns), 4))
for i, col in enumerate(numeric_columns, 1):
    plt.subplot(1, len(numeric_columns), i)
    sns.histplot(df[col], kde=True)
    plt.title(f'Distribution of {col}')
plt.tight_layout()
plt.savefig('original_distributions.png')
plt.close()

# 2. Transformed distributions
if transformed_cols:
    plt.figure(figsize=(5 * len(transformed_cols), 4))
    for i, (orig, trans, method) in enumerate(transformed_cols, 1):
        plt.subplot(1, len(transformed_cols), i)
        sns.histplot(df[trans], kde=True)
        plt.title(f'{method.title()} of {orig}')
    plt.tight_layout()
    plt.savefig('transformed_distributions.png')
    plt.close()

# 3. Box plots for numeric columns
plt.figure(figsize=(5 * len(numeric_columns), 4))
df[numeric_columns].boxplot()
plt.title('Box Plots of Numeric Variables')
plt.xticks(rotation=45)
plt.tight_layout()
plt.savefig('boxplots.png')
plt.close()

# 4. Correlation heatmap
plt.figure(figsize=(8, 6))
correlation_matrix = df[numeric_columns].corr()
sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
plt.title('Correlation Heatmap')
plt.tight_layout()
plt.savefig('correlation_heatmap.png')
plt.close()

# Save the processed data
df.to_csv('deposits_data_cleaned.csv', index=False)

print("Data preprocessing completed. Visualizations have been saved.")
print("\nSummary of the cleaned data:")
print(df.describe())
print("\nMissing values after cleaning:")
print(df.isnull().sum()) 