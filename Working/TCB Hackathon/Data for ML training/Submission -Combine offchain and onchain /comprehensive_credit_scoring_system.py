#!/usr/bin/env python3
"""
Comprehensive Credit Scoring System
Combines NFCS v1 formula, social scoring, and advanced ML with SHAP explainability
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor, IsolationForest
from sklearn.cluster import DBSCAN, KMeans
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_squared_error
import xgboost as xgb
import shap
import joblib
import warnings
warnings.filterwarnings('ignore')

class CreditScoreXCalculator:
    """
    Enhanced CreditScoreX Calculator implementing the specified formula:
    CreditScoreX_v1 = (
        Repayment_Ratio × 0.30 +
        Repayment_Consistency × 0.25 +
        Deposit_Borrow_Ratio × 0.20 +
        Protocol_Diversity × 0.15 +
        Activity_Frequency × 0.10
    ) × 1000
    """
    
    def __init__(self):
        self.scaler = MinMaxScaler()
        self.weights = {
            'repayment_ratio': 0.30,
            'repayment_consistency': 0.25,
            'deposit_borrow_ratio': 0.20,
            'protocol_diversity': 0.15,
            'activity_frequency': 0.10
        }
        
    def calculate_repayment_ratio(self, total_repaid, total_borrowed):
        """Calculate repayment ratio with safety checks"""
        if total_borrowed == 0:
            return 1.0 if total_repaid == 0 else 0.0
        return min(total_repaid / total_borrowed, 2.0)  # Cap at 200%
    
    def calculate_repayment_consistency(self, num_repays, num_borrows):
        """Calculate repayment consistency"""
        if num_borrows == 0:
            return 1.0 if num_repays == 0 else 0.0
        return min(num_repays / num_borrows, 1.5)  # Cap at 150%
    
    def calculate_deposit_borrow_ratio(self, total_deposited, total_borrowed):
        """Calculate deposit to borrow ratio"""
        if total_borrowed == 0:
            return min(total_deposited / 1000, 5.0)  # Normalize for deposit-only users
        return min(total_deposited / total_borrowed, 10.0)  # Cap at 1000%
    
    def calculate_protocol_diversity(self, unique_protocols, unique_tokens):
        """Calculate protocol diversity score"""
        return min(unique_protocols * unique_tokens, 100)  # Cap at 100
    
    def calculate_activity_frequency(self, total_transactions, days_active):
        """Calculate activity frequency"""
        if days_active == 0:
            return 0.0
        return min(total_transactions / days_active, 10.0)  # Cap at 10 tx/day
    
    def calculate_creditscorex_score(self, wallet_data):
        """
        Calculate CreditScoreX score using the specified formula
        """
        # Extract basic metrics
        total_borrowed = wallet_data.get('total_borrowed', 0)
        total_repaid = wallet_data.get('total_repaid', 0)
        total_deposited = wallet_data.get('total_deposited', 0)
        num_borrows = wallet_data.get('num_borrows', 0)
        num_repays = wallet_data.get('num_repays', 0)
        unique_protocols = wallet_data.get('unique_protocols', 1)
        unique_tokens = wallet_data.get('unique_tokens', 1)
        total_transactions = wallet_data.get('total_transactions', 0)
        days_active = wallet_data.get('days_active', 1)
        
        # Calculate components
        repayment_ratio = self.calculate_repayment_ratio(total_repaid, total_borrowed)
        repayment_consistency = self.calculate_repayment_consistency(num_repays, num_borrows)
        deposit_borrow_ratio = self.calculate_deposit_borrow_ratio(total_deposited, total_borrowed)
        protocol_diversity = self.calculate_protocol_diversity(unique_protocols, unique_tokens)
        activity_frequency = self.calculate_activity_frequency(total_transactions, days_active)
        
        # Normalize components to [0, 1] range
        components = np.array([
            repayment_ratio,
            repayment_consistency, 
            deposit_borrow_ratio,
            protocol_diversity,
            activity_frequency
        ]).reshape(1, -1)
        
        # Fit scaler if not already fitted
        if not hasattr(self.scaler, 'scale_'):
            # Use reasonable ranges for normalization
            self.scaler.fit(np.array([
                [0, 0, 0, 0, 0],      # Min values
                [2, 1.5, 10, 100, 10] # Max values
            ]))
        
        normalized_components = self.scaler.transform(components)[0]
        
        # Apply weights and calculate final score
        weighted_score = (
            normalized_components[0] * self.weights['repayment_ratio'] +
            normalized_components[1] * self.weights['repayment_consistency'] +
            normalized_components[2] * self.weights['deposit_borrow_ratio'] +
            normalized_components[3] * self.weights['protocol_diversity'] +
            normalized_components[4] * self.weights['activity_frequency']
        )
        
        # Scale to 1000 and apply bounds
        nfcs_score = max(0, min(1000, weighted_score * 1000))
        
        return {
            'nfcs_score': nfcs_score,
            'components': {
                'repayment_ratio': repayment_ratio,
                'repayment_consistency': repayment_consistency,
                'deposit_borrow_ratio': deposit_borrow_ratio,
                'protocol_diversity': protocol_diversity,
                'activity_frequency': activity_frequency
            },
            'normalized_components': {
                'repayment_ratio_norm': normalized_components[0],
                'repayment_consistency_norm': normalized_components[1],
                'deposit_borrow_ratio_norm': normalized_components[2],
                'protocol_diversity_norm': normalized_components[3],
                'activity_frequency_norm': normalized_components[4]
            }
        }
    
    def get_credit_rating(self, nfcs_score):
        """Get 7-tier credit rating"""
        if nfcs_score >= 900:
            return "AAA"
        elif nfcs_score >= 800:
            return "AA"
        elif nfcs_score >= 700:
            return "A"
        elif nfcs_score >= 600:
            return "BBB"
        elif nfcs_score >= 500:
            return "BB"
        elif nfcs_score >= 400:
            return "B"
        else:
            return "C"
    
    def get_risk_level(self, nfcs_score):
        """Get risk level assessment"""
        if nfcs_score >= 700:
            return "Low Risk"
        elif nfcs_score >= 500:
            return "Medium Risk"
        elif nfcs_score >= 300:
            return "High Risk"
        else:
            return "Very High Risk"


class SocialScoringIntegrator:
    """
    Integrates social media scores with credit scoring
    """

    def __init__(self):
        self.social_weights = {
            'engagement_score': 0.35,
            'influence_score': 0.25,
            'quality_score': 0.20,
            'activity_score': 0.20
        }

    def calculate_social_score(self, user_data):
        """Calculate comprehensive social score"""
        followers = user_data.get('followersCount', 0)
        following = user_data.get('friendsCount', 0)
        tweets = user_data.get('statusesCount', 0)
        favorites = user_data.get('favouritesCount', 0)
        listed = user_data.get('listedCount', 0)
        media = user_data.get('mediaCount', 0)
        verified = user_data.get('verified', False)
        blue = user_data.get('blue', False)

        # Engagement Score (35%)
        engagement_score = self._calculate_engagement_score(followers, tweets, favorites, media)

        # Influence Score (25%)
        influence_score = self._calculate_influence_score(followers, following, listed)

        # Quality Score (20%)
        quality_score = self._calculate_quality_score(verified, blue, user_data)

        # Activity Score (20%)
        activity_score = self._calculate_activity_score(tweets, favorites)

        # Weighted final score
        social_score = (
            engagement_score * self.social_weights['engagement_score'] +
            influence_score * self.social_weights['influence_score'] +
            quality_score * self.social_weights['quality_score'] +
            activity_score * self.social_weights['activity_score']
        )

        return {
            'social_score': min(100, max(0, social_score)),
            'components': {
                'engagement_score': engagement_score,
                'influence_score': influence_score,
                'quality_score': quality_score,
                'activity_score': activity_score
            }
        }

    def _calculate_engagement_score(self, followers, tweets, favorites, media):
        """Calculate engagement score component"""
        # Logarithmic scaling for large numbers
        follower_score = min(30, np.log10(max(1, followers)) * 5)
        tweet_score = min(25, np.log10(max(1, tweets)) * 4)
        favorite_score = min(25, np.log10(max(1, favorites)) * 4)
        media_score = min(20, np.log10(max(1, media)) * 3)

        return follower_score + tweet_score + favorite_score + media_score

    def _calculate_influence_score(self, followers, following, listed):
        """Calculate influence score component"""
        # Follower to following ratio
        if following == 0:
            ratio_score = 50 if followers > 100 else 25
        else:
            ratio = followers / following
            ratio_score = min(50, ratio * 10)

        # Listed score
        listed_score = min(50, listed * 2)

        return ratio_score + listed_score

    def _calculate_quality_score(self, verified, blue, user_data):
        """Calculate quality score component"""
        verification_score = 0
        if verified:
            verification_score += 40
        if blue:
            verification_score += 30

        # Bio completeness
        bio = user_data.get('rawDescription', '')
        # Handle NaN values and ensure bio is a string
        if pd.isna(bio) or bio is None:
            bio = ''
        else:
            bio = str(bio)
        bio_score = min(30, len(bio) / 10) if bio else 0

        return verification_score + bio_score

    def _calculate_activity_score(self, tweets, favorites):
        """Calculate activity score component"""
        # Activity level based on tweets and favorites
        tweet_activity = min(50, np.log10(max(1, tweets)) * 8)
        favorite_activity = min(50, np.log10(max(1, favorites)) * 8)

        return tweet_activity + favorite_activity


class AdvancedMLPipeline:
    """
    Advanced Machine Learning Pipeline with clustering and anomaly detection
    """

    def __init__(self):
        self.rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
        self.xgb_model = xgb.XGBRegressor(random_state=42)
        self.scaler = StandardScaler()
        self.isolation_forest = IsolationForest(contamination=0.1, random_state=42)
        self.dbscan = DBSCAN(eps=0.5, min_samples=5)
        self.kmeans = KMeans(n_clusters=5, random_state=42)
        self.shap_explainer = None

    def prepare_features(self, df):
        """Prepare features for ML models"""
        feature_columns = [
            'total_borrowed', 'total_repaid', 'total_deposited',
            'num_borrows', 'num_repays', 'unique_protocols', 'unique_tokens',
            'total_transactions', 'days_active', 'social_score',
            'engagement_score', 'influence_score', 'quality_score', 'activity_score'
        ]

        # Fill missing values
        for col in feature_columns:
            if col not in df.columns:
                df[col] = 0

        return df[feature_columns].fillna(0)

    def train_models(self, X, y):
        """Train all ML models"""
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        # Train Random Forest
        self.rf_model.fit(X_train_scaled, y_train)
        rf_pred = self.rf_model.predict(X_test_scaled)
        rf_r2 = r2_score(y_test, rf_pred)

        # Train XGBoost
        self.xgb_model.fit(X_train_scaled, y_train)
        xgb_pred = self.xgb_model.predict(X_test_scaled)
        xgb_r2 = r2_score(y_test, xgb_pred)

        # Train anomaly detection
        self.isolation_forest.fit(X_train_scaled)

        # Train clustering
        self.dbscan.fit(X_train_scaled)
        self.kmeans.fit(X_train_scaled)

        # Initialize SHAP explainer
        self.shap_explainer = shap.TreeExplainer(self.rf_model)

        return {
            'rf_r2': rf_r2,
            'xgb_r2': xgb_r2,
            'X_test': X_test_scaled,
            'y_test': y_test,
            'feature_names': X.columns.tolist()
        }

    def predict_credit_score(self, features):
        """Predict credit score using ensemble"""
        features_scaled = self.scaler.transform(features.reshape(1, -1))

        rf_pred = self.rf_model.predict(features_scaled)[0]
        xgb_pred = self.xgb_model.predict(features_scaled)[0]

        # Ensemble prediction (weighted average)
        ensemble_pred = 0.6 * rf_pred + 0.4 * xgb_pred

        return max(0, min(1000, ensemble_pred))

    def detect_anomalies(self, features):
        """Detect anomalies in user behavior"""
        features_scaled = self.scaler.transform(features.reshape(1, -1))

        anomaly_score = self.isolation_forest.decision_function(features_scaled)[0]
        is_anomaly = self.isolation_forest.predict(features_scaled)[0] == -1

        return {
            'is_anomaly': is_anomaly,
            'anomaly_score': anomaly_score,
            'confidence': abs(anomaly_score)
        }

    def assign_cluster(self, features):
        """Assign user to cluster for new user scoring"""
        features_scaled = self.scaler.transform(features.reshape(1, -1))

        dbscan_cluster = self.dbscan.fit_predict(features_scaled)[0]
        kmeans_cluster = self.kmeans.predict(features_scaled)[0]

        return {
            'dbscan_cluster': dbscan_cluster,
            'kmeans_cluster': kmeans_cluster
        }

    def get_shap_explanation(self, features, feature_names):
        """Get SHAP explanation for prediction"""
        if self.shap_explainer is None:
            return None

        features_scaled = self.scaler.transform(features.reshape(1, -1))
        shap_values = self.shap_explainer.shap_values(features_scaled)

        return {
            'shap_values': shap_values[0],
            'feature_names': feature_names,
            'base_value': self.shap_explainer.expected_value
        }


class TwitterWalletClustering:
    """
    Twitter-Wallet mapping using clustering techniques
    """

    def __init__(self):
        self.clustering_model = KMeans(n_clusters=10, random_state=42)
        self.scaler = StandardScaler()
        self.wallet_mappings = {}

    def extract_wallet_from_bio(self, bio_text):
        """Extract wallet addresses from bio text"""
        import re

        if not bio_text:
            return []

        # Patterns for different wallet types
        patterns = {
            'ethereum': r'0x[a-fA-F0-9]{40}',
            'bitcoin': r'[13][a-km-zA-HJ-NP-Z1-9]{25,34}|bc1[a-z0-9]{39,59}',
            'solana': r'[1-9A-HJ-NP-Za-km-z]{32,44}',
        }

        found_wallets = []
        for network, pattern in patterns.items():
            matches = re.findall(pattern, bio_text)
            for match in matches:
                found_wallets.append({
                    'address': match,
                    'network': network,
                    'confidence': 0.8  # High confidence for bio extraction
                })

        return found_wallets

    def create_user_features(self, user_data):
        """Create feature vector for clustering"""
        features = [
            np.log10(max(1, user_data.get('followersCount', 0))),
            np.log10(max(1, user_data.get('friendsCount', 0))),
            np.log10(max(1, user_data.get('statusesCount', 0))),
            np.log10(max(1, user_data.get('favouritesCount', 0))),
            user_data.get('listedCount', 0),
            user_data.get('mediaCount', 0),
            1 if user_data.get('verified', False) else 0,
            1 if user_data.get('blue', False) else 0,
            len(user_data.get('rawDescription', '')) / 100,  # Bio length normalized
        ]
        return np.array(features)

    def cluster_users(self, twitter_df):
        """Cluster Twitter users for wallet mapping"""
        # Create feature matrix
        feature_matrix = []
        user_ids = []

        for _, user in twitter_df.iterrows():
            features = self.create_user_features(user)
            feature_matrix.append(features)
            user_ids.append(user['_id'])

        feature_matrix = np.array(feature_matrix)

        # Scale features
        feature_matrix_scaled = self.scaler.fit_transform(feature_matrix)

        # Perform clustering
        clusters = self.clustering_model.fit_predict(feature_matrix_scaled)

        # Create cluster mapping
        cluster_mapping = {}
        for user_id, cluster in zip(user_ids, clusters):
            cluster_mapping[user_id] = cluster

        return cluster_mapping

    def map_wallets_to_users(self, twitter_df, wallet_df):
        """Map wallet addresses to Twitter users using clustering"""
        # First, extract wallets from bios
        bio_mappings = []
        for _, user in twitter_df.iterrows():
            bio = user.get('rawDescription', '')
            wallets = self.extract_wallet_from_bio(bio)
            for wallet in wallets:
                bio_mappings.append({
                    'user_id': user['_id'],
                    'wallet_address': wallet['address'],
                    'network': wallet['network'],
                    'mapping_method': 'bio_extraction',
                    'confidence': wallet['confidence']
                })

        # Cluster users
        cluster_mapping = self.cluster_users(twitter_df)

        # Create synthetic mappings based on clusters
        synthetic_mappings = []
        for _, user in twitter_df.iterrows():
            user_id = user['_id']
            cluster = cluster_mapping.get(user_id, 0)

            # Generate synthetic wallet based on cluster characteristics
            if cluster in [0, 1, 2]:  # High-value clusters
                networks = ['ethereum', 'bitcoin']
            elif cluster in [3, 4]:  # Medium-value clusters
                networks = ['ethereum', 'solana']
            else:  # Lower-value clusters
                networks = ['solana', 'polygon']

            for network in networks:
                synthetic_wallet = self._generate_synthetic_wallet(network, user_id)
                synthetic_mappings.append({
                    'user_id': user_id,
                    'wallet_address': synthetic_wallet,
                    'network': network,
                    'mapping_method': 'cluster_based',
                    'confidence': 0.3 + (cluster / 10) * 0.4,  # Confidence based on cluster
                    'cluster': cluster
                })

        # Combine all mappings
        all_mappings = bio_mappings + synthetic_mappings

        return pd.DataFrame(all_mappings)

    def _generate_synthetic_wallet(self, network, user_id):
        """Generate synthetic wallet address for demonstration"""
        import hashlib

        # Create deterministic but realistic-looking addresses
        seed = f"{network}_{user_id}"
        hash_obj = hashlib.sha256(seed.encode())
        hex_hash = hash_obj.hexdigest()

        if network == 'ethereum':
            return f"0x{hex_hash[:40]}"
        elif network == 'bitcoin':
            return f"1{hex_hash[:33]}"
        elif network == 'solana':
            return hex_hash[:44]
        else:
            return f"0x{hex_hash[:40]}"


class ComprehensiveCreditScoringSystem:
    """
    Main system combining NFCS, social scoring, and advanced ML
    """

    def __init__(self):
        self.creditscorex_calculator = CreditScoreXCalculator()
        self.social_integrator = SocialScoringIntegrator()
        self.ml_pipeline = AdvancedMLPipeline()
        self.twitter_clustering = TwitterWalletClustering()
        self.is_trained = False

    def load_and_prepare_data(self, data_path):
        """Load and prepare all data sources"""
        try:
            # Load Twitter social data
            twitter_df = pd.read_csv(f"{data_path}/twitter_users_social_scoring_50K.csv")

            # Load wallet transaction data
            borrows_df = pd.read_csv(f"{data_path}/preprocessed_data_borrows.csv")

            print(f"Loaded {len(twitter_df)} Twitter users")
            print(f"Loaded {len(borrows_df)} borrow transactions")

            return twitter_df, borrows_df

        except Exception as e:
            print(f"Error loading data: {e}")
            return None, None

    def prepare_training_data(self, twitter_df, borrows_df, sample_size=5000):
        """Prepare comprehensive training dataset"""
        print(f"🔄 Preparing training data with {sample_size} samples...")

        # Sample users for training
        sample_twitter = twitter_df.sample(n=min(sample_size, len(twitter_df)), random_state=42)

        training_data = []

        for _, user in sample_twitter.iterrows():
            # Generate realistic wallet data based on social profile
            social_score = self.social_integrator.calculate_social_score(user)['social_score']

            # Create synthetic but realistic wallet data
            # Higher social scores tend to correlate with better financial behavior
            base_multiplier = 1 + (social_score / 100)

            wallet_data = {
                'total_borrowed': np.random.uniform(1000, 100000) * base_multiplier,
                'total_repaid': np.random.uniform(800, 95000) * base_multiplier,
                'total_deposited': np.random.uniform(2000, 200000) * base_multiplier,
                'num_borrows': np.random.randint(1, 25),
                'num_repays': np.random.randint(1, 23),
                'unique_protocols': np.random.randint(1, 12),
                'unique_tokens': np.random.randint(1, 10),
                'total_transactions': np.random.randint(5, 150),
                'days_active': np.random.randint(30, 500)
            }

            # Calculate CreditScoreX score as target
            nfcs_result = self.creditscorex_calculator.calculate_creditscorex_score(wallet_data)
            target_score = nfcs_result['nfcs_score']

            # Add social components
            social_result = self.social_integrator.calculate_social_score(user)

            # Combine all features
            features = {
                'total_borrowed': wallet_data['total_borrowed'],
                'total_repaid': wallet_data['total_repaid'],
                'total_deposited': wallet_data['total_deposited'],
                'num_borrows': wallet_data['num_borrows'],
                'num_repays': wallet_data['num_repays'],
                'unique_protocols': wallet_data['unique_protocols'],
                'unique_tokens': wallet_data['unique_tokens'],
                'total_transactions': wallet_data['total_transactions'],
                'days_active': wallet_data['days_active'],
                'social_score': social_result['social_score'],
                'engagement_score': social_result['components']['engagement_score'],
                'influence_score': social_result['components']['influence_score'],
                'quality_score': social_result['components']['quality_score'],
                'activity_score': social_result['components']['activity_score'],
                'target_score': target_score
            }

            training_data.append(features)

        training_df = pd.DataFrame(training_data)
        print(f"✅ Training data prepared: {len(training_df)} samples with {len(training_df.columns)-1} features")

        return training_df

    def train_ml_models(self, twitter_df, borrows_df, sample_size=5000):
        """Train all ML models with comprehensive data"""
        print("🚀 Starting ML Model Training...")
        print("=" * 50)

        # Prepare training data
        training_df = self.prepare_training_data(twitter_df, borrows_df, sample_size)

        # Separate features and target
        feature_columns = [col for col in training_df.columns if col != 'target_score']
        X = training_df[feature_columns]
        y = training_df['target_score']

        print(f"📊 Training with {len(X)} samples and {len(feature_columns)} features")
        print(f"🎯 Target range: {y.min():.1f} - {y.max():.1f}")

        # Train models
        training_results = self.ml_pipeline.train_models(X, y)

        # Mark as trained
        self.is_trained = True

        print("\n🎉 ML Model Training Complete!")
        print(f"📈 Random Forest R²: {training_results['rf_r2']:.4f}")
        print(f"📈 XGBoost R²: {training_results['xgb_r2']:.4f}")
        print("✅ Anomaly detection model trained")
        print("✅ Clustering models trained")
        print("✅ SHAP explainer initialized")

        return training_results

    def save_trained_models(self, filepath="trained_models.pkl"):
        """Save trained models to file"""
        if not self.is_trained:
            print("❌ No trained models to save!")
            return False

        import pickle

        model_data = {
            'ml_pipeline': self.ml_pipeline,
            'creditscorex_calculator': self.creditscorex_calculator,
            'social_integrator': self.social_integrator,
            'twitter_clustering': self.twitter_clustering,
            'is_trained': self.is_trained
        }

        try:
            with open(filepath, 'wb') as f:
                pickle.dump(model_data, f)
            print(f"✅ Models saved to {filepath}")
            return True
        except Exception as e:
            print(f"❌ Failed to save models: {e}")
            return False

    def load_trained_models(self, filepath="trained_models.pkl"):
        """Load trained models from file"""
        import pickle
        import os
        import joblib

        # Try to load the new on-chain models first
        onchain_models = [
            "onchain_credit_models_rf.pkl",
            "onchain_credit_models_xgb.pkl",
            "onchain_credit_models_scaler.pkl",
            "onchain_credit_models_isolation.pkl",
            "onchain_credit_models_kmeans.pkl"
        ]

        if all(os.path.exists(f) for f in onchain_models):
            try:
                print("🔄 Loading on-chain trained models...")
                self.ml_pipeline.rf_model = joblib.load("onchain_credit_models_rf.pkl")
                self.ml_pipeline.xgb_model = joblib.load("onchain_credit_models_xgb.pkl")
                self.ml_pipeline.scaler = joblib.load("onchain_credit_models_scaler.pkl")
                self.ml_pipeline.isolation_forest = joblib.load("onchain_credit_models_isolation.pkl")
                self.ml_pipeline.kmeans = joblib.load("onchain_credit_models_kmeans.pkl")

                # Initialize SHAP explainer
                import shap
                self.ml_pipeline.shap_explainer = shap.TreeExplainer(self.ml_pipeline.rf_model)

                self.is_trained = True
                print("✅ On-chain models loaded successfully!")
                return True
            except Exception as e:
                print(f"⚠️ Failed to load on-chain models: {e}")

        # Fallback to old model format
        if not os.path.exists(filepath):
            print(f"❌ Model file {filepath} not found!")
            return False

        try:
            with open(filepath, 'rb') as f:
                model_data = pickle.load(f)

            self.ml_pipeline = model_data['ml_pipeline']
            self.creditscorex_calculator = model_data.get('creditscorex_calculator', model_data.get('nfcs_calculator'))
            self.social_integrator = model_data['social_integrator']
            self.twitter_clustering = model_data['twitter_clustering']
            self.is_trained = model_data['is_trained']

            print(f"✅ Models loaded from {filepath}")
            return True
        except Exception as e:
            print(f"❌ Failed to load models: {e}")
            return False

    def calculate_comprehensive_score(self, wallet_data, social_data=None):
        """Calculate comprehensive credit score"""
        # Calculate CreditScoreX score
        nfcs_result = self.creditscorex_calculator.calculate_creditscorex_score(wallet_data)

        # Calculate social score if data available
        social_result = None
        if social_data is not None and not (hasattr(social_data, 'empty') and social_data.empty):
            # Convert pandas Series to dict if needed
            if hasattr(social_data, 'to_dict'):
                social_data_dict = social_data.to_dict()
            else:
                social_data_dict = social_data
            social_result = self.social_integrator.calculate_social_score(social_data_dict)

        # Combine scores
        base_score = nfcs_result['nfcs_score']

        if social_result:
            # Weight: 70% NFCS, 30% Social
            combined_score = (base_score * 0.7) + (social_result['social_score'] * 3.0)  # Scale social to 300 max
            combined_score = min(1000, combined_score)
        else:
            combined_score = base_score

        # Get ML prediction if trained
        ml_prediction = None
        anomaly_detection = None
        cluster_assignment = None

        if self.is_trained:
            # Prepare features for ML - match the 17 features from on-chain training
            features = np.array([
                wallet_data.get('total_borrowed', 0),  # totalAmountOfBorrowInUSD
                wallet_data.get('total_repaid', 0),    # totalAmountOfRepayInUSD
                wallet_data.get('total_deposited', 0), # totalAmountOfDepositInUSD
                wallet_data.get('total_deposited', 0) * 0.7, # totalAmountOfWithdrawInUSD (synthetic)
                0,  # totalAmountOfLiquidateInUSD (rare, default to 0)
                wallet_data.get('num_borrows', 0),     # totalNumberOfBorrow
                wallet_data.get('num_repays', 0),      # totalNumberOfRepay
                wallet_data.get('num_borrows', 0) * 1.5, # totalNumberOfDeposit (synthetic)
                wallet_data.get('num_borrows', 0) * 1.2, # totalNumberOfWithdraw (synthetic)
                0,  # totalNumberOfLiquidate (rare, default to 0)
                nfcs_result['components']['repayment_ratio'],      # repayment_ratio
                nfcs_result['components']['repayment_consistency'], # repayment_consistency
                nfcs_result['components']['deposit_borrow_ratio'],  # deposit_borrow_ratio
                nfcs_result['components']['protocol_diversity'],    # protocol_diversity
                nfcs_result['components']['activity_frequency'],    # activity_frequency
                wallet_data.get('total_transactions', 0),          # total_transactions
                wallet_data.get('total_borrowed', 0) + wallet_data.get('total_deposited', 0), # total_volume
            ])

            ml_prediction = self.ml_pipeline.predict_credit_score(features)
            anomaly_detection = self.ml_pipeline.detect_anomalies(features)
            cluster_assignment = self.ml_pipeline.assign_cluster(features)

        return {
            'comprehensive_score': combined_score,
            'nfcs_score': base_score,
            'social_score': social_result['social_score'] if social_result else None,
            'ml_prediction': ml_prediction,
            'credit_rating': self.creditscorex_calculator.get_credit_rating(combined_score),
            'risk_level': self.creditscorex_calculator.get_risk_level(combined_score),
            'anomaly_detection': anomaly_detection,
            'cluster_assignment': cluster_assignment,
            'components': {
                'nfcs_components': nfcs_result['components'],
                'social_components': social_result['components'] if social_result else None
            }
        }
