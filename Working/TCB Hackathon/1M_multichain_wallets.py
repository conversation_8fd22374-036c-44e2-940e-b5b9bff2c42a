from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["multichain_wallets"]

# --- Step 1: Get total number of flags from the config collection ---
configs = db["configs"]
flag_state = configs.find_one({"_id": "multichain_wallets_flagged_state"})
num_flags = flag_state.get("batch_idx", 0)
print(f"Total flags: {num_flags}")

# --- Step 2: Project only required fields ---
fields_to_project = {
    "_id": 1,
    "address": 1,
    "lastUpdatedAt": 1,
    "balanceInUSD": 1,
    "depositInUSD": 1,
    "borrowInUSD": 1,
    "totalValueOfLiquidation": 1,
    "numberOfLiquidation": 1,
    "flagged": 1,
    # Add more fields if needed
}

# --- Step 3: Loop through flags and filter in Python ---
max_docs = 10000
all_docs = []
total_collected = 0

for flag in range(1, num_flags + 1):
    print(f"🔎 Fetching flagged = {flag}")
    cursor = collection.find({"flagged": flag}, fields_to_project)

    for doc in cursor:
        try:
            if doc.get("balanceInUSD", 0) >= 1_000_000:
                all_docs.append(doc)
                total_collected += 1
        except:
            continue

        if total_collected >= max_docs:
            break
    
    if total_collected >= max_docs:
        break

print(f"✅ Collected {total_collected} wallets with balance >= $1M")

# --- Step 4: Export to CSV ---
df = pd.DataFrame(all_docs)
df.to_csv("wallets_over_1M_10K.csv", index=False)
print("📁 Exported 10000 to wallets_over_1M.csv")
