# Credit Scoring System

This system provides a comprehensive credit scoring solution that combines social media activity and blockchain financial transactions to assess creditworthiness. The system includes:

1. A machine learning pipeline for credit score prediction
2. An autoencoder for dimensionality reduction
3. A user-friendly web interface for testing credit scores
4. Detailed visualizations and explanations of predictions

## Features

- Credit score prediction (300-850 scale)
- Default probability estimation
- Social media influence analysis
- Financial health indicators
- Interactive web interface
- SHAP-based feature importance
- Autoencoder-based feature extraction
- SMOTE for handling class imbalance

## Installation

1. Clone the repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

1. Train the model:
```bash
python enhanced_credit_scoring.py
```

2. Launch the web interface:
```bash
streamlit run credit_score_ui.py
```

## Input Parameters

### Social Media Metrics
- Number of Followers
- Number of Tweets
- Number of Favorites
- Number of Friends
- Number of Lists
- Number of Media Posts
- Verified Account Status
- Blue Checkmark Status

### Financial Metrics
- Balance in USD
- Daily Number of Transactions
- Total Amount Borrowed
- Total Amount Repaid
- Total Amount Deposited

## Output

The system provides:
1. Credit Score (300-850)
2. Default Probability
3. Risk Level (Low/Medium/High)
4. Score Breakdown
5. Recommendations for Improvement
6. Sample Data Visualization

## Files

- `enhanced_credit_scoring.py`: Main pipeline for model training and evaluation
- `credit_score_ui.py`: Streamlit web interface
- `requirements.txt`: Python dependencies
- `credit_scores.csv`: Generated credit scores
- `shap_importance.png`: Feature importance visualization
- `credit_score_distribution.png`: Distribution of credit scores

## Model Architecture

The system uses a combination of:
1. Random Forest Classifier
2. XGBoost Classifier
3. Autoencoder for feature extraction
4. SMOTE for handling class imbalance

## Credit Score Calculation

The credit score (300-850) is calculated based on:
1. Base score from model probability
2. Financial health indicators
3. Social trust indicators
4. Transaction history
5. Account balance

## Contributing

Feel free to submit issues and enhancement requests! 