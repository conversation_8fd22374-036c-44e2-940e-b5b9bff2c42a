import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.cluster import KMeans, DBSCAN
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.decomposition import PCA
import xgboost as xgb
import shap
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Set random seed for reproducibility
np.random.seed(42)

def load_and_preprocess_data():
    """
    Load and preprocess all datasets
    """
    print("Loading datasets...")
    
    # Load social data
    social_df = pd.read_csv('twitter_users_social_scoring_50K.csv')
    
    # Load financial data
    borrows_df = pd.read_csv('knowledge_graph_borrows_50K_.csv')
    deposits_df = pd.read_csv('knowledge_graph_deposits_50K.csv')
    repays_df = pd.read_csv('knowledge_graph_repays_50K.csv')
    liquidates_df = pd.read_csv('knowledge_graph_liquidiates_50K.csv')
    wallets_df = pd.read_csv('knowledge_graph_multichain_wallets_50k.csv')
    
    # Merge financial data
    print("Merging financial data...")
    financial_df = pd.merge(borrows_df, deposits_df, on='walletAddress', how='outer', suffixes=('_borrow', '_deposit'))
    financial_df = pd.merge(financial_df, repays_df, on='walletAddress', how='outer', suffixes=('', '_repay'))
    
    # Process liquidates data
    liquidates_df['numberOfLiquidation'] = 1
    liquidates_df['totalValueOfLiquidation'] = liquidates_df['liquidationLogs.liquidatedWallet.1676012980.debtAssetInUSD'].fillna(0)
    liquidates_df = liquidates_df.groupby('liquidatedWallet').agg({
        'numberOfLiquidation': 'sum',
        'totalValueOfLiquidation': 'sum'
    }).reset_index()
    liquidates_df = liquidates_df.rename(columns={'liquidatedWallet': 'walletAddress'})
    
    # Merge with liquidates data
    financial_df = pd.merge(financial_df, liquidates_df, on='walletAddress', how='outer', suffixes=('', '_liquidate'))
    
    # Merge with wallets data
    financial_df = pd.merge(financial_df, wallets_df, left_on='walletAddress', right_on='address', how='outer')
    
    # Fill missing values
    financial_df = financial_df.fillna(0)

    print('Columns in financial_df after merging:', financial_df.columns.tolist())
    
    # Use the correct liquidation columns
    if 'numberOfLiquidation_y' in financial_df.columns:
        financial_df['numberOfLiquidation'] = financial_df['numberOfLiquidation_y']
    elif 'numberOfLiquidation_x' in financial_df.columns:
        financial_df['numberOfLiquidation'] = financial_df['numberOfLiquidation_x']
    else:
        financial_df['numberOfLiquidation'] = 0

    if 'totalValueOfLiquidation_y' in financial_df.columns:
        financial_df['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation_y']
    elif 'totalValueOfLiquidation_x' in financial_df.columns:
        financial_df['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation_x']
    else:
        financial_df['totalValueOfLiquidation'] = 0

    return social_df, financial_df

def engineer_features(social_df, financial_df):
    """
    Create features for credit scoring
    """
    print("Engineering features...")
    
    # Social features
    social_features = social_df[[
        'followersCount', 'statusesCount', 'favouritesCount',
        'friendsCount', 'listedCount', 'mediaCount', 'verified', 'blue'
    ]].copy()
    
    # Calculate account age
    created_dates = pd.to_datetime(social_df['created'], utc=True)
    now_utc = pd.Timestamp.now(tz='UTC')
    social_features['account_age_days'] = (now_utc - created_dates).dt.days
    
    # Calculate engagement ratios
    social_features['engagement_ratio'] = social_features['favouritesCount'] / (social_features['followersCount'] + 1)
    social_features['social_influence_score'] = (
        social_features['followersCount'] * 0.5 +
        social_features['listedCount'] * 0.3 +
        social_features['mediaCount'] * 0.2
    )
    social_features['verified_status'] = (social_features['verified'] | social_features['blue']).astype(int)
    
    # Financial features
    financial_features = financial_df[[
        'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD',
        'totalNumberOfRepay', 'totalAmountOfRepayInUSD',
        'totalNumberOfDeposit', 'totalAmountOfDepositInUSD',
        'numberOfLiquidation', 'totalValueOfLiquidation',
        'balanceInUSD', 'dailyNumberOfTransactions'
    ]].copy()
    
    # Calculate financial ratios
    financial_features['borrow_to_repay_ratio'] = (
        financial_features['totalAmountOfBorrowInUSD'] / 
        (financial_features['totalAmountOfRepayInUSD'] + 1)
    )
    financial_features['liquidation_risk'] = (
        financial_features['numberOfLiquidation'] > 0
    ).astype(int)
    financial_features['debt_to_asset_ratio'] = (
        financial_features['totalAmountOfBorrowInUSD'] / 
        (financial_features['balanceInUSD'] + 1)
    )
    
    # Combine features
    features = pd.concat([social_features, financial_features], axis=1)
    
    # Handle infinite values and outliers
    features = features.replace([np.inf, -np.inf], np.nan)
    numeric_cols = features.select_dtypes(include=[np.number]).columns
    features[numeric_cols] = features[numeric_cols].fillna(features[numeric_cols].mean())
    
    # Select only numeric columns for modeling
    features = features.select_dtypes(include=[np.number])
    return features

def prepare_target_variable(financial_df):
    """
    Create target variable based on liquidation events
    """
    print("Preparing target variable...")
    
    # Create binary target: 1 if user has been liquidated, 0 otherwise
    target = (financial_df['numberOfLiquidation'] > 0).astype(int)
    
    return target

def check_target_distribution(target):
    print("\nTarget Distribution:")
    print(target.value_counts(normalize=True))
    plt.figure(figsize=(4,2))
    sns.countplot(x=target)
    plt.title('Target Distribution')
    plt.savefig('target_distribution.png')
    plt.close()

def remove_leaky_features(features, target):
    # Remove features that are direct proxies for the target
    # Remove any feature with correlation > 0.95 with the target
    corrs = features.corrwith(target).abs()
    leaky = corrs[corrs > 0.95].index.tolist()
    print(f"\nRemoving leaky features: {leaky}")
    features = features.drop(columns=leaky, errors='ignore')
    return features

def plot_feature_correlations(features, target):
    # Plot correlation matrix
    plt.figure(figsize=(12,10))
    corr = features.corr()
    sns.heatmap(corr, cmap='coolwarm', center=0)
    plt.title('Feature Correlation Matrix')
    plt.tight_layout()
    plt.savefig('feature_correlation_matrix.png')
    plt.close()
    # Print top 10 features correlated with target
    corrs = features.corrwith(target).abs().sort_values(ascending=False)
    print("\nTop 10 features correlated with target:")
    print(corrs.head(10))

def cross_validate_model(model, X, y):
    print("\nCross-validating model...")
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    scores = cross_val_score(model, X, y, cv=cv, scoring='roc_auc')
    print(f"Mean ROC-AUC: {scores.mean():.3f} (+/- {scores.std():.3f})")
    return scores

def run_kmeans_clustering(features, n_clusters=3):
    print("\nRunning K-Means clustering...")
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(features)
    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
    clusters = kmeans.fit_predict(X_scaled)
    plt.figure(figsize=(6,4))
    plt.hist(clusters, bins=n_clusters, rwidth=0.8)
    plt.title('K-Means Cluster Distribution')
    plt.xlabel('Cluster Label')
    plt.ylabel('Number of Users')
    plt.savefig('kmeans_cluster_distribution.png')
    plt.close()
    return clusters

def main():
    # Load and preprocess data
    social_df, financial_df = load_and_preprocess_data()
    
    # Engineer features
    features = engineer_features(social_df, financial_df)
    
    # Prepare target variable
    target = prepare_target_variable(financial_df)
    
    # 1. Check target distribution
    check_target_distribution(target)
    
    # 2. Remove leaky features
    features = remove_leaky_features(features, target)
    
    # 3. Visualize feature correlations
    plot_feature_correlations(features, target)
    
    # 4. Train/test split
    X_train, X_test, y_train, y_test = train_test_split(
        features, target, test_size=0.2, random_state=42, stratify=target
    )
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # 5. Cross-validation
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    cross_validate_model(rf_model, X_train_scaled, y_train)
    xgb_model = xgb.XGBClassifier(random_state=42)
    cross_validate_model(xgb_model, X_train_scaled, y_train)
    
    # 6. Train models
    rf_model.fit(X_train_scaled, y_train)
    xgb_model.fit(X_train_scaled, y_train)
    
    # 7. Evaluate models
    print("\nEvaluating models...")
    for name, model in zip(['Random Forest', 'XGBoost'], [rf_model, xgb_model]):
        pred = model.predict(X_test_scaled)
        proba = model.predict_proba(X_test_scaled)[:, 1]
        print(f"\n{name} Performance:")
        print(f"Accuracy: {accuracy_score(y_test, pred):.3f}")
        print(f"Precision: {precision_score(y_test, pred):.3f}")
        print(f"Recall: {recall_score(y_test, pred):.3f}")
        print(f"F1 Score: {f1_score(y_test, pred):.3f}")
        print(f"ROC AUC: {roc_auc_score(y_test, proba):.3f}")
    
    # 8. SHAP explanations
    print("\nGenerating SHAP explanations...")
    rf_explainer = shap.TreeExplainer(rf_model)
    rf_shap_values = rf_explainer.shap_values(X_test_scaled)
    plt.figure(figsize=(10, 6))
    shap.summary_plot(rf_shap_values, X_test, plot_type="bar", show=False)
    plt.title("SHAP Feature Importance (Random Forest)")
    plt.tight_layout()
    plt.savefig('shap_importance.png')
    plt.close()
    
    xgb_explainer = shap.TreeExplainer(xgb_model)
    xgb_shap_values = xgb_explainer.shap_values(X_test_scaled)
    plt.figure(figsize=(10, 6))
    shap.summary_plot(xgb_shap_values, X_test, plot_type="bar", show=False)
    plt.title("SHAP Feature Importance (XGBoost)")
    plt.tight_layout()
    plt.savefig('shap_importance_xgb.png')
    plt.close()
    
    # 9. K-Means clustering
    clusters = run_kmeans_clustering(features, n_clusters=3)
    print("\nCluster counts:", np.bincount(clusters))
    
    print("\nPipeline complete. Check generated PNGs for visualizations and feature importances.")

if __name__ == "__main__":
    main() 