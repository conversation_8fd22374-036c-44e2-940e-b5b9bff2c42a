import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split, cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
from sklearn.cluster import KMeans
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, roc_auc_score
from sklearn.decomposition import PCA
from imblearn.over_sampling import SMOTE
import xgboost as xgb
import shap
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import tensorflow as tf
from tensorflow.keras.layers import Input, Dense
from tensorflow.keras.models import Model
import warnings
import joblib
warnings.filterwarnings('ignore')

np.random.seed(42)
tf.random.set_seed(42)

def create_autoencoder(input_dim, encoding_dim=32):
    """Create an autoencoder model for dimensionality reduction"""
    # Encoder
    input_layer = Input(shape=(input_dim,))
    encoded = Dense(128, activation='relu')(input_layer)
    encoded = Dense(64, activation='relu')(encoded)
    encoded = Dense(encoding_dim, activation='relu')(encoded)
    
    # Decoder
    decoded = Dense(64, activation='relu')(encoded)
    decoded = Dense(128, activation='relu')(decoded)
    decoded = Dense(input_dim, activation='sigmoid')(decoded)
    
    # Autoencoder
    autoencoder = Model(input_layer, decoded)
    encoder = Model(input_layer, encoded)
    
    autoencoder.compile(optimizer='adam', loss='mse')
    return autoencoder, encoder

def calculate_credit_score(probabilities, features, scaler):
    """Calculate credit score (300-850) based on model probabilities and features"""
    # Base score from model probability (0-1 to 300-850)
    base_score = 300 + (probabilities * 550)
    
    # Feature-based adjustments
    feature_scores = pd.DataFrame()
    n = len(probabilities)
    
    # Helper to get column or default
    def get_col(col, default=0):
        if col in features.columns:
            return features[col]
        else:
            return pd.Series([default]*n, index=features.index)
    
    # Financial health indicators
    feature_scores['debt_to_asset_ratio'] = get_col('debt_to_asset_ratio').apply(
        lambda x: -50 if x > 0.8 else (-25 if x > 0.5 else 0)
    )
    
    feature_scores['transaction_frequency'] = get_col('dailyNumberOfTransactions').apply(
        lambda x: 25 if x > 10 else (10 if x > 5 else 0)
    )
    
    feature_scores['balance_score'] = get_col('balanceInUSD').apply(
        lambda x: 50 if x > 10000 else (25 if x > 1000 else 0)
    )
    
    # Social trust indicators
    feature_scores['social_score'] = get_col('social_influence_score').apply(
        lambda x: 25 if x > 1000 else (10 if x > 100 else 0)
    )
    
    feature_scores['account_age_score'] = get_col('account_age_days').apply(
        lambda x: 25 if x > 365 else (10 if x > 180 else 0)
    )
    
    # Calculate final score
    final_score = base_score + feature_scores.sum(axis=1)
    
    # Ensure score is within bounds
    final_score = final_score.clip(300, 850)
    
    return final_score

def load_and_preprocess_data():
    """Load and preprocess all datasets"""
    print("Loading datasets...")
    
    # Load social data
    social_df = pd.read_csv('twitter_users_social_scoring_50K.csv')
    
    # Load financial data
    borrows_df = pd.read_csv('knowledge_graph_borrows_50K_.csv')
    deposits_df = pd.read_csv('knowledge_graph_deposits_50K.csv')
    repays_df = pd.read_csv('knowledge_graph_repays_50K.csv')
    liquidates_df = pd.read_csv('knowledge_graph_liquidiates_50K.csv')
    wallets_df = pd.read_csv('knowledge_graph_multichain_wallets_50k.csv')
    
    # Merge financial data
    print("Merging financial data...")
    financial_df = pd.merge(borrows_df, deposits_df, on='walletAddress', how='outer', suffixes=('_borrow', '_deposit'))
    financial_df = pd.merge(financial_df, repays_df, on='walletAddress', how='outer', suffixes=('', '_repay'))
    
    # Process liquidates data
    liquidates_df['numberOfLiquidation'] = 1
    liquidates_df['totalValueOfLiquidation'] = liquidates_df['liquidationLogs.liquidatedWallet.1676012980.debtAssetInUSD'].fillna(0)
    liquidates_df = liquidates_df.groupby('liquidatedWallet').agg({
        'numberOfLiquidation': 'sum',
        'totalValueOfLiquidation': 'sum'
    }).reset_index()
    liquidates_df = liquidates_df.rename(columns={'liquidatedWallet': 'walletAddress'})
    
    # Merge with liquidates data
    financial_df = pd.merge(financial_df, liquidates_df, on='walletAddress', how='outer', suffixes=('', '_liquidate'))
    
    # Merge with wallets data
    financial_df = pd.merge(financial_df, wallets_df, left_on='walletAddress', right_on='address', how='outer')
    
    # Fill missing values
    financial_df = financial_df.fillna(0)
    
    # Print column names for debugging
    print("\nAvailable columns in financial_df:")
    print(financial_df.columns.tolist())
    
    return social_df, financial_df

def engineer_features(social_df, financial_df):
    """Create features for credit scoring"""
    print("Engineering features...")
    
    # Social features
    social_features = social_df[[
        'followersCount', 'statusesCount', 'favouritesCount',
        'friendsCount', 'listedCount', 'mediaCount', 'verified', 'blue'
    ]].copy()
    
    # Calculate account age
    created_dates = pd.to_datetime(social_df['created'], utc=True)
    now_utc = pd.Timestamp.now(tz='UTC')
    social_features['account_age_days'] = (now_utc - created_dates).dt.days
    
    # Calculate engagement ratios
    social_features['engagement_ratio'] = social_features['favouritesCount'] / (social_features['followersCount'] + 1)
    social_features['social_influence_score'] = (
        social_features['followersCount'] * 0.5 +
        social_features['listedCount'] * 0.3 +
        social_features['mediaCount'] * 0.2
    )
    social_features['verified_status'] = (social_features['verified'] | social_features['blue']).astype(int)
    
    # Financial features
    financial_features = financial_df[[
        'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD',
        'totalNumberOfRepay', 'totalAmountOfRepayInUSD',
        'totalNumberOfDeposit', 'totalAmountOfDepositInUSD',
        'balanceInUSD', 'dailyNumberOfTransactions'
    ]].copy()
    
    # Add liquidation features if they exist
    if 'numberOfLiquidation' in financial_df.columns:
        financial_features['numberOfLiquidation'] = financial_df['numberOfLiquidation']
    elif 'numberOfLiquidation_x' in financial_df.columns:
        financial_features['numberOfLiquidation'] = financial_df['numberOfLiquidation_x']
    elif 'numberOfLiquidation_y' in financial_df.columns:
        financial_features['numberOfLiquidation'] = financial_df['numberOfLiquidation_y']
    else:
        financial_features['numberOfLiquidation'] = 0
        
    if 'totalValueOfLiquidation' in financial_df.columns:
        financial_features['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation']
    elif 'totalValueOfLiquidation_x' in financial_df.columns:
        financial_features['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation_x']
    elif 'totalValueOfLiquidation_y' in financial_df.columns:
        financial_features['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation_y']
    else:
        financial_features['totalValueOfLiquidation'] = 0
    
    # Calculate financial ratios
    financial_features['borrow_to_repay_ratio'] = (
        financial_features['totalAmountOfBorrowInUSD'] / 
        (financial_features['totalAmountOfRepayInUSD'] + 1)
    )
    financial_features['liquidation_risk'] = (
        financial_features['numberOfLiquidation'] > 0
    ).astype(int)
    financial_features['debt_to_asset_ratio'] = (
        financial_features['totalAmountOfBorrowInUSD'] / 
        (financial_features['balanceInUSD'] + 1)
    )
    
    # Combine features
    features = pd.concat([social_features, financial_features], axis=1)
    
    # Ensure all columns needed for scoring are present
    required_cols = [
        'debt_to_asset_ratio', 'dailyNumberOfTransactions', 'balanceInUSD',
        'social_influence_score', 'account_age_days'
    ]
    for col in required_cols:
        if col not in features.columns:
            features[col] = 0
    
    # Handle infinite values and outliers
    features = features.replace([np.inf, -np.inf], np.nan)
    numeric_cols = features.select_dtypes(include=[np.number]).columns
    features[numeric_cols] = features[numeric_cols].fillna(features[numeric_cols].mean())
    
    # Select only numeric columns for modeling
    features = features.select_dtypes(include=[np.number])
    
    return features

def prepare_target_variable(financial_df):
    """Create target variable based on liquidation events"""
    print("Preparing target variable...")
    # Try to find the correct liquidation column
    liquidation_col = None
    for col in ['numberOfLiquidation', 'numberOfLiquidation_x', 'numberOfLiquidation_y']:
        if col in financial_df.columns:
            liquidation_col = col
            break
    if liquidation_col is None:
        print("Warning: No liquidation column found. Defaulting to zeros.")
        target = pd.Series(0, index=financial_df.index)
    else:
        target = (financial_df[liquidation_col] > 0).astype(int)
    return target

def main():
    # Load and preprocess data
    social_df, financial_df = load_and_preprocess_data()
    
    # Engineer features
    features = engineer_features(social_df, financial_df)
    
    # Prepare target variable
    target = prepare_target_variable(financial_df)
    
    # Remove leaky features
    corrs = features.corrwith(target).abs()
    leaky = corrs[corrs > 0.95].index.tolist()
    print(f"\nRemoving leaky features: {leaky}")
    features = features.drop(columns=leaky, errors='ignore')
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        features, target, test_size=0.2, random_state=42, stratify=target
    )
    # Save a copy of the original X_test before any further processing
    X_test_original = X_test.copy()
    
    # Scale features
    scaler = StandardScaler()
    X_train_scaled = scaler.fit_transform(X_train)
    X_test_scaled = scaler.transform(X_test)
    
    # Save scaler
    joblib.dump(scaler, 'scaler.joblib')
    
    # Apply SMOTE to balance the training data
    smote = SMOTE(random_state=42)
    X_train_balanced, y_train_balanced = smote.fit_resample(X_train_scaled, y_train)
    
    # Train autoencoder
    print("\nTraining autoencoder...")
    autoencoder, encoder = create_autoencoder(X_train_scaled.shape[1])
    autoencoder.fit(
        X_train_scaled, X_train_scaled,
        epochs=50,
        batch_size=256,
        validation_split=0.2,
        verbose=0
    )
    
    # Get encoded features
    X_train_encoded = encoder.predict(X_train_scaled)
    X_test_encoded = encoder.predict(X_test_scaled)
    
    # Train models on both original and encoded features
    print("\nTraining models...")
    
    # Random Forest
    rf_model = RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42)
    rf_model.fit(X_train_balanced, y_train_balanced)
    
    # Save Random Forest model
    joblib.dump(rf_model, 'rf_model.joblib')
    
    # XGBoost
    xgb_model = xgb.XGBClassifier(
        n_estimators=200,
        max_depth=6,
        learning_rate=0.1,
        random_state=42
    )
    xgb_model.fit(X_train_balanced, y_train_balanced)
    
    # Save XGBoost model
    joblib.dump(xgb_model, 'xgb_model.joblib')
    
    # Evaluate models
    print("\nEvaluating models...")
    for name, model in zip(['Random Forest', 'XGBoost'], [rf_model, xgb_model]):
        pred = model.predict(X_test_scaled)
        proba = model.predict_proba(X_test_scaled)[:, 1]
        print(f"\n{name} Performance:")
        print(f"Accuracy: {accuracy_score(y_test, pred):.3f}")
        print(f"Precision: {precision_score(y_test, pred):.3f}")
        print(f"Recall: {recall_score(y_test, pred):.3f}")
        print(f"F1 Score: {f1_score(y_test, pred):.3f}")
        print(f"ROC AUC: {roc_auc_score(y_test, proba):.3f}")
    
    # Calculate credit scores
    print("\nCalculating credit scores...")
    rf_proba = rf_model.predict_proba(X_test_scaled)[:, 1]
    credit_scores = calculate_credit_score(rf_proba, X_test_original, scaler)
    
    # Create results DataFrame
    results_df = pd.DataFrame({
        'walletAddress': financial_df['walletAddress'].iloc[X_test.index],
        'credit_score': credit_scores,
        'default_probability': rf_proba,
        'actual_default': y_test.values
    })
    
    # Save results
    results_df.to_csv('credit_scores.csv', index=False)
    print("\nResults saved to credit_scores.csv")
    
    # Generate SHAP explanations
    print("\nGenerating SHAP explanations...")
    explainer = shap.TreeExplainer(rf_model)
    shap_values = explainer.shap_values(X_test_scaled)
    
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_values, X_test, plot_type="bar", show=False)
    plt.title("SHAP Feature Importance")
    plt.tight_layout()
    plt.savefig('shap_importance.png')
    plt.close()
    
    # Plot credit score distribution
    plt.figure(figsize=(10, 6))
    sns.histplot(credit_scores, bins=50)
    plt.title('Credit Score Distribution')
    plt.xlabel('Credit Score')
    plt.ylabel('Count')
    plt.savefig('credit_score_distribution.png')
    plt.close()
    
    print("\nPipeline complete. Check generated files:")
    print("- credit_scores.csv: Detailed credit scores and predictions")
    print("- shap_importance.png: Feature importance visualization")
    print("- credit_score_distribution.png: Distribution of credit scores")
    print("- rf_model.joblib: Trained Random Forest model")
    print("- xgb_model.joblib: Trained XGBoost model")
    print("- scaler.joblib: Feature scaler")

if __name__ == "__main__":
    main() 