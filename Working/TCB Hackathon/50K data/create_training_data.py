import pandas as pd
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

def load_and_preprocess_data():
    """Load and preprocess all datasets"""
    print("Loading datasets...")
    
    # Load social data
    social_df = pd.read_csv('twitter_users_social_scoring_50K.csv')
    
    # Load financial data
    borrows_df = pd.read_csv('knowledge_graph_borrows_50K_.csv')
    deposits_df = pd.read_csv('knowledge_graph_deposits_50K.csv')
    repays_df = pd.read_csv('knowledge_graph_repays_50K.csv')
    liquidates_df = pd.read_csv('knowledge_graph_liquidiates_50K.csv')
    wallets_df = pd.read_csv('knowledge_graph_multichain_wallets_50k.csv')
    
    # Merge financial data
    print("Merging financial data...")
    financial_df = pd.merge(borrows_df, deposits_df, on='walletAddress', how='outer', suffixes=('_borrow', '_deposit'))
    financial_df = pd.merge(financial_df, repays_df, on='walletAddress', how='outer', suffixes=('', '_repay'))
    
    # Process liquidates data
    liquidates_df['numberOfLiquidation'] = 1
    liquidates_df['totalValueOfLiquidation'] = liquidates_df['liquidationLogs.liquidatedWallet.1676012980.debtAssetInUSD'].fillna(0)
    liquidates_df = liquidates_df.groupby('liquidatedWallet').agg({
        'numberOfLiquidation': 'sum',
        'totalValueOfLiquidation': 'sum'
    }).reset_index()
    liquidates_df = liquidates_df.rename(columns={'liquidatedWallet': 'walletAddress'})
    
    # Merge with liquidates data
    financial_df = pd.merge(financial_df, liquidates_df, on='walletAddress', how='outer', suffixes=('', '_liquidate'))
    
    # Merge with wallets data
    financial_df = pd.merge(financial_df, wallets_df, left_on='walletAddress', right_on='address', how='outer')
    
    # Fill missing values
    financial_df = financial_df.fillna(0)
    
    return social_df, financial_df

def engineer_features(social_df, financial_df):
    """Create features for credit scoring"""
    print("Engineering features...")
    
    # Social features
    social_features = social_df[[
        'followersCount', 'statusesCount', 'favouritesCount',
        'friendsCount', 'listedCount', 'mediaCount', 'verified', 'blue'
    ]].copy()
    
    # Calculate account age
    created_dates = pd.to_datetime(social_df['created'], utc=True)
    now_utc = pd.Timestamp.now(tz='UTC')
    social_features['account_age_days'] = (now_utc - created_dates).dt.days
    
    # Calculate engagement ratios
    social_features['engagement_ratio'] = social_features['favouritesCount'] / (social_features['followersCount'] + 1)
    social_features['social_influence_score'] = (
        social_features['followersCount'] * 0.5 +
        social_features['listedCount'] * 0.3 +
        social_features['mediaCount'] * 0.2
    )
    social_features['verified_status'] = (social_features['verified'] | social_features['blue']).astype(int)
    
    # Financial features
    financial_features = financial_df[[
        'totalNumberOfBorrow', 'totalAmountOfBorrowInUSD',
        'totalNumberOfRepay', 'totalAmountOfRepayInUSD',
        'totalNumberOfDeposit', 'totalAmountOfDepositInUSD',
        'balanceInUSD', 'dailyNumberOfTransactions'
    ]].copy()
    
    # Add liquidation features if they exist
    if 'numberOfLiquidation' in financial_df.columns:
        financial_features['numberOfLiquidation'] = financial_df['numberOfLiquidation']
    elif 'numberOfLiquidation_x' in financial_df.columns:
        financial_features['numberOfLiquidation'] = financial_df['numberOfLiquidation_x']
    elif 'numberOfLiquidation_y' in financial_df.columns:
        financial_features['numberOfLiquidation'] = financial_df['numberOfLiquidation_y']
    else:
        financial_features['numberOfLiquidation'] = 0
        
    if 'totalValueOfLiquidation' in financial_df.columns:
        financial_features['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation']
    elif 'totalValueOfLiquidation_x' in financial_df.columns:
        financial_features['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation_x']
    elif 'totalValueOfLiquidation_y' in financial_df.columns:
        financial_features['totalValueOfLiquidation'] = financial_df['totalValueOfLiquidation_y']
    else:
        financial_features['totalValueOfLiquidation'] = 0
    
    # Calculate financial ratios
    financial_features['borrow_to_repay_ratio'] = (
        financial_features['totalAmountOfBorrowInUSD'] / 
        (financial_features['totalAmountOfRepayInUSD'] + 1)
    )
    financial_features['liquidation_risk'] = (
        financial_features['numberOfLiquidation'] > 0
    ).astype(int)
    financial_features['debt_to_asset_ratio'] = (
        financial_features['totalAmountOfBorrowInUSD'] / 
        (financial_features['balanceInUSD'] + 1)
    )
    
    # Combine features
    features = pd.concat([social_features, financial_features], axis=1)
    
    # Ensure all columns needed for scoring are present
    required_cols = [
        'debt_to_asset_ratio', 'dailyNumberOfTransactions', 'balanceInUSD',
        'social_influence_score', 'account_age_days'
    ]
    for col in required_cols:
        if col not in features.columns:
            features[col] = 0
    
    # Handle infinite values and outliers
    features = features.replace([np.inf, -np.inf], np.nan)
    numeric_cols = features.select_dtypes(include=[np.number]).columns
    features[numeric_cols] = features[numeric_cols].fillna(features[numeric_cols].mean())
    
    return features

def prepare_target_variable(financial_df):
    """Create target variable based on liquidation events"""
    print("Preparing target variable...")
    # Try to find the correct liquidation column
    liquidation_col = None
    for col in ['numberOfLiquidation', 'numberOfLiquidation_x', 'numberOfLiquidation_y']:
        if col in financial_df.columns:
            liquidation_col = col
            break
    if liquidation_col is None:
        print("Warning: No liquidation column found. Defaulting to zeros.")
        target = pd.Series(0, index=financial_df.index)
    else:
        target = (financial_df[liquidation_col] > 0).astype(int)
    return target

def main():
    # Load and preprocess data
    social_df, financial_df = load_and_preprocess_data()
    
    # Engineer features
    features = engineer_features(social_df, financial_df)
    
    # Prepare target variable
    target = prepare_target_variable(financial_df)
    
    # Create comprehensive dataset
    training_data = features.copy()
    training_data['target'] = target
    training_data['walletAddress'] = financial_df['walletAddress']
    
    # Add metadata
    training_data['created_at'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # Save to CSV
    output_file = 'ml_training_data.csv'
    training_data.to_csv(output_file, index=False)
    print(f"\nTraining data saved to {output_file}")
    print(f"Total samples: {len(training_data)}")
    print(f"Features: {len(training_data.columns) - 3}")  # Excluding target, walletAddress, and created_at
    print(f"Target distribution: {training_data['target'].value_counts().to_dict()}")
    
    # Print feature statistics
    print("\nFeature Statistics:")
    print(training_data.describe())

if __name__ == "__main__":
    main() 