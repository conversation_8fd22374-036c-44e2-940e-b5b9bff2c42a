import streamlit as st
import pandas as pd
import numpy as np
import joblib
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

# Set page config
st.set_page_config(
    page_title="Credit Score Predictor",
    page_icon="💰",
    layout="wide"
)

# Title and description
st.title("Credit Score Predictor")
st.markdown("""
This application predicts credit scores based on social media activity and blockchain financial transactions.
Enter the required information below to get a credit score prediction.
""")

# Load the model and scaler
@st.cache_resource
def load_model():
    try:
        model = joblib.load('rf_model.joblib')
        scaler = joblib.load('scaler.joblib')
        return model, scaler
    except Exception as e:
        st.error(f"Error loading model: {str(e)}")
        st.error("Please run the enhanced_credit_scoring.py script first to train the model.")
        return None, None

# Create input form
st.header("Input Parameters")

col1, col2 = st.columns(2)

with col1:
    st.subheader("Social Media Metrics")
    followers = st.number_input("Number of Followers", min_value=0, value=100)
    statuses = st.number_input("Number of Tweets", min_value=0, value=50)
    favorites = st.number_input("Number of Favorites", min_value=0, value=200)
    friends = st.number_input("Number of Friends", min_value=0, value=50)
    listed = st.number_input("Number of Lists", min_value=0, value=10)
    media = st.number_input("Number of Media Posts", min_value=0, value=20)
    verified = st.checkbox("Verified Account")
    blue = st.checkbox("Blue Checkmark")
    account_age = st.number_input("Account Age (days)", min_value=0, value=365)

with col2:
    st.subheader("Financial Metrics")
    balance = st.number_input("Balance in USD", min_value=0.0, value=1000.0)
    daily_tx = st.number_input("Daily Number of Transactions", min_value=0, value=5)
    total_borrow = st.number_input("Total Amount Borrowed (USD)", min_value=0.0, value=500.0)
    total_repay = st.number_input("Total Amount Repaid (USD)", min_value=0.0, value=400.0)
    total_deposit = st.number_input("Total Amount Deposited (USD)", min_value=0.0, value=2000.0)

# Calculate derived features
def calculate_features():
    try:
        features = pd.DataFrame({
            'followersCount': [followers],
            'statusesCount': [statuses],
            'favouritesCount': [favorites],
            'friendsCount': [friends],
            'listedCount': [listed],
            'mediaCount': [media],
            'verified': [int(verified)],
            'blue': [int(blue)],
            'account_age_days': [account_age],
            'balanceInUSD': [balance],
            'dailyNumberOfTransactions': [daily_tx],
            'totalAmountOfBorrowInUSD': [total_borrow],
            'totalAmountOfRepayInUSD': [total_repay],
            'totalAmountOfDepositInUSD': [total_deposit]
        })
        
        # Calculate derived features
        features['engagement_ratio'] = features['favouritesCount'] / (features['followersCount'] + 1)
        features['social_influence_score'] = (
            features['followersCount'] * 0.5 +
            features['listedCount'] * 0.3 +
            features['mediaCount'] * 0.2
        )
        features['verified_status'] = (features['verified'] | features['blue']).astype(int)
        
        features['borrow_to_repay_ratio'] = (
            features['totalAmountOfBorrowInUSD'] / 
            (features['totalAmountOfRepayInUSD'] + 1)
        )
        features['debt_to_asset_ratio'] = (
            features['totalAmountOfBorrowInUSD'] / 
            (features['balanceInUSD'] + 1)
        )
        
        return features
    except Exception as e:
        st.error(f"Error calculating features: {str(e)}")
        return None

# Predict button
if st.button("Predict Credit Score"):
    model, scaler = load_model()
    
    if model is not None and scaler is not None:
        # Get features
        features = calculate_features()
        
        if features is not None:
            try:
                # Scale features
                features_scaled = scaler.transform(features)
                
                # Get prediction
                proba = model.predict_proba(features_scaled)[0, 1]
                
                # Calculate credit score
                base_score = 300 + (proba * 550)
                
                # Feature-based adjustments
                feature_scores = pd.DataFrame()
                
                # Financial health indicators
                feature_scores['debt_to_asset_ratio'] = features['debt_to_asset_ratio'].apply(
                    lambda x: -50 if x > 0.8 else (-25 if x > 0.5 else 0)
                )
                
                feature_scores['transaction_frequency'] = features['dailyNumberOfTransactions'].apply(
                    lambda x: 25 if x > 10 else (10 if x > 5 else 0)
                )
                
                feature_scores['balance_score'] = features['balanceInUSD'].apply(
                    lambda x: 50 if x > 10000 else (25 if x > 1000 else 0)
                )
                
                # Social trust indicators
                feature_scores['social_score'] = features['social_influence_score'].apply(
                    lambda x: 25 if x > 1000 else (10 if x > 100 else 0)
                )
                
                feature_scores['account_age_score'] = features['account_age_days'].apply(
                    lambda x: 25 if x > 365 else (10 if x > 180 else 0)
                )
                
                # Calculate final score
                final_score = base_score + feature_scores.sum(axis=1).iloc[0]
                final_score = max(300, min(850, final_score))
                
                # Display results
                st.header("Results")
                
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("Credit Score", f"{final_score:.0f}")
                
                with col2:
                    st.metric("Default Probability", f"{proba:.1%}")
                
                with col3:
                    risk_level = "Low" if final_score >= 700 else "Medium" if final_score >= 600 else "High"
                    st.metric("Risk Level", risk_level)
                
                # Display score breakdown
                st.subheader("Score Breakdown")
                
                breakdown = pd.DataFrame({
                    'Component': ['Base Score', 'Debt-to-Asset Ratio', 'Transaction Frequency', 
                                'Balance Score', 'Social Score', 'Account Age Score'],
                    'Points': [
                        base_score,
                        feature_scores['debt_to_asset_ratio'].iloc[0],
                        feature_scores['transaction_frequency'].iloc[0],
                        feature_scores['balance_score'].iloc[0],
                        feature_scores['social_score'].iloc[0],
                        feature_scores['account_age_score'].iloc[0]
                    ]
                })
                
                st.bar_chart(breakdown.set_index('Component'))
                
                # Display recommendations
                st.subheader("Recommendations")
                
                if final_score < 600:
                    st.warning("""
                    To improve your credit score:
                    1. Increase your account balance
                    2. Maintain a lower debt-to-asset ratio
                    3. Increase your transaction frequency
                    4. Build your social media presence
                    5. Keep your account active for longer
                    """)
                elif final_score < 700:
                    st.info("""
                    Your credit score is good, but could be improved by:
                    1. Maintaining a higher account balance
                    2. Increasing your transaction frequency
                    3. Building your social media presence
                    4. Maintaining a longer account history
                    """)
                else:
                    st.success("""
                    Excellent credit score! Keep up the good work by:
                    1. Maintaining your current financial habits
                    2. Continuing to build your social media presence
                    3. Keeping your debt-to-asset ratio low
                    4. Maintaining your account activity
                    """)
            except Exception as e:
                st.error(f"Error making prediction: {str(e)}")

# Display sample data
st.header("Sample Data")
try:
    sample_data = pd.read_csv('credit_scores.csv')
    st.dataframe(sample_data.head())
    
    # Plot credit score distribution
    st.subheader("Credit Score Distribution")
    fig, ax = plt.subplots(figsize=(10, 6))
    sns.histplot(sample_data['credit_score'], bins=50, ax=ax)
    ax.set_title('Credit Score Distribution')
    ax.set_xlabel('Credit Score')
    ax.set_ylabel('Count')
    st.pyplot(fig)
except Exception as e:
    st.info("Run the enhanced_credit_scoring.py script to generate sample data.") 