from pymongo import MongoClient
import pandas as pd

# Connect to MongoDB
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["smart_contracts"]

# --- Extract documents (LIMITED to 1M) ---
documents = collection.find().limit(1000000)

# --- Flatten and normalize ---
flattened_data = []

for doc in documents:
    # Handle tags as list of dicts or values
    tags = doc.get("tags", [])
    token_tag = None
    if isinstance(tags, list):
        for tag in tags:
            if isinstance(tag, dict) and "token" in tag:
                token_tag = tag["token"]
            elif isinstance(tag, str) and tag == "token":
                token_tag = tag

    # Handle categories
    categories = doc.get("categories", [])
    eth_ecosystem = None
    if isinstance(categories, list):
        for cat in categories:
            if isinstance(cat, dict) and "Ethereum Ecosystem" in cat:
                eth_ecosystem = cat["Ethereum Ecosystem"]

    # Extract priceChangeLogs, numberOfDailyCalls, numberOfDailyActiveUsers, tokenDailyTransfers
    price_logs = doc.get("priceChangeLogs", {})
    daily_calls = doc.get("numberOfDailyCalls", {})
    daily_users = doc.get("numberOfDailyActiveUsers", {})
    token_transfers = doc.get("tokenDailyTransfers", {})

    # Merge all timestamps from relevant fields
    all_timestamps = sorted(set(
        list(price_logs.keys()) +
        list(daily_calls.keys()) +
        list(daily_users.keys()) +
        list(token_transfers.keys())
    ))

    for ts in all_timestamps:
        flat_doc = {
            "_id": doc.get("_id"),
            "address": doc.get("address"),
            "abi": doc.get("abi"),
            "tags_token": token_tag,
            "name": doc.get("name"),
            "symbol": doc.get("symbol"),
            "decimals": doc.get("decimals"),
            "categories_Ethereum_Ecosystem": eth_ecosystem,
            "marketCap": doc.get("marketCap"),
            "totalSupply": doc.get("totalSupply"),
            "tradingVolume": doc.get("tradingVolume"),
            "price": doc.get("price"),
            "numberOfHolders": doc.get("numberOfHolders"),
            "lastUpdatedAt": doc.get("lastUpdatedAt"),
            "keyABI": doc.get("keyABI"),
            "idCoingecko": doc.get("idCoingecko"),
            "imgUrl": doc.get("imgUrl"),
            "chainId": doc.get("chainId"),
            "timestamp": ts,
            "priceChange": price_logs.get(ts),
            "numberOfDailyCalls": daily_calls.get(ts),
            "numberOfDailyActiveUsers": daily_users.get(ts),
            "tokenDailyTransfers": token_transfers.get(ts),
            "numberOfLastDayCalls": doc.get("numberOfLastDayCalls"),
            "numberOfThisMonthCalls": doc.get("numberOfThisMonthCalls"),
            "numberOfLastDayActiveUsers": doc.get("numberOfLastDayActiveUsers"),
        }
        flattened_data.append(flat_doc)

# --- Convert to DataFrame ---
df = pd.DataFrame(flattened_data)

# --- Export to CSV ---
df.to_csv("1M_knowledge_graph_smart_contracts_flat_50K.csv", index=False)
print("✅ Exported 1M smart_contracts.csv with", len(df), "rows")
