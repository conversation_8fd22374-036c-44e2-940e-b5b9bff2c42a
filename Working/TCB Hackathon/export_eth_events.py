from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["ethereum_blockchain_etl"]
collection = db["events"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "block_number": 1,
    "block_timestamp": 1,
    "contract_address": 1,
    "event_type": 1,
    "liquidityIndex": 1,
    "liquidityRate": 1,
    "log_index": 1,
    "reserve": 1,
    "stableBorrowRate": 1,
    "transaction_hash": 1,
    "type": 1,
    "variableBorrowIndex": 1,
    "variableBorrowRate": 1,
    "wallet": 1,
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 1000000}},       # Randomly sample
    {"$project": projection}            # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("1M_ethereum_blockchain_events.csv", index=False)
print("✅ Exported 1M documents to ethereum_blockchain.csv")
