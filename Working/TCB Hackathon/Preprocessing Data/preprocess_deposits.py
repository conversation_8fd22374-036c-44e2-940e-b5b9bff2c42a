import pandas as pd
import numpy as np
from datetime import datetime
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def preprocess_deposits_data(input_file, output_file):
    try:
        # Read the CSV file
        logging.info(f"Reading data from {input_file}")
        df = pd.read_csv(input_file)
        
        # Remove specified columns
        columns_to_remove = [
            'lendingPoolAddress', 'timestamp', 'tokenAddress',
            'valueInUSD', 'highestDepositInUSD', 'lowestDepositInUSD',
            'averageDepositInUSD'
        ]
        df = df.drop(columns=[col for col in columns_to_remove if col in df.columns])
        logging.info("Removed specified columns")
        
        # Handle missing values
        numeric_columns = df.select_dtypes(include=[np.number]).columns
        categorical_columns = df.select_dtypes(include=['object']).columns
        
        # Fill numeric columns with median
        for col in numeric_columns:
            df[col] = df[col].fillna(df[col].median())
        logging.info("Filled missing values in numeric columns with median")
        
        # Fill categorical columns with mode
        for col in categorical_columns:
            df[col] = df[col].fillna(df[col].mode()[0])
        logging.info("Filled missing values in categorical columns with mode")
        
        # Remove duplicates based on walletAddress
        df = df.drop_duplicates(subset=['walletAddress'], keep='first')
        logging.info("Removed duplicate wallet addresses")
        
        # Handle outliers using IQR method for numeric columns
        for col in numeric_columns:
            Q1 = df[col].quantile(0.25)
            Q3 = df[col].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            # Clip outliers instead of removing them
            df[col] = df[col].clip(lower=lower_bound, upper=upper_bound)
        logging.info("Handled outliers using IQR method")
        
        # Apply log transformation to right-skewed numeric columns
        for col in numeric_columns:
            skewness = df[col].skew()
            if skewness > 1:  # If right-skewed
                df[col] = np.log1p(df[col])  # Using log1p to handle zero values
                logging.info(f"Applied log transformation to {col}")
        
        # Save the preprocessed data
        df.to_csv(output_file, index=False)
        logging.info(f"Saved preprocessed data to {output_file}")
        
        return True
        
    except Exception as e:
        logging.error(f"Error during preprocessing: {str(e)}")
        return False

if __name__ == "__main__":
    input_file = "Data original/2. full_knowledge_graph_deposits_50K.csv"
    output_file = "preprocessed_data_deposits.csv"
    
    success = preprocess_deposits_data(input_file, output_file)
    if success:
        logging.info("Data preprocessing completed successfully")
    else:
        logging.error("Data preprocessing failed") 