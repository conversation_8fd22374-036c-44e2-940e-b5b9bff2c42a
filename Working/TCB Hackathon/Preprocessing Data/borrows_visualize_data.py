import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def visualize_data(input_file):
    try:
        # Read the preprocessed data
        logging.info(f"Reading data from {input_file}")
        df = pd.read_csv(input_file)
        
        # Create a figure with subplots for distribution plots
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        n_cols = len(numeric_cols)
        n_rows = (n_cols + 1) // 2  # Round up division
        
        # Distribution plots
        plt.figure(figsize=(15, 5*n_rows))
        for i, col in enumerate(numeric_cols, 1):
            plt.subplot(n_rows, 2, i)
            sns.histplot(data=df, x=col, kde=True)
            plt.title(f'Distribution of {col}')
            plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('distributions.png')
        plt.close()
        
        # Box plots for outlier detection
        plt.figure(figsize=(15, 5*n_rows))
        for i, col in enumerate(numeric_cols, 1):
            plt.subplot(n_rows, 2, i)
            sns.boxplot(data=df, y=col)
            plt.title(f'Box Plot of {col}')
            plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('boxplots.png')
        plt.close()
        
        # Correlation heatmap
        plt.figure(figsize=(12, 8))
        correlation_matrix = df[numeric_cols].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
        plt.title('Correlation Heatmap')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png')
        plt.close()
        
        # Pairplot for selected numeric columns (if there are too many columns, select top 5)
        if len(numeric_cols) > 5:
            top_correlated = correlation_matrix.unstack().sort_values(ascending=False)
            top_correlated = top_correlated[top_correlated != 1.0].head(10)
            selected_cols = list(set([x[0] for x in top_correlated.index] + [x[1] for x in top_correlated.index]))
        else:
            selected_cols = numeric_cols
            
        sns.pairplot(df[selected_cols])
        plt.savefig('pairplot.png')
        plt.close()
        
        # Summary statistics
        summary_stats = df[numeric_cols].describe()
        summary_stats.to_csv('summary_statistics.csv')
        
        logging.info("Visualization completed successfully")
        
    except Exception as e:
        logging.error(f"Error during visualization: {str(e)}")
        raise

if __name__ == "__main__":
    visualize_data("preprocessed_data_borrows.csv") 