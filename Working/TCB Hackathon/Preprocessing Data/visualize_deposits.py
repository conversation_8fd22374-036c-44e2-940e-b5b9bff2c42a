import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

def visualize_data(input_file):
    try:
        # Read the preprocessed data
        logging.info(f"Reading preprocessed data from {input_file}")
        df = pd.read_csv(input_file)
        
        # Set up the plotting style
        plt.style.use('default')
        sns.set_theme(style="whitegrid")
        
        # 1. Distribution plots for numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        n_cols = len(numeric_cols)
        fig, axes = plt.subplots(n_cols, 1, figsize=(12, 4*n_cols))
        fig.suptitle('Distribution of Numeric Variables')
        
        for idx, col in enumerate(numeric_cols):
            sns.histplot(data=df, x=col, kde=True, ax=axes[idx])
            axes[idx].set_title(f'Distribution of {col}')
            axes[idx].set_xlabel(col)
            axes[idx].set_ylabel('Count')
        
        plt.tight_layout()
        plt.savefig('deposits_distributions.png')
        plt.close()
        logging.info("Created distribution plots")
        
        # 2. Box plots for outlier detection
        plt.figure(figsize=(12, 6))
        sns.boxplot(data=df[numeric_cols])
        plt.xticks(rotation=45)
        plt.title('Box Plots of Numeric Variables')
        plt.tight_layout()
        plt.savefig('deposits_boxplots.png')
        plt.close()
        logging.info("Created box plots")
        
        # 3. Correlation heatmap
        plt.figure(figsize=(10, 8))
        correlation_matrix = df[numeric_cols].corr()
        sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
        plt.title('Correlation Heatmap')
        plt.tight_layout()
        plt.savefig('deposits_correlation_heatmap.png')
        plt.close()
        logging.info("Created correlation heatmap")
        
        # 4. Pairplot for top correlated variables
        # Select top correlated variables (if more than 5)
        if len(numeric_cols) > 5:
            # Get absolute correlations and select top 5
            corr_matrix = df[numeric_cols].corr().abs()
            top_corr = corr_matrix.unstack().sort_values(ascending=False)
            top_vars = list(set([x[0] for x in top_corr[1:6].index]))
            plot_cols = top_vars
        else:
            plot_cols = numeric_cols
            
        sns.pairplot(df[plot_cols])
        plt.savefig('deposits_pairplot.png')
        plt.close()
        logging.info("Created pairplot")
        
        # 5. Save summary statistics
        summary_stats = df[numeric_cols].describe()
        summary_stats.to_csv('deposits_summary_statistics.csv')
        logging.info("Saved summary statistics")
        
        logging.info("Visualization completed successfully")
        return True
        
    except Exception as e:
        logging.error(f"Error during visualization: {str(e)}")
        return False

if __name__ == "__main__":
    input_file = "preprocessed_data_deposits.csv"
    success = visualize_data(input_file)
    if success:
        logging.info("Data visualization completed successfully")
    else:
        logging.error("Data visualization failed") 