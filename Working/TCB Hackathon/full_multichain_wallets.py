from pymongo import MongoClient
import pandas as pd

# Connect to MongoDB
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["multichain_wallets"]

# Get sample (limit to reduce memory use)
documents = collection.find({}, {
    "_id": 1,
    "address": 1,
    "lastUpdatedAt": 1,
    "balanceInUSD": 1,
    "balanceChangeLogs": 1,
    "tokens": 1,
    "tokenChangeLogs": 1,
    "depositInUSD": 1,
    "depositChangeLogs": 1,
    "depositTokens": 1,
    "depositTokenChangeLogs": 1,
    "borrowInUSD": 1,
    "borrowChangeLogs": 1,
    "totalValueOfLiquidation": 1,
    "numberOfLiquidation": 1,
    "flagged": 1,
    "dailyNumberOfTransactions": 1,
    "dailyTransactionAmounts": 1
}).limit(250000)

rows = []

# Flatten logic
for doc in documents:
    base = {
        "_id": doc.get("_id"),
        "address": doc.get("address"),
        "lastUpdatedAt": doc.get("lastUpdatedAt"),
        "balanceInUSD": doc.get("balanceInUSD"),
        "depositInUSD": doc.get("depositInUSD"),
        "borrowInUSD": doc.get("borrowInUSD"),
        "totalValueOfLiquidation": doc.get("totalValueOfLiquidation"),
        "numberOfLiquidation": doc.get("numberOfLiquidation"),
        "flagged": doc.get("flagged")
    }

    # balanceChangeLogs
    if "balanceChangeLogs" in doc:
        for ts, val in doc["balanceChangeLogs"].items():
            row = base.copy()
            row["type"] = "balanceChange"
            row["timestamp"] = int(ts)
            row["value"] = val
            rows.append(row)

    # tokenChangeLogs
    if "tokenChangeLogs" in doc:
        for token, logs in doc["tokenChangeLogs"].items():
            for ts, tsdata in logs.items():
                row = base.copy()
                row["type"] = "tokenChange"
                row["timestamp"] = int(ts)
                row["token"] = token
                row["value"] = tsdata.get("valueInUSD")
                rows.append(row)

    # depositChangeLogs
    if "depositChangeLogs" in doc:
        for ts, val in doc["depositChangeLogs"].items():
            row = base.copy()
            row["type"] = "depositChange"
            row["timestamp"] = int(ts)
            row["value"] = val
            rows.append(row)

    # depositTokenChangeLogs
    if "depositTokenChangeLogs" in doc:
        for token, logs in doc["depositTokenChangeLogs"].items():
            for ts, tsdata in logs.items():
                row = base.copy()
                row["type"] = "depositTokenChange"
                row["timestamp"] = int(ts)
                row["token"] = token
                row["value"] = tsdata.get("valueInUSD")
                rows.append(row)

    # borrowChangeLogs
    if "borrowChangeLogs" in doc:
        for ts, val in doc["borrowChangeLogs"].items():
            row = base.copy()
            row["type"] = "borrowChange"
            row["timestamp"] = int(ts)
            row["value"] = val
            rows.append(row)

    # dailyNumberOfTransactions
    if "dailyNumberOfTransactions" in doc:
        for ts, val in doc["dailyNumberOfTransactions"].items():
            row = base.copy()
            row["type"] = "dailyTxCount"
            row["timestamp"] = int(ts)
            row["value"] = val
            rows.append(row)

    # dailyTransactionAmounts
    if "dailyTransactionAmounts" in doc:
        for ts, val in doc["dailyTransactionAmounts"].items():
            row = base.copy()
            row["type"] = "dailyTxAmount"
            row["timestamp"] = int(ts)
            row["value"] = val
            rows.append(row)

# Convert to DataFrame
df = pd.DataFrame(rows)

# Save to CSV
df.to_csv("250K_knowledge_graph_multichain_wallets.csv", index=False)
print("Exported to flattened_multichain_wallets.csv")
