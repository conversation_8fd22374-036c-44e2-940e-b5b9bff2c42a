import csv
from pymongo import MongoClient

# MongoDB connection
client = MongoClient("*************************************************************************************************************")

# Connect to DB and Collection
db = client["knowledge_graph"]
collection = db["borrows"]

# Define the output CSV file
filename = "knowledge_graph_borrows_50K_.csv"

# Fields to extract (nested fields will be accessed manually)
fields = [
    "_id",
    "walletAddress",
    "lendingPoolAddress",
    "borrowLogs.1675824439.tokens.******************************************",
    "borrowLogs.1675824439.valueInUSD",
    "borrowTokens.******************************************",
    "totalNumberOfBorrow",
    "totalAmountOfBorrowInUSD",
    "highestBorrowInUSD",
    "lowestBorrowInUSD",
    "averageBorrowInUSD"
]

# Set up top-level projection (exclude nested fields for projection)
projection = {
    "_id": 1,
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "borrowLogs": 1,
    "borrowTokens": 1,
    "totalNumberOfBorrow": 1,
    "totalAmountOfBorrowInUSD": 1,
    "highestBorrowInUSD": 1,
    "lowestBorrowInUSD": 1,
    "averageBorrowInUSD": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# Write to CSV
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(fields)  # CSV Header

    for doc in cursor:
        row = [
            doc.get("_id", ""),
            doc.get("walletAddress", ""),
            doc.get("lendingPoolAddress", ""),
            doc.get("borrowLogs", {}).get("1675824439", {}).get("tokens", {}).get("******************************************", ""),
            doc.get("borrowLogs", {}).get("1675824439", {}).get("valueInUSD", ""),
            doc.get("borrowTokens", {}).get("******************************************", ""),
            doc.get("totalNumberOfBorrow", ""),
            doc.get("totalAmountOfBorrowInUSD", ""),
            doc.get("highestBorrowInUSD", ""),
            doc.get("lowestBorrowInUSD", ""),
            doc.get("averageBorrowInUSD", "")
        ]
        writer.writerow(row)

print(f"✅ Exported 50000 records from 'knowledge_graph.borrows' to '{filename}'")
