import tweepy
from textblob import TextBlob
import os
from dotenv import load_dotenv

class TwitterAnalyzer:
    def __init__(self):
        load_dotenv()
        self.client = tweepy.Client(
            bearer_token=os.getenv('TWITTER_BEARER_TOKEN'),
            consumer_key=os.getenv('TWITTER_API_KEY'),
            consumer_secret=os.getenv('TWITTER_API_SECRET'),
            access_token=os.getenv('TWITTER_ACCESS_TOKEN'),
            access_token_secret=os.getenv('TWITTER_ACCESS_TOKEN_SECRET')
        )

    def get_user_metrics(self, username):
        """Get basic user metrics"""
        try:
            user = self.client.get_user(username=username, 
                                      user_fields=['public_metrics', 'created_at', 'verified'])
            if user.data:
                return {
                    'followers_count': user.data.public_metrics['followers_count'],
                    'following_count': user.data.public_metrics['following_count'],
                    'tweet_count': user.data.public_metrics['tweet_count'],
                    'account_age': user.data.created_at,
                    'verified': user.data.verified
                }
        except Exception as e:
            print(f"Error fetching user metrics: {e}")
            return None

    def analyze_user_content(self, username, tweet_limit=100):
        """Analyze user's tweets for crypto relevance and sentiment"""
        try:
            tweets = self.client.get_users_tweets(username=username, 
                                                max_results=tweet_limit,
                                                tweet_fields=['public_metrics', 'created_at'])
            
            if not tweets.data:
                return None

            analysis = {
                'avg_engagement': 0,
                'crypto_relevance': 0,
                'sentiment_score': 0,
                'engagement_consistency': 0
            }

            for tweet in tweets.data:
                # Calculate engagement
                metrics = tweet.public_metrics
                engagement = (metrics['retweet_count'] + metrics['like_count'] + 
                            metrics['reply_count'] + metrics['quote_count'])
                
                # Sentiment analysis
                blob = TextBlob(tweet.text)
                analysis['sentiment_score'] += blob.sentiment.polarity

                # Check crypto relevance (basic keyword check)
                crypto_keywords = ['crypto', 'bitcoin', 'ethereum', 'blockchain', 'defi']
                if any(keyword in tweet.text.lower() for keyword in crypto_keywords):
                    analysis['crypto_relevance'] += 1

            # Normalize scores
            tweet_count = len(tweets.data)
            analysis['sentiment_score'] /= tweet_count
            analysis['crypto_relevance'] = (analysis['crypto_relevance'] / tweet_count) * 100

            return analysis

        except Exception as e:
            print(f"Error analyzing user content: {e}")
            return None

    def calculate_social_score(self, username):
        """Calculate overall social credit score"""
        metrics = self.get_user_metrics(username)
        content_analysis = self.analyze_user_content(username)

        if not metrics or not content_analysis:
            return None

        # Score components (adjust weights as needed)
        score_components = {
            'follower_score': min(metrics['followers_count'] / 1000, 100) * 0.3,
            'engagement_score': content_analysis['avg_engagement'] * 0.2,
            'crypto_relevance': content_analysis['crypto_relevance'] * 0.25,
            'sentiment_score': ((content_analysis['sentiment_score'] + 1) / 2) * 100 * 0.15,
            'account_quality': (metrics['verified'] * 100) * 0.1
        }

        total_score = sum(score_components.values())
        return min(total_score, 100)  # Normalize to 0-100 scale 