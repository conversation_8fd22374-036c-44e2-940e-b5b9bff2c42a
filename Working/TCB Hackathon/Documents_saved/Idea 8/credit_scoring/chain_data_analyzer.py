import pandas as pd
import numpy as np
from datetime import datetime

class ChainDataAnalyzer:
    def __init__(self):
        self.eth_data = None
        self.bnb_data = None
        self.load_data()

    def load_data(self):
        """Load chain data from CSV files"""
        try:
            self.eth_data = pd.read_csv('Ethereum_chain_0x1.balance_change.csv')
            self.bnb_data = pd.read_csv('BNB_Chain_chain_0x38.balance_change.csv')
        except Exception as e:
            print(f"Error loading chain data: {e}")

    def get_wallet_metrics(self, wallet_address):
        """Calculate wallet metrics across both chains"""
        eth_metrics = self._analyze_chain_data(self.eth_data, wallet_address) if self.eth_data is not None else {}
        bnb_metrics = self._analyze_chain_data(self.bnb_data, wallet_address) if self.bnb_data is not None else {}
        
        combined_metrics = self._combine_chain_metrics(eth_metrics, bnb_metrics)
        return self._calculate_risk_scores(combined_metrics)

    def _analyze_chain_data(self, df, wallet_address):
        """Analyze wallet data for a specific chain"""
        if df is None or df.empty:
            return {}

        wallet_data = df[df['address'].str.lower() == wallet_address.lower()]
        
        if wallet_data.empty:
            return {}

        metrics = {
            'total_transactions': wallet_data['number_tx'].sum(),
            'unique_tokens': len(wallet_data['token'].unique()),
            'total_value': wallet_data['value'].sum(),
            'total_income': wallet_data['income'].sum(),
            'avg_transaction_value': wallet_data['value'].mean(),
            'max_transaction_value': wallet_data['value'].max(),
            'min_transaction_value': wallet_data['value'].min(),
            'transaction_frequency': len(wallet_data),
            'positive_transactions': len(wallet_data[wallet_data['value'] > 0]),
            'negative_transactions': len(wallet_data[wallet_data['value'] < 0]),
            'net_position': wallet_data['value'].sum()
        }

        return metrics

    def _combine_chain_metrics(self, eth_metrics, bnb_metrics):
        """Combine metrics from both chains"""
        combined = {}
        
        # Combine numerical metrics
        for key in ['total_transactions', 'unique_tokens', 'total_value', 'total_income',
                   'positive_transactions', 'negative_transactions']:
            combined[key] = (eth_metrics.get(key, 0) + bnb_metrics.get(key, 0))

        # Calculate averages across chains
        eth_avg = eth_metrics.get('avg_transaction_value', 0)
        bnb_avg = bnb_metrics.get('avg_transaction_value', 0)
        combined['avg_transaction_value'] = (eth_avg + bnb_avg) / 2 if eth_avg and bnb_avg else max(eth_avg, bnb_avg)

        # Track activity on multiple chains
        combined['active_chains'] = sum([
            1 if eth_metrics else 0,
            1 if bnb_metrics else 0
        ])

        return combined

    def _calculate_risk_scores(self, metrics):
        """Calculate risk scores based on combined metrics"""
        if not metrics:
            return {
                'risk_score': 0,
                'transaction_score': 0,
                'diversity_score': 0,
                'value_score': 0,
                'stability_score': 0,
                'risk_level': 'High Risk'
            }

        # Transaction Activity Score (0-100)
        transaction_score = min(metrics['total_transactions'] / 100 * 100, 100)

        # Token Diversity Score (0-100)
        diversity_score = min(metrics['unique_tokens'] / 5 * 100, 100)

        # Value Score (0-100)
        value_ratio = metrics['positive_transactions'] / max(metrics['total_transactions'], 1)
        value_score = value_ratio * 100

        # Stability Score (0-100)
        stability_score = min(metrics['active_chains'] / 2 * 100, 100)

        # Calculate final risk score
        risk_score = (
            transaction_score * 0.3 +
            diversity_score * 0.2 +
            value_score * 0.3 +
            stability_score * 0.2
        )

        # Determine risk level
        risk_level = 'Low Risk' if risk_score >= 80 else 'Medium Risk' if risk_score >= 60 else 'High Risk'

        return {
            'risk_score': risk_score,
            'transaction_score': transaction_score,
            'diversity_score': diversity_score,
            'value_score': value_score,
            'stability_score': stability_score,
            'risk_level': risk_level,
            'raw_metrics': metrics
        }

    def generate_recommendations(self, scores):
        """Generate recommendations based on scores"""
        recommendations = []

        if scores['transaction_score'] < 70:
            recommendations.append("Increase transaction activity across chains")
        
        if scores['diversity_score'] < 70:
            recommendations.append("Diversify token holdings across different assets")
        
        if scores['value_score'] < 70:
            recommendations.append("Maintain a positive balance and consistent transaction history")
        
        if scores['stability_score'] < 70:
            recommendations.append("Consider expanding activity to multiple chains")

        return recommendations 