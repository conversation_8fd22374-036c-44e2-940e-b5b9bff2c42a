from .chain_data_analyzer import ChainDataAnalyzer
import pandas as pd
from datetime import datetime

class CryptoCreditScorer:
    def __init__(self):
        self.chain_analyzer = ChainDataAnalyzer()
        
    def calculate_credit_score(self, wallet_address):
        """Calculate credit score based on wallet activity"""
        chain_metrics = self.chain_analyzer.get_wallet_metrics(wallet_address)
        return chain_metrics

    def generate_credit_report(self, wallet_address):
        """Generate a detailed credit report"""
        chain_metrics = self.calculate_credit_score(wallet_address)
        
        return {
            'summary': {
                'total_score': chain_metrics['risk_score'],
                'transaction_score': chain_metrics['transaction_score'],
                'diversity_score': chain_metrics['diversity_score'],
                'value_score': chain_metrics['value_score'],
                'stability_score': chain_metrics['stability_score']
            },
            'risk_level': chain_metrics['risk_level'],
            'chain_metrics': chain_metrics,
            'recommendations': self.chain_analyzer.generate_recommendations(chain_metrics),
            'timestamp': datetime.now().isoformat()
        } 