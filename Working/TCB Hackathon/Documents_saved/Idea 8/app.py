import streamlit as st
import plotly.graph_objects as go
from streamlit_option_menu import option_menu
from credit_scoring.credit_scorer import CryptoCreditScorer
import pandas as pd
import plotly.express as px

# Page config
st.set_page_config(
    page_title="Crypto Credit Score Calculator",
    page_icon="🏦",
    layout="wide"
)

# Custom CSS
st.markdown("""
<style>
    .main {
        padding: 0rem 1rem;
    }
    .stAlert {
        margin-top: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        border-radius: 0.5rem;
        padding: 1rem;
        margin: 0.5rem 0;
    }
    .css-1d391kg {
        padding-top: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def create_gauge_chart(value, title):
    """Create a gauge chart for scores"""
    fig = go.Figure(go.Indicator(
        mode="gauge+number",
        value=value,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': title},
        gauge={
            'axis': {'range': [0, 100]},
            'bar': {'color': "darkblue"},
            'steps': [
                {'range': [0, 40], 'color': "red"},
                {'range': [40, 60], 'color': "orange"},
                {'range': [60, 80], 'color': "yellow"},
                {'range': [80, 100], 'color': "green"}
            ],
            'threshold': {
                'line': {'color': "black", 'width': 4},
                'thickness': 0.75,
                'value': value
            }
        }
    ))
    fig.update_layout(height=200)
    return fig

def create_radar_chart(metrics):
    """Create a radar chart for detailed metrics"""
    categories = ['Transaction Score', 'Diversity Score', 'Value Score', 'Stability Score']
    values = [
        metrics['transaction_score'],
        metrics['diversity_score'],
        metrics['value_score'],
        metrics['stability_score']
    ]
    
    fig = go.Figure()
    fig.add_trace(go.Scatterpolar(
        r=values,
        theta=categories,
        fill='toself',
        name='Metrics'
    ))
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 100]
            )),
        showlegend=False,
        height=400
    )
    return fig

def main():
    # Initialize the scorer
    scorer = CryptoCreditScorer()
    
    # Sidebar
    with st.sidebar:
        selected = option_menu(
            "Navigation",
            ["Credit Score Calculator", "About"],
            icons=["calculator", "info-circle"],
            menu_icon="cast",
            default_index=0,
        )
    
    if selected == "Credit Score Calculator":
        st.title("🏦 Crypto Credit Score Calculator")
        st.write("Calculate credit scores based on on-chain data from Ethereum and BNB Chain")
        
        # Input form
        with st.form("score_calculator"):
            wallet_address = st.text_input("Wallet Address", placeholder="0x...")
            submitted = st.form_submit_button("Calculate Score")
            
            if submitted and wallet_address:
                try:
                    # Calculate credit score
                    report = scorer.generate_credit_report(wallet_address=wallet_address)
                    
                    # Display results
                    st.success("Credit score calculated successfully!")
                    
                    # Main metrics
                    col1, col2 = st.columns(2)
                    with col1:
                        st.plotly_chart(create_gauge_chart(
                            report['summary']['total_score'],
                            "Overall Credit Score"
                        ), use_container_width=True)
                    with col2:
                        # Risk Level
                        st.subheader("Risk Assessment")
                        risk_color = {
                            "Low Risk": "green",
                            "Medium Risk": "orange",
                            "High Risk": "red"
                        }
                        st.markdown(
                            f"""
                            <div style='background-color: {risk_color[report['risk_level']]}1a; 
                                      padding: 20px; border-radius: 10px; 
                                      border: 1px solid {risk_color[report['risk_level']]}'>
                                <h2 style='color: {risk_color[report['risk_level']]}; margin:0'>
                                    {report['risk_level']}
                                </h2>
                            </div>
                            """, 
                            unsafe_allow_html=True
                        )
                    
                    # Detailed Metrics
                    st.subheader("Detailed Metrics")
                    col1, col2 = st.columns([2, 1])
                    with col1:
                        st.plotly_chart(create_radar_chart(report['summary']), use_container_width=True)
                    with col2:
                        st.markdown("### Component Scores")
                        metrics = {
                            "Transaction Activity": report['summary']['transaction_score'],
                            "Token Diversity": report['summary']['diversity_score'],
                            "Value Movement": report['summary']['value_score'],
                            "Chain Activity": report['summary']['stability_score']
                        }
                        for key, value in metrics.items():
                            st.metric(key, f"{value:.1f}%")
                    
                    # Raw Metrics
                    if 'raw_metrics' in report['chain_metrics']:
                        with st.expander("View Raw Metrics"):
                            raw_metrics = report['chain_metrics']['raw_metrics']
                            col1, col2 = st.columns(2)
                            metrics_list = list(raw_metrics.items())
                            mid = len(metrics_list) // 2
                            
                            with col1:
                                for key, value in metrics_list[:mid]:
                                    st.metric(key.replace('_', ' ').title(), f"{value:.2f}")
                            with col2:
                                for key, value in metrics_list[mid:]:
                                    st.metric(key.replace('_', ' ').title(), f"{value:.2f}")
                    
                    # Recommendations
                    st.subheader("Recommendations")
                    for rec in report['recommendations']:
                        st.markdown(f"- {rec}")
                
                except Exception as e:
                    st.error(f"Error calculating credit score: {str(e)}")
    
    else:  # About page
        st.title("About the Credit Score Calculator")
        st.write("""
        This tool calculates crypto credit scores based on on-chain activity across multiple blockchains.
        
        ### Scoring Components
        
        #### Transaction Score (30%)
        - Number of transactions
        - Transaction frequency
        - Transaction values
        
        #### Token Diversity Score (20%)
        - Number of unique tokens
        - Distribution across different tokens
        
        #### Value Score (30%)
        - Ratio of positive to negative transactions
        - Total value movement
        - Income metrics
        
        #### Chain Activity Score (20%)
        - Activity across multiple chains
        - Consistency of transactions
        
        ### Risk Levels
        - **Low Risk** (Score >= 80): Excellent credit history
        - **Medium Risk** (Score 60-79): Good credit history with some areas for improvement
        - **High Risk** (Score < 60): Significant room for improvement
        
        ### Data Sources
        - Ethereum blockchain
        - BNB Chain
        """)

if __name__ == "__main__":
    main() 