# Crypto Credit Score Calculator

A comprehensive credit scoring system for crypto users that analyzes both on-chain and social data to generate credit scores.

## Features

- Multi-chain analysis (Ethereum & BNB Chain)
- Social data integration (Twitter)
- Interactive dashboard with visualizations
- Detailed metrics and recommendations
- Risk assessment

## Setup

1. Install dependencies:
```bash
pip install -r requirements.txt
```

2. Set up environment variables:
Create a `.env` file with your Twitter API credentials:
```
TWITTER_BEARER_TOKEN=your_bearer_token
TWITTER_API_KEY=your_api_key
TWITTER_API_SECRET=your_api_secret
TWITTER_ACCESS_TOKEN=your_access_token
TWITTER_ACCESS_TOKEN_SECRET=your_access_token_secret
```

3. Run the application:
```bash
streamlit run app.py
```

## Usage

1. Open the application in your web browser (default: http://localhost:8501)
2. Enter a wallet address to analyze
3. Optionally, enter a Twitter username for social scoring
4. Click "Calculate Score" to generate the credit report

## Scoring Components

### On-Chain Metrics (70%)
- Transaction Score (30%)
- Token Diversity Score (20%)
- Value Score (30%)
- Stability Score (20%)

### Social Metrics (30%)
- Twitter engagement
- Community participation
- Content quality

## Risk Levels

- Low Risk: Score >= 80
- Medium Risk: Score 60-79
- High Risk: Score < 60 