import streamlit as st
import pandas as pd
import base64
import io
from components.data_upload import load_data
from components.preprocessing import preprocess_data
from components.clustering import SegmentationAnalyzer
from components.visualization import (
    plot_elbow_curve,
    create_segment_dashboard
)
from components.data_enrichment import get_segmentation_features

def main():
    st.set_page_config(
        page_title="Techcombank Customer Segmentation Dashboard",
        page_icon="📊",
        layout="wide"
    )

    st.title("📊 Techcombank Customer Segmentation Dashboard")
    st.write("This dashboard provides multi-dimensional customer segmentation analysis.")

    # Sidebar for parameters
    st.sidebar.header("Parameters")
    
    try:
        # Load data
        st.header("1. Data Loading and Enrichment")
        file_path = 'sample_data.csv'
        df = load_data(file_path)
        
        if df is None:
            st.error("Failed to load data. Please check the data file and try again.")
            return

        st.success("Data loaded and enriched successfully!")
        st.write(f"Number of records: {len(df)}")
        
        # Show sample data
        with st.expander("View Sample Data"):
            st.write(df.head())
            st.write("Data Summary:")
            st.write(df.describe())

        # Get segmentation features
        segmentation_features = get_segmentation_features()
        
        # Segmentation Analysis
        st.header("2. Segmentation Analysis")
        
        # Select segmentation type
        segmentation_type = st.selectbox(
            "Select Segmentation Type",
            list(segmentation_features.keys()),
            key="segmentation_type"
        )
        
        # Number of clusters
        n_clusters = st.slider(
            f"Number of {segmentation_type} Segments",
            min_value=2,
            max_value=10,
            value=4,
            key=f"n_clusters_{segmentation_type}"
        )
        
        # Initialize analyzer
        analyzer = SegmentationAnalyzer(df)
        
        # Compute optimal clusters
        with st.spinner(f"Computing optimal number of {segmentation_type} segments..."):
            wcss, silhouette_scores = analyzer.compute_elbow_method(segmentation_type)
            if wcss is not None and silhouette_scores is not None:
                st.subheader("2.1 Optimal Number of Segments")
                plot_elbow_curve(wcss, silhouette_scores, segmentation_type)
                
                # Add explanation
                st.info("""
                The elbow curve shows the Within-Cluster Sum of Squares (WCSS) for different numbers of segments.
                The optimal number is typically found at the 'elbow' of the curve.
                The Silhouette Score helps validate the segmentation quality (higher is better, range: -1 to 1).
                """)

        # Perform segmentation
        with st.spinner(f"Performing {segmentation_type} segmentation analysis..."):
            result = analyzer.perform_clustering(segmentation_type, n_clusters)
            
            if result is not None:
                segmented_data, sil_score, cluster_centers = result
                
                if segmented_data is not None:
                    st.subheader("2.2 Segmentation Results")
                    st.write(f"**Silhouette Score**: {sil_score:.3f}")
                    
                    # Display segment centers
                    with st.expander("View Segment Centers"):
                        st.write(cluster_centers)
                    
                    # Create dashboard for this segmentation
                    create_segment_dashboard(
                        segmented_data,
                        segmentation_type,
                        segmentation_features[segmentation_type]
                    )
                    
                    # Export options
                    st.header("3. Export Options")
                    export_format = st.selectbox(
                        "Select export format",
                        ["CSV", "Excel"],
                        key="export_format"
                    )
                    
                    if st.button("Export Segmented Data"):
                        try:
                            if export_format == "CSV":
                                csv = segmented_data.to_csv(index=False)
                                b64 = base64.b64encode(csv.encode()).decode()
                                href = f'<a href="data:file/csv;base64,{b64}" download="techcombank_{segmentation_type.lower()}_segments.csv">Download {segmentation_type} Segments (CSV)</a>'
                                st.markdown(href, unsafe_allow_html=True)
                            else:  # Excel
                                output = io.BytesIO()
                                with pd.ExcelWriter(output, engine='xlsxwriter') as writer:
                                    # Write main data
                                    segmented_data.to_excel(writer, sheet_name='All Segments', index=False)
                                    
                                    # Write individual segment sheets
                                    segment_col = f'{segmentation_type}_Cluster'
                                    for segment in sorted(segmented_data[segment_col].unique()):
                                        segment_data = segmented_data[segmented_data[segment_col] == segment]
                                        segment_data.to_excel(writer, sheet_name=f'Segment {segment}', index=False)
                                    
                                    # Write segment centers
                                    cluster_centers.to_excel(writer, sheet_name='Segment Centers', index=True)
                                
                                output.seek(0)
                                b64 = base64.b64encode(output.read()).decode()
                                href = f'<a href="data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,{b64}" download="techcombank_{segmentation_type.lower()}_segments.xlsx">Download {segmentation_type} Segments (Excel)</a>'
                                st.markdown(href, unsafe_allow_html=True)
                                
                        except Exception as e:
                            st.error(f"Error exporting data: {str(e)}")

    except Exception as e:
        st.error(f"An error occurred: {str(e)}")
        st.error("Please check the data and try again.")

if __name__ == "__main__":
    main()