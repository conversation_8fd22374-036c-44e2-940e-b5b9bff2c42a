from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import silhouette_score
import pandas as pd
import numpy as np
import streamlit as st
from typing import Dict, List, Tuple, Optional
from .data_enrichment import get_segmentation_features

class SegmentationAnalyzer:
    def __init__(self, data: pd.DataFrame):
        """Initialize the segmentation analyzer with customer data."""
        self.data = data.copy()
        self.segmentation_features = get_segmentation_features()
        self.encoders = {}
        self.scalers = {}
        self.models = {}
        
    def preprocess_features(self, features: List[str]) -> np.ndarray:
        """Preprocess features for clustering."""
        df = self.data[features].copy()
        processed_data = []
        
        for feature in features:
            feature_data = df[feature]
            
            # Handle different data types appropriately
            if feature_data.dtype == 'object' or feature_data.dtype.name == 'category':
                # For categorical features, use label encoding
                if feature not in self.encoders:
                    self.encoders[feature] = LabelEncoder()
                    # Clean the data by taking only the first value if multiple values exist
                    cleaned_data = feature_data.astype(str).apply(lambda x: x[:50] if len(x) > 50 else x)
                    encoded_data = self.encoders[feature].fit_transform(cleaned_data)
                else:
                    cleaned_data = feature_data.astype(str).apply(lambda x: x[:50] if len(x) > 50 else x)
                    try:
                        encoded_data = self.encoders[feature].transform(cleaned_data)
                    except ValueError:
                        # Handle unknown categories by fitting again
                        encoded_data = self.encoders[feature].fit_transform(cleaned_data)
                
                processed_data.append(encoded_data.reshape(-1, 1))
            else:
                # For numeric features, handle missing values and reshape
                numeric_data = pd.to_numeric(feature_data, errors='coerce').fillna(feature_data.mean())
                processed_data.append(numeric_data.values.reshape(-1, 1))
        
        # Combine all processed features
        X = np.hstack(processed_data)
        
        # Scale the combined features
        feature_count = len(features)
        if feature_count not in self.scalers:
            self.scalers[feature_count] = StandardScaler()
            X = self.scalers[feature_count].fit_transform(X)
        else:
            X = self.scalers[feature_count].transform(X)
        
        return X

    def perform_clustering(self, segmentation_type: str, n_clusters: int = 4) -> Tuple[pd.DataFrame, float, pd.DataFrame]:
        """Perform clustering for a specific segmentation type."""
        features = self.segmentation_features.get(segmentation_type, [])
        if not features:
            st.error(f"No features defined for segmentation type: {segmentation_type}")
            return None, None, None
        
        # Verify all features exist in the data
        missing_features = [f for f in features if f not in self.data.columns]
        if missing_features:
            st.error(f"Missing required features for {segmentation_type} segmentation: {missing_features}")
            return None, None, None
        
        try:
            # Preprocess features
            X = self.preprocess_features(features)
            
            # Remove any remaining NaN values
            X = np.nan_to_num(X)
            
            # Perform clustering
            model = KMeans(
                n_clusters=n_clusters,
                init='k-means++',
                n_init=10,
                max_iter=300,
                random_state=42
            )
            
            cluster_labels = model.fit_predict(X)
            sil_score = silhouette_score(X, cluster_labels)
            
            # Store the model
            self.models[segmentation_type] = model
            
            # Add cluster labels to data and convert to string for Arrow compatibility
            self.data[f'{segmentation_type}_Cluster'] = cluster_labels.astype(str)
            
            # Calculate cluster centers and decode categorical features
            cluster_centers = pd.DataFrame(
                self.scalers[len(features)].inverse_transform(model.cluster_centers_),
                columns=features
            )
            
            # Decode categorical features in cluster centers
            for feature in features:
                if feature in self.encoders:
                    try:
                        cluster_centers[feature] = self.encoders[feature].inverse_transform(
                            cluster_centers[feature].round().astype(int)
                        )
                    except:
                        # If decoding fails, leave as numeric values
                        pass
            
            # Convert cluster centers index to string for Arrow compatibility
            cluster_centers.index = [str(i) for i in range(len(cluster_centers))]
            cluster_centers.index.name = 'Cluster'
            
            return self.data, sil_score, cluster_centers
            
        except Exception as e:
            st.error(f"Error during clustering for {segmentation_type}: {str(e)}")
            return None, None, None

    def compute_elbow_method(self, segmentation_type: str, max_clusters: int = 10) -> Tuple[List[float], List[float]]:
        """Compute elbow curve for a specific segmentation type."""
        features = self.segmentation_features.get(segmentation_type, [])
        if not features:
            st.error(f"No features defined for segmentation type: {segmentation_type}")
            return None, None
        
        # Verify all features exist in the data
        missing_features = [f for f in features if f not in self.data.columns]
        if missing_features:
            st.error(f"Missing required features for {segmentation_type} segmentation: {missing_features}")
            return None, None
        
        try:
            # Preprocess features
            X = self.preprocess_features(features)
            X = np.nan_to_num(X)
            
            wcss = []
            silhouette_scores = []
            
            for i in range(2, max_clusters + 1):
                kmeans = KMeans(
                    n_clusters=i,
                    init='k-means++',
                    n_init=10,
                    max_iter=300,
                    random_state=42
                )
                kmeans.fit(X)
                wcss.append(kmeans.inertia_)
                
                # Calculate silhouette score
                labels = kmeans.labels_
                silhouette_avg = silhouette_score(X, labels)
                silhouette_scores.append(silhouette_avg)
            
            return wcss, silhouette_scores
            
        except Exception as e:
            st.error(f"Error computing elbow curve for {segmentation_type}: {str(e)}")
            return None, None

    def get_cluster_profiles(self, segmentation_type: str) -> Optional[pd.DataFrame]:
        """Generate profiles for each cluster in a segmentation type."""
        cluster_col = f'{segmentation_type}_Cluster'
        if cluster_col not in self.data.columns:
            st.error(f"No clustering results found for {segmentation_type}")
            return None
        
        try:
            features = self.segmentation_features[segmentation_type]
            # Verify all features exist
            available_features = [f for f in features if f in self.data.columns]
            if len(available_features) < len(features):
                st.warning(f"Some features are missing for {segmentation_type} profiles: {set(features) - set(available_features)}")
            
            profiles = self.data.groupby(cluster_col)[available_features].agg([
                'mean', 'std', 'count'
            ]).round(2)
            
            # Add percentage of total
            total = len(self.data)
            profiles['percentage'] = (profiles[available_features[0]]['count'] / total * 100).round(1)
            
            # Convert index to string for Arrow compatibility
            profiles = profiles.reset_index()
            profiles[cluster_col] = profiles[cluster_col].astype(str)
            
            return profiles
            
        except Exception as e:
            st.error(f"Error generating cluster profiles for {segmentation_type}: {str(e)}")
            return None

    def get_segment_descriptions(self, segmentation_type: str) -> Dict[int, str]:
        """Generate descriptions for each segment based on cluster characteristics."""
        if f'{segmentation_type}_Cluster' not in self.data.columns:
            return {}
        
        profiles = self.get_cluster_profiles(segmentation_type)
        if profiles is None:
            return {}
        
        descriptions = {}
        features = self.segmentation_features[segmentation_type]
        available_features = [f for f in features if f in self.data.columns]
        
        for cluster in range(len(profiles)):
            profile = profiles.xs(cluster, level=0)
            
            # Generate description based on feature values
            feature_descriptions = []
            for feature in available_features:
                mean_val = profile[feature]['mean']
                if isinstance(mean_val, (int, float)):
                    if mean_val > profile[feature]['mean'].mean():
                        feature_descriptions.append(f"high {feature}")
                    else:
                        feature_descriptions.append(f"low {feature}")
                        
            description = f"Segment representing {profiles['percentage'][cluster]}% of customers, "
            description += f"characterized by {', '.join(feature_descriptions)}"
            descriptions[cluster] = description
            
        return descriptions

def perform_all_segmentations(data: pd.DataFrame, n_clusters: int = 4) -> Dict[str, Tuple[pd.DataFrame, float, pd.DataFrame]]:
    """Perform clustering for all segmentation types."""
    analyzer = SegmentationAnalyzer(data)
    results = {}
    
    for segmentation_type in analyzer.segmentation_features.keys():
        result = analyzer.perform_clustering(segmentation_type, n_clusters)
        if result[0] is not None:
            results[segmentation_type] = result
            
    return results