import streamlit as st
import plotly.express as px
import plotly.graph_objects as go
import pandas as pd
import numpy as np
from typing import List, Dict, Optional

def plot_elbow_curve(wcss: List[float], silhouette_scores: Optional[List[float]] = None, segmentation_type: str = ""):
    """Plot the elbow curve and optionally the silhouette scores."""
    if wcss is None:
        st.error("No data available for elbow curve visualization")
        return

    fig = go.Figure()
    
    # Plot WCSS
    x_range = list(range(2, len(wcss) + 2))
    fig.add_trace(go.Scatter(
        x=x_range,
        y=wcss,
        mode='lines+markers',
        name='Within-Cluster Sum of Squares',
        line=dict(color='blue')
    ))
    
    # Add silhouette scores if available
    if silhouette_scores is not None:
        fig.add_trace(go.Scatter(
            x=x_range,
            y=silhouette_scores,
            mode='lines+markers',
            name='Silhouette Score',
            yaxis='y2',
            line=dict(color='red')
        ))
        
        fig.update_layout(
            yaxis2=dict(
                title='Silhouette Score',
                overlaying='y',
                side='right',
                range=[0, 1]
            )
        )

    fig.update_layout(
        title=f'Elbow Method and Silhouette Analysis for {segmentation_type} Segmentation',
        xaxis_title='Number of Clusters',
        yaxis_title='WCSS',
        hovermode='x unified',
        showlegend=True
    )
    
    st.plotly_chart(fig)

def plot_segment_distribution(data: pd.DataFrame, segmentation_type: str):
    """Create an enhanced segment distribution visualization."""
    cluster_col = f'{segmentation_type}_Cluster'
    if cluster_col not in data.columns:
        st.error(f"No clustering data available for {segmentation_type}")
        return

    # Calculate cluster statistics
    cluster_stats = data.groupby(cluster_col).agg({
        'Customer_ID': 'count',
        'Transaction_Amount_VND': 'mean',
        'Interaction_Count': 'mean',
        'Loyalty_Points_Earned': 'mean'
    }).round(2)
    
    cluster_stats.columns = ['Count', 'Avg Transaction', 'Avg Interactions', 'Avg Loyalty Points']
    
    # Convert index to string to avoid Arrow serialization issues
    cluster_stats.index = cluster_stats.index.astype(str)
    
    # Create distribution plot
    fig = go.Figure()
    
    # Bar chart for cluster sizes
    fig.add_trace(go.Bar(
        x=cluster_stats.index,
        y=cluster_stats['Count'],
        name='Segment Size',
        text=cluster_stats['Count'],
        textposition='auto',
    ))
    
    # Update layout
    fig.update_layout(
        title=f'{segmentation_type} Segment Distribution',
        xaxis_title='Segment',
        yaxis_title='Number of Customers',
        showlegend=True,
        height=500
    )
    
    st.plotly_chart(fig)
    
    # Convert DataFrame to a format compatible with Arrow
    display_stats = cluster_stats.copy()
    display_stats = display_stats.reset_index()
    display_stats.columns = ['Segment'] + list(display_stats.columns[1:])
    
    # Display segment statistics
    st.write("Segment Statistics:")
    st.write(display_stats)

def plot_segment_characteristics(data: pd.DataFrame, segmentation_type: str, features: List[str]):
    """Plot characteristics of each segment using a heatmap."""
    cluster_col = f'{segmentation_type}_Cluster'
    if cluster_col not in data.columns:
        st.error(f"No clustering data available for {segmentation_type}")
        return

    # Calculate z-scores for each feature in each segment
    segment_profiles = []
    
    for segment in data[cluster_col].unique():
        segment_data = data[data[cluster_col] == segment][features]
        segment_mean = segment_data.mean()
        overall_mean = data[features].mean()
        overall_std = data[features].std()
        
        z_scores = (segment_mean - overall_mean) / overall_std
        segment_profiles.append(z_scores)
    
    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=np.array(segment_profiles),
        x=features,
        y=[f'Segment {i}' for i in range(len(segment_profiles))],
        colorscale='RdBu',
        zmid=0
    ))
    
    fig.update_layout(
        title=f'{segmentation_type} Segment Characteristics',
        xaxis_title='Features',
        yaxis_title='Segments',
        height=400
    )
    
    st.plotly_chart(fig)

def plot_segment_scatter(data: pd.DataFrame, segmentation_type: str, features: List[str]):
    """Create an interactive scatter plot for segment visualization."""
    cluster_col = f'{segmentation_type}_Cluster'
    if cluster_col not in data.columns:
        st.error(f"No clustering data available for {segmentation_type}")
        return

    # Let user select dimensions to plot
    numeric_features = [f for f in features if data[f].dtype in ['int64', 'float64']]
    if len(numeric_features) < 2:
        st.error("Not enough numeric features for scatter plot")
        return
    
    col1, col2 = st.columns(2)
    with col1:
        x_axis = st.selectbox(
            'Select X-axis:',
            numeric_features,
            index=0,
            key=f"scatter_x_{segmentation_type}"
        )
    with col2:
        y_axis = st.selectbox(
            'Select Y-axis:',
            numeric_features,
            index=min(1, len(numeric_features)-1),
            key=f"scatter_y_{segmentation_type}"
        )

    # Optional size variable
    size_var = st.selectbox(
        'Select size variable (optional):',
        ['None'] + numeric_features,
        key=f"scatter_size_{segmentation_type}"
    )
    
    # Create scatter plot
    fig = px.scatter(
        data,
        x=x_axis,
        y=y_axis,
        color=cluster_col,
        size=None if size_var == 'None' else size_var,
        hover_data=features,
        title=f'{segmentation_type} Segments: {x_axis} vs {y_axis}',
        color_continuous_scale='viridis'
    )
    
    fig.update_layout(
        height=600,
        hovermode='closest',
        legend_title_text='Segment'
    )
    
    st.plotly_chart(fig)

def plot_radar_chart(data: pd.DataFrame, segmentation_type: str, features: List[str]):
    """Create a radar chart comparing segments."""
    cluster_col = f'{segmentation_type}_Cluster'
    if cluster_col not in data.columns:
        st.error(f"No clustering data available for {segmentation_type}")
        return

    # Calculate mean values for each segment
    segment_means = data.groupby(cluster_col)[features].mean()
    
    # Create radar chart
    fig = go.Figure()
    
    for segment in segment_means.index:
        fig.add_trace(go.Scatterpolar(
            r=segment_means.loc[segment],
            theta=features,
            name=f'Segment {segment}',
            fill='toself'
        ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 1]
            )),
        showlegend=True,
        title=f'{segmentation_type} Segment Comparison'
    )
    
    st.plotly_chart(fig)

def create_segment_dashboard(data: pd.DataFrame, segmentation_type: str, features: List[str]):
    """Create a comprehensive dashboard for a segmentation type."""
    st.subheader(f"{segmentation_type} Segmentation Analysis")
    
    # Create tabs for different visualizations
    tabs = st.tabs([
        "Distribution",
        "Characteristics",
        "Scatter Plot",
        "Radar Chart",
        "Detailed Stats"
    ])
    
    with tabs[0]:
        plot_segment_distribution(data, segmentation_type)
        
    with tabs[1]:
        plot_segment_characteristics(data, segmentation_type, features)
        
    with tabs[2]:
        plot_segment_scatter(data, segmentation_type, features)
        
    with tabs[3]:
        plot_radar_chart(data, segmentation_type, features)
        
    with tabs[4]:
        st.write("Detailed Segment Statistics")
        cluster_col = f'{segmentation_type}_Cluster'
        detailed_stats = data.groupby(cluster_col)[features].agg([
            'mean', 'std', 'min', 'max', 'count'
        ]).round(2)
        
        # Convert index to string and reset index for Arrow compatibility
        detailed_stats = detailed_stats.reset_index()
        detailed_stats[cluster_col] = detailed_stats[cluster_col].astype(str)
        
        st.write(detailed_stats)