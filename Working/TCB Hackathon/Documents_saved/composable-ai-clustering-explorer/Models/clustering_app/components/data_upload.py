import pandas as pd
import streamlit as st
from .data_enrichment import enrich_customer_data

def load_data(file_path: str) -> pd.DataFrame:
    """Load and enrich customer data from CSV file."""
    try:
        # Load raw data
        df = pd.read_csv(file_path)
        
        # Basic validation
        required_cols = [
            'Customer_ID', 'Income_Level', 'Gender', 'Location', 'Customer_Segment',
            'Channel', 'Interaction_Type', 'Journey_Stage', 'Transaction_Amount_VND',
            'Satisfaction_Score', 'Response_Time_Minutes', 'Loyalty_Points_Earned',
            'Age', 'Timestamp'
        ]
        
        # Check for missing columns
        missing_cols = [col for col in required_cols if col not in df.columns]
        if missing_cols:
            st.error(f"Missing required columns: {', '.join(missing_cols)}")
            return None

        # Convert timestamp
        if 'Timestamp' in df.columns:
            try:
                df['Timestamp'] = pd.to_datetime(df['Timestamp'])
            except Exception as e:
                st.warning(f"Warning: Could not convert Timestamp column: {str(e)}")

        # Handle missing values
        numeric_cols = [
            'Transaction_Amount_VND', 'Satisfaction_Score',
            'Response_Time_Minutes', 'Loyalty_Points_Earned', 'Age'
        ]
        
        categorical_cols = [
            'Gender', 'Location', 'Customer_Segment', 'Channel',
            'Interaction_Type', 'Journey_Stage'
        ]
        
        # Fill missing values
        df[numeric_cols] = df[numeric_cols].fillna(0)
        df[categorical_cols] = df[categorical_cols].fillna('Unknown')
        
        # Validate value ranges
        if (df['Satisfaction_Score'] < 0).any() or (df['Satisfaction_Score'] > 10).any():
            st.warning("Warning: Satisfaction scores found outside expected range (0-10)")
        
        if (df['Age'] < 18).any() or (df['Age'] > 120).any():
            st.warning("Warning: Age values found outside expected range (18-120)")
        
        if (df['Transaction_Amount_VND'] < 0).any():
            st.warning("Warning: Negative transaction amounts found")

        # Enrich data with synthetic features
        try:
            df = enrich_customer_data(df)
            st.success("Data enrichment completed successfully!")
        except Exception as e:
            st.error(f"Error during data enrichment: {str(e)}")
            return None

        # Display data quality metrics
        display_data_quality_metrics(df)
        
        return df

    except FileNotFoundError:
        st.error(f"Error: File {file_path} not found.")
        return None
    except Exception as e:
        st.error(f"Error loading file: {str(e)}")
        return None

def display_data_quality_metrics(df: pd.DataFrame) -> None:
    """Display data quality metrics in the Streamlit app."""
    with st.expander("Data Quality Metrics"):
        # Basic metrics
        st.write("Basic Metrics:")
        metrics = {
            "Total Records": len(df),
            "Total Features": len(df.columns),
            "Memory Usage": f"{df.memory_usage().sum() / 1024**2:.2f} MB"
        }
        st.write(metrics)
        
        # Missing values
        st.write("\nMissing Values:")
        missing = df.isnull().sum()
        missing = missing[missing > 0]
        if not missing.empty:
            st.write(missing)
        else:
            st.write("No missing values found")
        
        # Data types
        st.write("\nData Types:")
        st.write(df.dtypes)
        
        # Value distributions
        st.write("\nValue Distributions:")
        numeric_cols = df.select_dtypes(include=['int64', 'float64']).columns
        if len(numeric_cols) > 0:
            st.write(df[numeric_cols].describe())