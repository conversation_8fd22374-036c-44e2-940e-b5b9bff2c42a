import pandas as pd
import numpy as np
from typing import Dict, List
import streamlit as st

def calculate_basic_metrics(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate basic customer metrics from raw data."""
    # Create a copy to avoid modifying the original
    df = df.copy()
    
    # Calculate interaction count per customer
    df['Interaction_Count'] = df.groupby('Customer_ID')['Customer_ID'].transform('count')
    
    # Calculate average transaction amount per customer
    df['Avg_Transaction_Amount'] = df.groupby('Customer_ID')['Transaction_Amount_VND'].transform('mean')
    
    # Calculate total transaction amount per customer
    df['Total_Transaction_Amount'] = df.groupby('Customer_ID')['Transaction_Amount_VND'].transform('sum')
    
    return df

def generate_psychographic_data(df: pd.DataFrame) -> pd.DataFrame:
    """Generate synthetic psychographic data based on existing features."""
    # Define lifestyle categories and their weights
    lifestyles = ['Active', 'Budget-Conscious', 'Luxury', 'Traditional', 'Digital-First']
    
    # Generate lifestyle based on transaction amount and interaction type
    conditions = [
        (df['Avg_Transaction_Amount'] > df['Avg_Transaction_Amount'].quantile(0.75)) & 
        (df['Interaction_Count'] > df['Interaction_Count'].quantile(0.75)),
        (df['Avg_Transaction_Amount'] < df['Avg_Transaction_Amount'].quantile(0.25)),
        (df['Avg_Transaction_Amount'] > df['Avg_Transaction_Amount'].quantile(0.9)),
        (df['Channel'] == 'Branch'),
        (df['Channel'].isin(['Mobile', 'Online']))
    ]
    choices = lifestyles
    df['Lifestyle'] = np.select(conditions, choices, default='Traditional')
    
    return df

def generate_needs_based_data(df: pd.DataFrame) -> pd.DataFrame:
    """Generate customer needs data based on interaction patterns."""
    # Define needs categories
    needs_mapping = {
        'Investment': ['Investment_Inquiry', 'Wealth_Management'],
        'Convenience': ['Mobile_Banking', 'Online_Transfer'],
        'Security': ['Security_Check', 'Fraud_Report'],
        'Support': ['Customer_Service', 'Technical_Support'],
        'Growth': ['Loan_Application', 'Business_Services']
    }
    
    # Map interaction types to needs
    df['Primary_Need'] = df['Interaction_Type'].map({
        k: v for v, ks in needs_mapping.items() for k in ks
    }).fillna('Basic_Banking')
    
    return df

def calculate_customer_value(df: pd.DataFrame) -> pd.DataFrame:
    """Calculate customer lifetime value and value segment."""
    try:
        # Calculate basic CLV metrics
        if 'Timestamp' in df.columns:
            df['Transaction_Recency'] = (pd.to_datetime('now') - pd.to_datetime(df['Timestamp'])).dt.days
        else:
            df['Transaction_Recency'] = 0  # Default if no timestamp available
            
        df['Customer_Lifetime_Value'] = (
            df['Total_Transaction_Amount'] * 
            df['Interaction_Count'] * 
            (1 / (1 + df['Transaction_Recency']/365))
        )
        
        # Assign value segments
        df['Value_Segment'] = pd.qcut(
            df['Customer_Lifetime_Value'],
            q=4,
            labels=['Bronze', 'Silver', 'Gold', 'Platinum']
        )
    except Exception as e:
        st.warning(f"Warning in CLV calculation: {str(e)}")
        # Provide default values if calculation fails
        df['Customer_Lifetime_Value'] = df['Total_Transaction_Amount']
        df['Value_Segment'] = 'Not Calculated'
    
    return df

def generate_firmographic_data(df: pd.DataFrame) -> pd.DataFrame:
    """Generate synthetic firmographic data for business customers."""
    # Identify business customers (synthetic logic)
    is_business = (
        (df['Avg_Transaction_Amount'] > df['Avg_Transaction_Amount'].quantile(0.8)) & 
        (df['Interaction_Type'].isin(['Business_Services', 'Loan_Application']))
    )
    
    # Generate company sizes
    company_sizes = ['Small', 'Medium', 'Large', 'Enterprise']
    size_weights = [0.4, 0.3, 0.2, 0.1]
    
    # Generate industries
    industries = [
        'Retail', 'Manufacturing', 'Technology', 'Services', 
        'Construction', 'Healthcare', 'Education', 'Other'
    ]
    
    # Add firmographic data for business customers
    df.loc[is_business, 'Company_Size'] = np.random.choice(
        company_sizes, 
        size=is_business.sum(), 
        p=size_weights
    )
    df.loc[is_business, 'Industry'] = np.random.choice(
        industries, 
        size=is_business.sum()
    )
    
    # Fill non-business customers
    df['Company_Size'] = df['Company_Size'].fillna('Individual')
    df['Industry'] = df['Industry'].fillna('Individual')
    
    return df

def enrich_customer_data(df: pd.DataFrame) -> pd.DataFrame:
    """Main function to enrich customer data with all synthetic features."""
    try:
        enriched_df = df.copy()
        
        # Calculate basic metrics first
        enriched_df = calculate_basic_metrics(enriched_df)
        
        # Generate synthetic data for each dimension
        enriched_df = generate_psychographic_data(enriched_df)
        enriched_df = generate_needs_based_data(enriched_df)
        enriched_df = calculate_customer_value(enriched_df)
        enriched_df = generate_firmographic_data(enriched_df)
        
        # Add lifecycle stage if not present
        if 'Journey_Stage' not in enriched_df.columns:
            stages = ['Awareness', 'Consideration', 'Purchase', 'Retention', 'Advocacy']
            enriched_df['Journey_Stage'] = np.random.choice(stages, size=len(enriched_df))
        
        # Validate required columns
        required_columns = [
            'Customer_ID', 'Transaction_Amount_VND', 'Interaction_Count',
            'Channel', 'Interaction_Type', 'Satisfaction_Score',
            'Loyalty_Points_Earned', 'Age', 'Gender', 'Income_Level',
            'Location'
        ]
        
        missing_columns = [col for col in required_columns if col not in enriched_df.columns]
        if missing_columns:
            st.warning(f"Warning: Missing columns: {', '.join(missing_columns)}")
            # Add missing columns with default values
            for col in missing_columns:
                if col in ['Age', 'Satisfaction_Score', 'Loyalty_Points_Earned']:
                    enriched_df[col] = 0
                elif col in ['Gender', 'Location', 'Channel', 'Interaction_Type']:
                    enriched_df[col] = 'Unknown'
                elif col == 'Income_Level':
                    enriched_df[col] = 'Medium'
        
        return enriched_df
        
    except Exception as e:
        st.error(f"Error in data enrichment: {str(e)}")
        return df

def get_segmentation_features() -> Dict[str, List[str]]:
    """Return the features used for each segmentation type."""
    return {
        'Demographic': ['Age', 'Gender', 'Income_Level'],
        'Geographic': ['Location'],
        'Psychographic': ['Lifestyle'],
        'Behavioral': [
            'Interaction_Count', 'Avg_Transaction_Amount',
            'Satisfaction_Score', 'Loyalty_Points_Earned'
        ],
        'Technographic': ['Channel'],
        'Needs_Based': ['Primary_Need'],
        'Value_Based': ['Customer_Lifetime_Value', 'Value_Segment'],
        'Firmographic': ['Company_Size', 'Industry'],
        'Lifecycle': ['Journey_Stage']
    } 