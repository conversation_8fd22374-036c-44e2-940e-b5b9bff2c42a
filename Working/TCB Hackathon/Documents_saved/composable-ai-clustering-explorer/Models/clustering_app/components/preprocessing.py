import pandas as pd
from sklearn.preprocessing import LabelEncoder, StandardScaler

def preprocess_data(df):
    # Verify required columns
    required_cols = [
        'Customer_ID', 'Income_Level', 'Gender', 'Location', 'Customer_Segment', 'Channel',
        'Interaction_Type', 'Journey_Stage', 'Personalized_Offer_Accepted', 'Biometric_Verified',
        'Merchant_Transaction', 'Transaction_Amount_VND', 'Satisfaction_Score',
        'Response_Time_Minutes', 'Loyalty_Points_Earned', 'Age'
    ]
    missing_cols = [col for col in required_cols if col not in df.columns]
    if missing_cols:
        raise ValueError(f"Missing columns in data: {missing_cols}")

    # Check for NaN in input data
    if df.isna().any().any():
        print("NaN values found in input data:")
        print(df.isna().sum())
        # Fill NaN with 0 for numerical columns
        numerical_cols = [
            'Transaction_Amount_VND', 'Satisfaction_Score', 'Response_Time_Minutes',
            'Loyalty_Points_Earned', 'Age'
        ]
        df[numerical_cols] = df[numerical_cols].fillna(0)

    # Define column types
    ordinal_cols = ['Income_Level']
    nominal_cols = [
        'Gender', 'Location', 'Customer_Segment', 'Channel', 'Interaction_Type',
        'Journey_Stage', 'Personalized_Offer_Accepted', 'Biometric_Verified', 'Merchant_Transaction'
    ]
    numerical_cols = [
        'Transaction_Amount_VND', 'Satisfaction_Score', 'Response_Time_Minutes',
        'Loyalty_Points_Earned', 'Age'
    ]

    # Encode ordinal columns
    le = LabelEncoder()
    for col in ordinal_cols:
        df[col] = le.fit_transform(df[col].astype(str))  # Handle potential NaN

    # Scale numerical columns
    scaler = StandardScaler()
    df[numerical_cols] = scaler.fit_transform(df[numerical_cols])
    # Fill any NaN introduced by scaling (unlikely, but for safety)
    df[numerical_cols] = df[numerical_cols].fillna(0)

    # Encode nominal columns but keep original columns for aggregation
    df_encoded = pd.get_dummies(df[nominal_cols], drop_first=False)
    df_final = pd.concat([df, df_encoded], axis=1)  # Keep original columns

    # Aggregate to customer level
    customer_features = df_final.groupby('Customer_ID').agg({
        'Transaction_Amount_VND': ['mean', 'sum', 'count'],
        'Satisfaction_Score': 'mean',
        'Response_Time_Minutes': 'mean',
        'Loyalty_Points_Earned': 'sum',
        'Age': 'first',
        'Income_Level': 'first',
        'Customer_Segment': lambda x: x.mode()[0] if not x.isna().all() else 'Unknown',
        'Channel': lambda x: x.mode()[0] if not x.isna().all() else 'Unknown',
        'Interaction_Type': lambda x: x.mode()[0] if not x.isna().all() else 'Unknown'
    }).reset_index()

    # Flatten column names
    customer_features.columns = [
        'Customer_ID', 'Avg_Transaction_Amount', 'Total_Transaction_Amount', 'Interaction_Count',
        'Avg_Satisfaction_Score', 'Avg_Response_Time', 'Total_Loyalty_Points',
        'Age', 'Income_Level', 'Customer_Segment', 'Most_Frequent_Channel', 'Most_Frequent_Interaction'
    ]

    # Fill NaN in aggregated numerical columns with 0
    agg_numerical_cols = [
        'Avg_Transaction_Amount', 'Total_Transaction_Amount', 'Interaction_Count',
        'Avg_Satisfaction_Score', 'Avg_Response_Time', 'Total_Loyalty_Points', 'Age', 'Income_Level'
    ]
    customer_features[agg_numerical_cols] = customer_features[agg_numerical_cols].fillna(0)

    # Check for NaN in customer_features
    if customer_features.isna().any().any():
        print("NaN values found in customer_features:")
        print(customer_features.isna().sum())

    # Debug: Print data types
    print("Data types in customer_features:")
    print(customer_features.dtypes)

    return customer_features