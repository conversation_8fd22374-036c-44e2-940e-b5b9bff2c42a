import { AppStep } from './types';

export const APP_TITLE = "Composable AI Clustering Explorer";

export const STEPS_CONFIG: { id: AppStep; name: string; description: string }[] = [
  { id: AppStep.UPLOAD, name: 'Upload Data', description: 'Start by uploading your CSV file.' },
  { id: AppStep.OVERVIEW, name: 'Data Overview', description: 'Review summary statistics of your data.' },
  { id: AppStep.CONFIGURE, name: 'Configure Analysis', description: 'Set preprocessing and clustering parameters.' },
  { id: AppStep.RESULTS_INSIGHTS, name: 'Results & Insights', description: 'Visualize clusters and get AI-powered insights.' },
];

export const SCALING_OPTIONS = ['None', 'StandardScaler (Mock)', 'MinMaxScaler (Mock)'];
export const DIMENSIONALITY_REDUCTION_OPTIONS = ['None', 'PCA (Mock)', 'UMAP (Mock)'];
export const CLUSTERING_ALGORITHMS = ['KMeans (Mock)', 'DBSCAN (Mock)', 'HDBSCAN (Mock)'];

export const DEFAULT_NUM_CLUSTERS = 3;
export const MAX_NUM_CLUSTERS = 10;
export const MIN_NUM_CLUSTERS = 2;

export const MOCK_CLUSTER_COLORS = [
  '#3b82f6', // blue-500
  '#10b981', // emerald-500
  '#ef4444', // red-500
  '#f97316', // orange-500
  '#8b5cf6', // violet-500
  '#ec4899', // pink-500
  '#f59e0b', // amber-500
  '#06b6d4', // cyan-500
  '#6366f1', // indigo-500
  '#84cc16', // lime-500
];

export const OLLAMA_API_URL = 'http://localhost:11434/api/generate';
export const DEFAULT_OLLAMA_MODEL = 'llama3';
