import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer, Cell } from 'recharts';
import { PlotDataPoint, ClusteringConfig, RawDataRow } from '../types';
import { MOCK_CLUSTER_COLORS } from '../constants';

interface ClusterVisualizationProps {
  plotData: PlotDataPoint[];
  config: ClusteringConfig;
}

const CustomTooltip: React.FC<any> = ({ active, payload }) => {
  if (active && payload && payload.length) {
    const data: PlotDataPoint = payload[0].payload;
    const originalData: RawDataRow | undefined = data.tooltipPayload;

    return (
      <div className="bg-white p-2 shadow-lg rounded border border-gray-300 text-xs opacity-95">
        <p className="font-bold mb-1 text-primary-dark">Cluster: {data.cluster}</p>
        <p><span className="font-semibold">X:</span> {data.x.toFixed(2)}</p>
        <p><span className="font-semibold">Y:</span> {data.y.toFixed(2)}</p>
        {originalData && (
          <>
            <hr className="my-1 border-gray-200"/>
            <p className="font-semibold mt-1 text-neutral-dark">Original Data (Top 5 fields):</p>
            {Object.entries(originalData).slice(0, 5).map(([key, value]) => (
               <p key={key} className="truncate" title={`${key}: ${value}`}>
                 <span className="font-medium">{key}:</span> {String(value).substring(0,25)}{String(value).length > 25 ? '...' : ''}
               </p>
            ))}
            {Object.keys(originalData).length > 5 && <p className="text-gray-400">...</p>}
          </>
        )}
      </div>
    );
  }
  return null;
};


const ClusterVisualization: React.FC<ClusterVisualizationProps> = ({ plotData, config }) => {
  if (!plotData || plotData.length === 0) {
    return <p className="text-center text-neutral-DEFAULT py-10">No data available for visualization.</p>;
  }

  const series = Array.from({ length: config.numClusters }, (_, i) => ({
    name: `Cluster ${i}`,
    data: plotData.filter(p => p.cluster === i),
    color: MOCK_CLUSTER_COLORS[i % MOCK_CLUSTER_COLORS.length],
  }));

  return (
    <div className="space-y-2">
      {/* Title moved to Card.Header */}
      <p className="text-xs text-neutral-DEFAULT px-1">
        Mock 2D visualization. Colors represent clusters. Hover for details.
      </p>
      <div style={{ width: '100%', height: 350 }} role="figure" aria-label="Cluster scatter plot">
        <ResponsiveContainer>
          <ScatterChart
            margin={{
              top: 5, right: 20, bottom: 20, left: -10, // Adjusted left margin for YAxis label
            }}
          >
            <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
            <XAxis 
              type="number" 
              dataKey="x" 
              name="Dim 1" 
              label={{ value: 'Dimension 1', position: 'insideBottomRight', offset: -15, style: {fontSize: '0.75rem', fill: '#6b7280'} }} 
              tick={{fontSize: '0.7rem', fill: '#6b7280'}}
            />
            <YAxis 
              type="number" 
              dataKey="y" 
              name="Dim 2" 
              label={{ value: 'Dimension 2', angle: -90, position: 'insideLeft', offset: 10, style: {fontSize: '0.75rem', fill: '#6b7280'} }}
              tick={{fontSize: '0.7rem', fill: '#6b7280'}}
            />
            <Tooltip content={<CustomTooltip />} cursor={{ strokeDasharray: '3 3' }}/>
            <Legend wrapperStyle={{fontSize: "0.8rem", paddingTop: "10px"}} />
            {series.map((s) => (
              <Scatter key={s.name} name={s.name} data={s.data} fill={s.color} shape="circle" />
            ))}
          </ScatterChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default ClusterVisualization;