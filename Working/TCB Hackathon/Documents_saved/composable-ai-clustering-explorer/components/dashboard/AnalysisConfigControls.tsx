import React from 'react';
import { ClusteringConfig } from '../../types';
import { SCALING_OPTIONS, DIMENSIONALITY_REDUCTION_OPTIONS, CLUSTERING_ALGORITHMS, DEFAULT_NUM_CLUSTERS, MIN_NUM_CLUSTERS, MAX_NUM_CLUSTERS } from '../../constants';
import { Select } from '../common/Select';
import { Button } from '../common/Button';
import Card from '../common/Card';
import { CogIcon, BeakerIcon, ArrowPathIcon } from '../common/Icons';

interface AnalysisConfigControlsProps {
  config: ClusteringConfig;
  onConfigChange: (newConfig: Partial<ClusteringConfig>) => void;
  onRunAnalysis: () => void;
  onReset: () => void;
  isAnalyzing: boolean;
  isFileUploaded: boolean;
}

const AnalysisConfigControls: React.FC<AnalysisConfigControlsProps> = ({ 
    config, onConfigChange, onRunAnalysis, onReset, isAnalyzing, isFileUploaded 
}) => {
  
  const handleNumClustersChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = parseInt(e.target.value, 10);
    if (isNaN(value)) value = DEFAULT_NUM_CLUSTERS;
    value = Math.max(MIN_NUM_CLUSTERS, Math.min(MAX_NUM_CLUSTERS, value));
    onConfigChange({ numClusters: value });
  };
  
  return (
    <Card className="dashboard-card">
      <Card.Header>
        <div className="flex items-center">
            <CogIcon className="h-6 w-6 text-primary mr-2" />
            Analysis Configuration
        </div>
      </Card.Header>
      <Card.Body className="space-y-4">
        <Select
          label="Scaling Method"
          value={config.scaling}
          onChange={(e) => onConfigChange({ scaling: e.target.value })}
          options={SCALING_OPTIONS.map(opt => ({ value: opt, label: opt }))}
          helpText="Mock: Simulates data scaling."
          disabled={isAnalyzing || !isFileUploaded}
        />
        <Select
          label="Dimensionality Reduction"
          value={config.dimensionalityReduction}
          onChange={(e) => onConfigChange({ dimensionalityReduction: e.target.value })}
          options={DIMENSIONALITY_REDUCTION_OPTIONS.map(opt => ({ value: opt, label: opt }))}
          helpText="Mock: Simulates 2D reduction."
          disabled={isAnalyzing || !isFileUploaded}
        />
        <Select
          label="Clustering Algorithm"
          value={config.clusteringAlgorithm}
          onChange={(e) => onConfigChange({ clusteringAlgorithm: e.target.value })}
          options={CLUSTERING_ALGORITHMS.map(opt => ({ value: opt, label: opt }))}
          helpText="Mock: Simulates algorithm."
          disabled={isAnalyzing || !isFileUploaded}
        />
        {config.clusteringAlgorithm.toLowerCase().includes('kmeans') && (
          <div>
            <label htmlFor="numClusters" className="block text-sm font-medium text-gray-700">
              Number of Clusters (K)
            </label>
            <input
              type="number"
              id="numClusters"
              name="numClusters"
              value={config.numClusters}
              onChange={handleNumClustersChange}
              min={MIN_NUM_CLUSTERS}
              max={MAX_NUM_CLUSTERS}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm disabled:bg-gray-100"
              disabled={isAnalyzing || !isFileUploaded}
            />
            <p className="mt-1 text-xs text-gray-500">Range: {MIN_NUM_CLUSTERS}-{MAX_NUM_CLUSTERS}.</p>
          </div>
        )}
        <div className="pt-2 space-y-3">
          <Button 
            onClick={onRunAnalysis} 
            isLoading={isAnalyzing} 
            disabled={isAnalyzing || !isFileUploaded}
            className="w-full flex items-center justify-center"
            size="md"
          >
            <BeakerIcon className="h-5 w-5 mr-2"/>
            {isAnalyzing ? 'Analyzing...' : 'Run Analysis & Get Insights'}
          </Button>
        </div>
      </Card.Body>
    </Card>
  );
};

export default AnalysisConfigControls;
