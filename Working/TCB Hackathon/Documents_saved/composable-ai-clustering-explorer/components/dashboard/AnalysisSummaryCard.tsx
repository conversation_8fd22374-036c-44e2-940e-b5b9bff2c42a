import React from 'react';
import { ClusteringConfig } from '../../types';
import Card from '../common/Card';
import { ChartPieIcon } from '../common/Icons'; // Or a more generic "summary" icon

interface AnalysisSummaryCardProps {
  config: ClusteringConfig;
  fileName: string | null;
}

const InfoItem: React.FC<{ label: string; value: string | number }> = ({ label, value }) => (
  <div>
    <p className="text-xs text-neutral-DEFAULT">{label}</p>
    <p className="font-medium text-neutral-dark text-sm truncate" title={String(value)}>{value}</p>
  </div>
);

const AnalysisSummaryCard: React.FC<AnalysisSummaryCardProps> = ({ config, fileName }) => {
  return (
    <Card className="dashboard-card">
      <Card.Header>
        <div className="flex items-center">
            <ChartPieIcon className="h-6 w-6 text-primary mr-2" />
            Analysis Overview
        </div>
      </Card.Header>
      <Card.Body>
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
          {fileName && <InfoItem label="Active Dataset" value={fileName} />}
          <InfoItem label="Clustering Algorithm" value={config.clusteringAlgorithm} />
          <InfoItem label="Number of Clusters" value={config.numClusters} />
          <InfoItem label="Scaling Method" value={config.scaling} />
          <InfoItem label="Dimensionality Reduction" value={config.dimensionalityReduction} />
        </div>
      </Card.Body>
    </Card>
  );
};

export default AnalysisSummaryCard;
