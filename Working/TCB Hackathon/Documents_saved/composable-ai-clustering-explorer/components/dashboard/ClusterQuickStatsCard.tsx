import React from 'react';
import { PlotDataPoint } from '../../types';
import Card from '../common/Card';
import { ListBulletIcon } from '../common/Icons';
import { MOCK_CLUSTER_COLORS } from '../../constants';


interface ClusterQuickStatsCardProps {
  plotData: PlotDataPoint[];
  numClusters: number;
}

const ClusterQuickStatsCard: React.FC<ClusterQuickStatsCardProps> = ({ plotData, numClusters }) => {
  const clusterCounts: { [key: number]: number } = {};
  for (let i = 0; i < numClusters; i++) {
    clusterCounts[i] = 0;
  }
  plotData.forEach(point => {
    if (clusterCounts[point.cluster] !== undefined) {
      clusterCounts[point.cluster]++;
    } else {
        // This case handles points that might have a cluster ID outside the expected range.
        // For mock data, this shouldn't happen if config.numClusters is accurate.
        // In a real scenario, this could indicate an issue.
        clusterCounts[point.cluster] = (clusterCounts[point.cluster] || 0) + 1;
    }
  });

  const sortedClusters = Object.entries(clusterCounts)
    .map(([clusterId, count]) => ({ id: parseInt(clusterId), count }))
    .sort((a, b) => a.id - b.id); // Sort by cluster ID

  return (
    <Card className="dashboard-card min-h-[200px] flex flex-col"> {/* Added min-height and flex for consistent sizing */}
      <Card.Header>
        <div className="flex items-center">
            <ListBulletIcon className="h-6 w-6 text-primary mr-2" />
            Cluster Quick Stats
        </div>
      </Card.Header>
      <Card.Body className="space-y-2 flex-grow overflow-y-auto max-h-60"> {/* Added flex-grow and scroll */}
        {sortedClusters.length > 0 ? (
            sortedClusters.map(item => (
            <div key={item.id} className="flex items-center justify-between text-sm p-1.5 bg-gray-50 rounded-md">
              <div className="flex items-center">
                <span 
                    className="h-3 w-3 rounded-full mr-2" 
                    style={{ backgroundColor: MOCK_CLUSTER_COLORS[item.id % MOCK_CLUSTER_COLORS.length] }}
                    aria-hidden="true"
                ></span>
                <span className="text-neutral-dark font-medium">Cluster {item.id}</span>
              </div>
              <span className="text-neutral-DEFAULT">{item.count.toLocaleString()} items</span>
            </div>
            ))
        ) : (
            <div className="flex flex-col items-center justify-center h-full">
                 <p className="text-sm text-neutral-DEFAULT">No cluster data to display.</p>
            </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default ClusterQuickStatsCard;
