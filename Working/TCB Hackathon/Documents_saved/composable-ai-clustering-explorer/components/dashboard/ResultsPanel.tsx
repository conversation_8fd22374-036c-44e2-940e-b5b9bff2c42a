import React, { useState } from 'react';
import { ClusteringResult, FullInsights } from '../../types';
import AnalysisSummaryCard from './AnalysisSummaryCard';
import AIInsightsDisplayCard from './AIInsightsDisplayCard';
import ClusterQuickStatsCard from './ClusterQuickStatsCard';
import ClusterVisualization from '../ClusterVisualization';
import Card from '../common/Card';
import { Loader } from '../common/Loader';
import { ChartBarIcon, LightBulbIcon, MagnifyingGlassIcon } from '../common/Icons'; // MagnifyingGlassIcon might be used in AIInsightsDisplayCard for details button
import Modal from '../common/Modal'; // For displaying full AI report
import { ollamaService } from '../../services/ollamaService'; // Added import

interface ResultsPanelProps {
  isAnalysisComplete: boolean;
  isAnalyzing: boolean; // Overall analysis in progress (includes insights)
  isGeneratingInsights: boolean; // Specifically insights generation phase
  clusteringResult: ClusteringResult | null;
  ollamaInsights: FullInsights | null;
  fileName: string | null;
}

const ResultsPanel: React.FC<ResultsPanelProps> = ({
  isAnalysisComplete,
  isAnalyzing,
  isGeneratingInsights,
  clusteringResult,
  ollamaInsights,
  fileName
}) => {
  const [showDetailedReportModal, setShowDetailedReportModal] = useState(false);

  if (isAnalyzing && !isAnalysisComplete) { // Initial analysis phase (before results object exists)
    return (
      <div className="flex flex-col items-center justify-center h-96 bg-white shadow-lg rounded-lg p-8 dashboard-card">
        <Loader size="lg" />
        <p className="mt-4 text-neutral-DEFAULT font-semibold">Performing analysis...</p>
        <p className="text-sm text-neutral-DEFAULT">Please wait while we process your data.</p>
      </div>
    );
  }
  
  if (!isAnalysisComplete || !clusteringResult) {
     return (
      <div className="flex flex-col items-center justify-center h-96 bg-white shadow-lg rounded-lg p-8 dashboard-card">
        <MagnifyingGlassIcon className="h-16 w-16 text-neutral-DEFAULT mb-4"/>
        <h3 className="text-xl font-semibold text-neutral-dark">Awaiting Analysis</h3>
        <p className="text-neutral-DEFAULT mt-2 text-center">
          { fileName ? "Configure and run the analysis on the left to see results here." : "Upload a dataset to get started."}
        </p>
      </div>
    );
  }

  const { config, plotData } = clusteringResult;

  return (
    <div className="space-y-6">
      <AnalysisSummaryCard config={config} fileName={fileName} />
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <AIInsightsDisplayCard 
            insights={ollamaInsights} 
            isLoading={isGeneratingInsights && !ollamaInsights} // Show loading if generating and no insights yet
            onViewDetails={() => setShowDetailedReportModal(true)}
        />
        <ClusterQuickStatsCard plotData={plotData} numClusters={config.numClusters} />
      </div>
      
      <Card className="dashboard-card">
        <Card.Header>
            <div className="flex items-center">
                <ChartBarIcon className="h-6 w-6 text-primary mr-2" />
                Cluster Visualization
            </div>
        </Card.Header>
        <Card.Body>
          <ClusterVisualization plotData={plotData} config={config} />
        </Card.Body>
      </Card>

      {ollamaInsights && (
        <Modal 
            isOpen={showDetailedReportModal} 
            onClose={() => setShowDetailedReportModal(false)}
            title="Full AI Insights Report (Llama3)"
        >
            <div 
                className="prose prose-sm max-w-none max-h-[70vh] overflow-y-auto" 
                dangerouslySetInnerHTML={{ __html: ollamaService.renderMarkdown(ollamaInsights.detailedReport) }} 
            />
        </Modal>
      )}
    </div>
  );
};

export default ResultsPanel;