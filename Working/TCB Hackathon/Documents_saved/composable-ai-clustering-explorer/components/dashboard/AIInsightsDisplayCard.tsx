import React from 'react';
import { FullInsights, SummarizedInsight } from '../../types';
import Card from '../common/Card';
import { Button } from '../common/Button';
import { Loader } from '../common/Loader';
import { SparklesIcon, MagnifyingGlassIcon } from '../common/Icons';

interface AIInsightsDisplayCardProps {
  insights: FullInsights | null;
  isLoading: boolean;
  onViewDetails: () => void;
}

const PlausibilityIndicator: React.FC<{ plausibility: SummarizedInsight['plausibility'] }> = ({ plausibility }) => {
  let colorClass = 'bg-gray-200 text-gray-700';
  if (plausibility === 'High') colorClass = 'bg-green-100 text-green-700';
  else if (plausibility === 'Medium') colorClass = 'bg-yellow-100 text-yellow-700';
  else if (plausibility === 'Low') colorClass = 'bg-red-100 text-red-700';

  return (
    <span className={`px-2 py-0.5 text-xs font-semibold rounded-full ${colorClass}`}>
      {plausibility} Plausibility
    </span>
  );
};

const AIInsightsDisplayCard: React.FC<AIInsightsDisplayCardProps> = ({ insights, isLoading, onViewDetails }) => {
  return (
    <Card className="dashboard-card min-h-[200px] flex flex-col"> {/* Added min-height and flex for consistent sizing */}
      <Card.Header>
        <div className="flex items-center justify-between">
            <div className="flex items-center">
                <SparklesIcon className="h-6 w-6 text-yellow-500 mr-2" />
                AI Insights (Llama3)
            </div>
            {insights && (
                 <Button onClick={onViewDetails} variant="outline" size="sm" className="flex items-center">
                    <MagnifyingGlassIcon className="h-4 w-4 mr-1.5"/> View Full Report
                </Button>
            )}
        </div>
      </Card.Header>
      <Card.Body className="flex-grow"> {/* Added flex-grow */}
        {isLoading && (
          <div className="flex flex-col items-center justify-center h-full">
            <Loader />
            <p className="mt-2 text-sm text-neutral-DEFAULT">Generating insights...</p>
          </div>
        )}
        {!isLoading && !insights && (
          <div className="flex flex-col items-center justify-center h-full text-center">
            <SparklesIcon className="h-12 w-12 text-gray-300 mb-2"/>
            <p className="text-sm text-neutral-DEFAULT">AI insights will appear here after analysis.</p>
          </div>
        )}
        {!isLoading && insights && (
          <div className="space-y-3">
            <div>
              <h4 className="text-sm font-semibold text-neutral-dark">Primary Theme:</h4>
              <p className="text-base text-primary-dark font-medium">{insights.summary.theme || "Not available"}</p>
            </div>
            <div>
              <PlausibilityIndicator plausibility={insights.summary.plausibility} />
            </div>
            <div>
              <h4 className="text-sm font-semibold text-neutral-dark mt-1">Key Factors / Characteristics (Mock):</h4>
              {insights.summary.keyFactors && insights.summary.keyFactors.length > 0 ? (
                <ul className="list-disc list-inside text-sm text-neutral-DEFAULT space-y-0.5 pl-1">
                  {insights.summary.keyFactors.map((factor, index) => (
                    <li key={index}>{factor}</li>
                  ))}
                </ul>
              ) : (
                <p className="text-sm text-neutral-DEFAULT">No specific factors highlighted by AI.</p>
              )}
            </div>
          </div>
        )}
      </Card.Body>
    </Card>
  );
};

export default AIInsightsDisplayCard;
