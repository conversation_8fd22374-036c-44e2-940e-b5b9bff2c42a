import React from 'react';

interface DashboardLayoutProps {
  leftPanel: React.ReactNode;
  rightPanel: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ leftPanel, rightPanel }) => {
  return (
    <div className="flex flex-col md:flex-row gap-6 lg:gap-8">
      <div className="w-full md:w-1/3 lg:w-1/4 flex-shrink-0">
        {/* Left Panel for Controls and Data Profile */}
        <aside className="sticky top-6 space-y-6">
         {leftPanel}
        </aside>
      </div>
      <div className="w-full md:w-2/3 lg:w-3/4">
        {/* Right Panel for Results */}
        {rightPanel}
      </div>
    </div>
  );
};

export default DashboardLayout;
