import React, { useState } from 'react';
import { ColumnStat } from '../../types';
import Card from '../common/Card';
import { DocumentDuplicateIcon, TableIcon, HashtagIcon, ChevronDownIcon, ChevronUpIcon } from '../common/Icons';

interface DataProfileCardProps {
  fileName: string;
  rowCount: number;
  columnCount: number;
  columnStats: ColumnStat[];
}

const DataProfileCard: React.FC<DataProfileCardProps> = ({ fileName, rowCount, columnCount, columnStats }) => {
  const [showStats, setShowStats] = useState(false);

  const numericFeatureCount = columnStats.filter(cs => cs.type === 'numeric').length;
  const stringFeatureCount = columnStats.filter(cs => cs.type === 'string').length;
  const missingValueTotal = columnStats.reduce((acc, curr) => acc + curr.missingCount, 0);

  return (
    <Card className="dashboard-card">
      <Card.Header>
        <div className="flex items-center">
          <DocumentDuplicateIcon className="h-6 w-6 text-primary mr-2" />
          Data Profile
        </div>
      </Card.Header>
      <Card.Body className="space-y-3">
        <div>
          <p className="text-xs text-neutral-DEFAULT">File Name</p>
          <p className="font-medium text-neutral-dark truncate" title={fileName}>{fileName}</p>
        </div>
        <div className="grid grid-cols-2 gap-3 text-sm">
          <div>
            <p className="text-xs text-neutral-DEFAULT">Rows</p>
            <p className="font-medium text-neutral-dark">{rowCount.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-xs text-neutral-DEFAULT">Columns</p>
            <p className="font-medium text-neutral-dark">{columnCount}</p>
          </div>
          <div>
            <p className="text-xs text-neutral-DEFAULT">Numeric Features</p>
            <p className="font-medium text-neutral-dark">{numericFeatureCount}</p>
          </div>
           <div>
            <p className="text-xs text-neutral-DEFAULT">Text Features</p>
            <p className="font-medium text-neutral-dark">{stringFeatureCount}</p>
          </div>
          <div className="col-span-2">
            <p className="text-xs text-neutral-DEFAULT">Total Missing Values</p>
            <p className="font-medium text-neutral-dark">{missingValueTotal.toLocaleString()}</p>
          </div>
        </div>
        
        <div>
            <button 
                onClick={() => setShowStats(!showStats)}
                className="text-xs text-primary hover:text-primary-dark flex items-center mt-2"
            >
                {showStats ? 'Hide' : 'Show'} Column Details
                {showStats ? <ChevronUpIcon className="h-3 w-3 ml-1"/> : <ChevronDownIcon className="h-3 w-3 ml-1"/>}
            </button>
        </div>

        {showStats && (
            <div className="mt-3 pt-3 border-t border-gray-200 max-h-48 overflow-y-auto text-xs">
                <h4 className="font-semibold text-neutral-dark mb-1">Column Overview:</h4>
                <ul className="space-y-1">
                {columnStats.map(col => (
                    <li key={col.name} className="p-1 bg-gray-50 rounded text-neutral-DEFAULT">
                        <span className="font-medium text-neutral-dark">{col.name}</span> ({col.type})
                        - Missing: {col.missingCount}
                        {col.type === 'numeric' && col.mean !== undefined && `, Avg: ${col.mean.toFixed(2)}`}
                    </li>
                ))}
                </ul>
            </div>
        )}

      </Card.Body>
    </Card>
  );
};

export default DataProfileCard;
