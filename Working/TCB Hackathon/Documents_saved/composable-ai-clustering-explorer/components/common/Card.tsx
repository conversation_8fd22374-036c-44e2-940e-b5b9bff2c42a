
import React from 'react';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'outline';
}

interface CardHeaderProps {
  children: React.ReactNode;
  className?: string;
}

interface CardBodyProps {
  children: React.ReactNode;
  className?: string;
}

interface CardFooterProps {
  children: React.ReactNode;
  className?: string;
}

const CardContext = React.createContext<{ variant: 'default' | 'outline' }>({ variant: 'default'});

const Card: React.FC<CardProps> & {
  Header: React.FC<CardHeaderProps>;
  Body: React.FC<CardBodyProps>;
  Footer: React.FC<CardFooterProps>;
} = ({ children, className = '', variant = 'default' }) => {
  const baseStyle = "bg-white rounded-lg";
  const variantStyle = variant === 'default' ? "shadow-md" : "border border-gray-200";
  
  return (
    <CardContext.Provider value={{ variant }}>
      <div className={`${baseStyle} ${variantStyle} ${className}`}>
        {children}
      </div>
    </CardContext.Provider>
  );
};

const Header: React.FC<CardHeaderProps> = ({ children, className = '' }) => {
  const { variant } = React.useContext(CardContext);
  const baseStyle = "px-4 py-3 sm:px-6";
  const borderStyle = variant === 'default' ? "border-b border-gray-200" : "";
  return (
    <div className={`${baseStyle} ${borderStyle} ${className}`}>
      {typeof children === 'string' ? <h3 className="text-lg font-medium leading-6 text-neutral-dark">{children}</h3> : children}
    </div>
  );
};

const Body: React.FC<CardBodyProps> = ({ children, className = '' }) => {
  return <div className={`px-4 py-5 sm:p-6 ${className}`}>{children}</div>;
};

const Footer: React.FC<CardFooterProps> = ({ children, className = '' }) => {
  const { variant } = React.useContext(CardContext);
  const baseStyle = "px-4 py-3 sm:px-6";
  const borderStyle = variant === 'default' ? "border-t border-gray-200 bg-gray-50" : "bg-gray-50";
  return <div className={`${baseStyle} ${borderStyle} ${className}`}>{children}</div>;
};

Card.Header = Header;
Card.Body = Body;
Card.Footer = Footer;

export default Card;
