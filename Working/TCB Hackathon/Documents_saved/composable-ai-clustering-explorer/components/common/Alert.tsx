
import React from 'react';
import { InformationCircleIcon, CheckCircleIcon, ExclamationTriangleIcon, XCircleIcon } from './Icons'; // Assuming these exist

interface AlertProps {
  type?: 'info' | 'success' | 'warning' | 'error';
  title?: string;
  message: string;
  className?: string;
}

const Alert: React.FC<AlertProps> = ({ type = 'info', title, message, className = '' }) => {
  const baseStyles = "p-4 rounded-md";
  
  const typeStyles = {
    info: {
      bg: 'bg-blue-50',
      iconColor: 'text-blue-400',
      titleColor: 'text-blue-800',
      messageColor: 'text-blue-700',
      Icon: InformationCircleIcon,
    },
    success: {
      bg: 'bg-green-50',
      iconColor: 'text-green-400',
      titleColor: 'text-green-800',
      messageColor: 'text-green-700',
      Icon: CheckCircleIcon,
    },
    warning: {
      bg: 'bg-yellow-50',
      iconColor: 'text-yellow-400',
      titleColor: 'text-yellow-800',
      messageColor: 'text-yellow-700',
      Icon: ExclamationTriangleIcon,
    },
    error: {
      bg: 'bg-red-50',
      iconColor: 'text-red-400',
      titleColor: 'text-red-800',
      messageColor: 'text-red-700',
      Icon: XCircleIcon,
    },
  };

  const currentTypeStyle = typeStyles[type];
  const IconComponent = currentTypeStyle.Icon;

  return (
    <div className={`${baseStyles} ${currentTypeStyle.bg} ${className}`} role="alert">
      <div className="flex">
        <div className="flex-shrink-0">
          <IconComponent className={`h-5 w-5 ${currentTypeStyle.iconColor}`} aria-hidden="true" />
        </div>
        <div className="ml-3">
          {title && <h3 className={`text-sm font-medium ${currentTypeStyle.titleColor}`}>{title}</h3>}
          <div className={`text-sm ${currentTypeStyle.messageColor} ${title ? 'mt-1' : ''}`}>
            {message}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Alert;
