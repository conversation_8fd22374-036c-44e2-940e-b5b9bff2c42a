import React, { useState, useCallback } from 'react';
import { ParsedData, RawDataRow } from '../types';
import { Button } from './common/Button';
import { UploadIcon, DocumentArrowUpIcon } from './common/Icons'; // Added DocumentArrowUpIcon
import Alert from './common/Alert';
import Card from './common/Card';

interface FileUploadProps {
  onFileUpload: (fileName: string, data: ParsedData) => void;
  isLoading: boolean;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFileUpload, isLoading }) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [fileError, setFileError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);


  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFileError(null);
    if (event.target.files && event.target.files[0]) {
      processFile(event.target.files[0]);
    }
    event.target.value = ''; // Reset file input to allow re-uploading the same file
  };
  
  const processFile = (file: File) => {
    if (file.type !== 'text/csv' && !file.name.endsWith('.csv')) {
      setFileError('Invalid file type. Please upload a CSV file.');
      setSelectedFile(null);
      return;
    }
    if (file.size > 10 * 1024 * 1024) { // 10MB limit
      setFileError('File is too large. Maximum size is 10MB.');
      setSelectedFile(null);
      return;
    }
    setSelectedFile(file);
  };

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setDragOver(false);
    setFileError(null);
    if (event.dataTransfer.files && event.dataTransfer.files[0]) {
      processFile(event.dataTransfer.files[0]);
    }
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    event.stopPropagation();
    setDragOver(false);
  }, []);


  const handleUpload = useCallback(() => {
    if (!selectedFile) {
      setFileError('Please select a file to upload.');
      return;
    }
    setFileError(null);

    window.Papa.parse(selectedFile, {
      header: true,
      skipEmptyLines: true,
      dynamicTyping: true, 
      complete: (results: any) => { 
        if (results.errors && results.errors.length > 0) {
           setFileError(`Error parsing CSV: ${results.errors.map((e:any) => e.message).join(', ')}`);
           onFileUpload(selectedFile.name, { headers: [], rows: [], errors: results.errors });
           return;
        }
        const headers = results.meta.fields || [];
        const rows = results.data as RawDataRow[];
        onFileUpload(selectedFile.name, { headers, rows, errors: [] });
      },
      error: (error: Error) => {
        setFileError(`Failed to parse CSV: ${error.message}`);
        onFileUpload(selectedFile.name, { headers: [], rows: [], errors: [{type: 'ParseError', code: 'Unknown', message: error.message, row: -1}]});
      }
    });
  }, [selectedFile, onFileUpload]);

  return (
    <Card className="dashboard-card">
      <Card.Header>
        <div className="flex items-center">
          <DocumentArrowUpIcon className="h-6 w-6 text-primary mr-2" />
          Upload Dataset
        </div>
      </Card.Header>
      <Card.Body className="space-y-4">
        <div 
          className={`flex justify-center px-6 pt-5 pb-6 border-2 ${dragOver ? 'border-primary' : 'border-gray-300'} border-dashed rounded-md transition-colors`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <div className="space-y-1 text-center">
            <UploadIcon className="mx-auto h-10 w-10 text-gray-400" />
            <div className="flex text-sm text-gray-600">
              <label
                htmlFor="file-upload-input"
                className="relative cursor-pointer bg-white rounded-md font-medium text-primary hover:text-primary-dark focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary px-1"
              >
                <span>Choose a file</span>
                <input id="file-upload-input" name="file-upload" type="file" className="sr-only" onChange={handleFileChange} accept=".csv,text/csv" />
              </label>
              <p className="pl-1">or drag and drop CSV</p>
            </div>
            <p className="text-xs text-gray-500">Max 10MB</p>
          </div>
        </div>
        {selectedFile && <p className="text-sm text-green-600 bg-green-50 p-2 rounded-md">Selected: {selectedFile.name}</p>}
        
        {fileError && <Alert type="error" message={fileError} />}
        
        <Button 
          onClick={handleUpload} 
          disabled={!selectedFile || isLoading}
          isLoading={isLoading}
          className="w-full"
          size="md"
        >
          {isLoading ? 'Processing...' : 'Load Data'}
        </Button>
      </Card.Body>
    </Card>
  );
};

export default FileUpload;