export enum AppStep {
  UPLOAD = 'UPLOAD',
  OVERVIEW = 'OVERVIEW',
  CONFIGURE = 'CONFIGURE',
  RESULTS_INSIGHTS = 'RESULTS_INSIGHTS',
}

export type RawDataRow = Record<string, string | number>;

export interface ParsedData {
  headers: string[];
  rows: RawDataRow[];
  errors: { type: string; code: string; message: string; row: number }[];
}

export interface ColumnStat {
  name: string;
  type: 'numeric' | 'string' | 'boolean' | 'unknown';
  nonNullCount: number;
  missingCount: number;
  uniqueValues?: number; // For string/categorical
  min?: number;    // For numeric
  max?: number;    // For numeric
  mean?: number;   // For numeric
}

export interface ClusteringConfig {
  scaling: string;
  dimensionalityReduction: string;
  clusteringAlgorithm: string;
  numClusters: number;
}

export interface PlotDataPoint {
  originalRowIndex: number; 
  x: number;
  y: number;
  cluster: number;
  tooltipPayload?: RawDataRow; 
}

export interface ClusteringResult {
  plotData: PlotDataPoint[];
  config: ClusteringConfig;
  columnStats: ColumnStat[];
  parsedData: ParsedData; 
  fileName: string | null;
}

export interface SummarizedInsight {
  theme: string; // e.g., "High Engagement Users"
  plausibility: 'High' | 'Medium' | 'Low' | 'N/A'; // Qualitative assessment
  keyFactors: string[]; // e.g., ["High purchase_frequency", "Low churn_score"]
}

export interface FullInsights {
  summary: SummarizedInsight;
  detailedReport: string; // The full markdown from Ollama
}


declare global {
  interface Window {
    Papa: any;
  }
}