
import { ParsedData, ColumnStat, ClusteringConfig, PlotDataPoint, RawDataRow } from '../types';

export const generateBasicStats = (data: ParsedData): ColumnStat[] => {
  if (!data || !data.headers || data.rows.length === 0) {
    return [];
  }

  return data.headers.map(header => {
    const values = data.rows.map(row => row[header]).filter(value => value !== null && value !== undefined && value !== '');
    
    let type: ColumnStat['type'] = 'unknown';
    let numericValues: number[] = [];

    if (values.length > 0) {
      const allNumeric = values.every(val => typeof val === 'number' || (typeof val === 'string' && !isNaN(parseFloat(val))));
      if (allNumeric) {
        type = 'numeric';
        numericValues = values.map(val => typeof val === 'number' ? val : parseFloat(val as string));
      } else {
        const allBooleanStrings = values.every(val => typeof val === 'string' && (val.toLowerCase() === 'true' || val.toLowerCase() === 'false'));
        if (allBooleanStrings) type = 'boolean';
        else type = 'string';
      }
    } else if (data.rows.length > 0) { // Some rows exist, but all values for this column are missing
        // Try to infer from header or first non-empty row globally if possible (more complex)
        // For now, assume string if no values present
        type = 'string';
    }


    const stat: ColumnStat = {
      name: header,
      type: type,
      nonNullCount: values.length,
      missingCount: data.rows.length - values.length,
    };

    if (type === 'numeric' && numericValues.length > 0) {
      stat.min = Math.min(...numericValues);
      stat.max = Math.max(...numericValues);
      stat.mean = numericValues.reduce((acc, val) => acc + val, 0) / numericValues.length;
    }
    
    if (type === 'string' || type === 'boolean') {
        stat.uniqueValues = new Set(values.map(v => String(v))).size;
    }

    return stat;
  });
};

export const generateMockPlotData = (
  parsedData: ParsedData,
  config: ClusteringConfig,
  columnStats: ColumnStat[]
): PlotDataPoint[] => {
  if (!parsedData || parsedData.rows.length === 0) return [];

  const numericCols = columnStats.filter(cs => cs.type === 'numeric');
  
  let useRandomData = true;
  let xCol: string | null = null;
  let yCol: string | null = null;

  if (config.dimensionalityReduction === 'None' && numericCols.length >= 2) {
    xCol = numericCols[0].name;
    yCol = numericCols[1].name;
    useRandomData = false;
  } else if (config.dimensionalityReduction === 'None' && numericCols.length === 1) {
    xCol = numericCols[0].name; // Use one numeric col for X, random for Y
    useRandomData = true; // For Y
  } else { // PCA/UMAP (mocked) or no/few numeric cols
    useRandomData = true;
  }

  return parsedData.rows.map((row, index): PlotDataPoint => {
    let x: number;
    let y: number;

    if (useRandomData) {
      x = xCol ? (row[xCol] as number) : Math.random() * 100; // If xCol exists, use it, else random
      y = Math.random() * 100; // Y is always random if useRandomData is true
    } else if (xCol && yCol) { // Use actual data from two numeric columns
      x = row[xCol] as number;
      y = row[yCol] as number;
      // Basic normalization to 0-100 range for consistent plotting if values are large/small
      const xStat = columnStats.find(cs => cs.name === xCol);
      const yStat = columnStats.find(cs => cs.name === yCol);
      if (xStat && xStat.min !== undefined && xStat.max !== undefined && xStat.max !== xStat.min) {
        x = ((x - xStat.min) / (xStat.max - xStat.min)) * 100;
      } else if (xStat && xStat.min !== undefined && xStat.max !== undefined && xStat.max === xStat.min){
        x = 50; // If all values are same, plot in middle
      }
      if (yStat && yStat.min !== undefined && yStat.max !== undefined && yStat.max !== yStat.min) {
        y = ((y - yStat.min) / (yStat.max - yStat.min)) * 100;
      } else if (yStat && yStat.min !== undefined && yStat.max !== undefined && yStat.max === yStat.min){
        y = 50;
      }
    } else { // Fallback, should not happen with current logic but as a safeguard
        x = Math.random() * 100;
        y = Math.random() * 100;
    }


    return {
      originalRowIndex: index,
      x: isNaN(x) ? Math.random()*100 : x, // Handle potential NaN from data
      y: isNaN(y) ? Math.random()*100 : y,
      cluster: Math.floor(Math.random() * config.numClusters), // Random cluster assignment
      tooltipPayload: row, // Include original row data for tooltip
    };
  });
};
