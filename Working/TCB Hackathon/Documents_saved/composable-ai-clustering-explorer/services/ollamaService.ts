import { OLLAMA_API_URL, DEFAULT_OLLAMA_MODEL } from '../constants';

interface OllamaResponse {
  model: string;
  created_at: string;
  response: string;
  done: boolean;
  context?: number[];
  total_duration?: number;
  load_duration?: number;
  prompt_eval_count?: number;
  prompt_eval_duration?: number;
  eval_count?: number;
  eval_duration?: number;
}

const generateText = async (prompt: string, model: string = DEFAULT_OLLAMA_MODEL): Promise<string> => {
  try {
    const response = await fetch(OLLAMA_API_URL, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: model,
        prompt: prompt,
        stream: false, 
      }),
    });

    if (!response.ok) {
      const errorBody = await response.text();
      throw new Error(`Ollama API request failed with status ${response.status}: ${errorBody}`);
    }

    const data: OllamaResponse = await response.json();
    
    if (!data.done || typeof data.response !== 'string') {
        console.error("Unexpected response format from Ollama API:", data);
        throw new Error("Received an unexpected response format from Ollama. Expected a 'response' string and 'done: true'.");
    }
    return data.response;

  } catch (error: any) {
    console.error("Error calling Ollama API:", error);
    if (error.message && error.message.includes('Failed to fetch')) {
        throw new Error(`Network error: Could not connect to Ollama API at ${OLLAMA_API_URL}. Ensure Ollama is running and accessible.`);
    }
    throw new Error(`An error occurred while communicating with Ollama: ${error.message || 'Unknown error'}`);
  }
};

// Basic Markdown to HTML (very simplified, for use in Modal)
// For more robust parsing, a library like 'marked' or 'showdown' would be better.
const renderMarkdown = (text: string): string => {
    if (!text) return '';
    let html = text;
    
    // Headers (e.g., ## Header 2)
    html = html.replace(/^### (.*$)/gim, '<h3>$1</h3>');
    html = html.replace(/^## (.*$)/gim, '<h2>$1</h2>');
    html = html.replace(/^# (.*$)/gim, '<h1>$1</h1>');

    // Bold: **text** or __text__
    html = html.replace(/\*\*(.*?)\*\*|__(.*?)__/g, '<strong>$1$2</strong>');
    // Italics: *text* or _text_
    html = html.replace(/\*(.*?)\*|_(.*?)_/g, '<em>$1$2</em>');
    
    // Unordered lists: lines starting with * or - or +
    // This needs to handle multi-line list items better and wrap in <ul>
    html = html.replace(/^\s*[\*\-\+]\s+(.*)/gm, '<li>$1</li>');
    // Wrap consecutive <li> items in <ul>
    html = html.replace(/(<li>.*?<\/li>\s*)+/gs, (match) => `<ul>${match.trim()}</ul>`);


    // Ordered lists: lines starting with 1. 2. etc.
    // This needs to handle multi-line list items better and wrap in <ol>
    html = html.replace(/^\s*\d+\.\s+(.*)/gm, '<li>$1</li>');
    // Wrap consecutive <li> items in <ol> (if preceded by digit, simplified)
    // This is tricky without more state. For now, it might incorrectly wrap if mixed with <ul>.
    // A proper parser is much better for this.
    // Let's assume for now that Ollama produces blocks of one list type.

    // Paragraphs (convert newlines to <p> tags, but not inside lists or around block elements)
    // This is a very basic approach.
    const blocks = html.split(/\n{2,}/); // Split by double newlines
    html = blocks.map(block => {
        if (block.startsWith('<ul>') || block.startsWith('<ol>') || block.startsWith('<li>') || block.startsWith('<h')) {
            return block; // Don't wrap list blocks or headers in <p>
        }
        return block.trim() ? `<p>${block.replace(/\n/g, '<br/>')}</p>` : ''; // Convert single newlines in paragraphs to <br>
    }).join('');

    // Final cleanup for list wrappers if they are inside <p> due to simple split
    html = html.replace(/<p><ul>/g, '<ul>').replace(/<\/ul><\/p>/g, '</ul>');
    html = html.replace(/<p><ol>/g, '<ol>').replace(/<\/ol><\/p>/g, '</ol>');


    return html;
};


export const ollamaService = {
  generateText,
  renderMarkdown
};
