
import React, { useState, useCallback, useEffect } from 'react';
import { ParsedData, ColumnStat, ClusteringConfig, ClusteringResult, FullInsights } from './types';
import { APP_TITLE, DEFAULT_NUM_CLUSTERS, SCALING_OPTIONS, DIMENSIONALITY_REDUCTION_OPTIONS, CLUSTERING_ALGORITHMS, OLLAMA_API_URL, DEFAULT_OLLAMA_MODEL } from './constants';
import { generateBasicStats, generateMockPlotData } from './utils/dataProcessor';
import { ollamaService } from './services/ollamaService';

import DashboardLayout from './components/dashboard/DashboardLayout';
import DataProfileCard from './components/dashboard/DataProfileCard';
import AnalysisConfigControls from './components/dashboard/AnalysisConfigControls';
import ResultsPanel from './components/dashboard/ResultsPanel';
import FileUpload from './components/FileUpload';
import Alert from './components/common/Alert';

const App: React.FC = () => {
  const [fileName, setFileName] = useState<string | null>(null);
  const [parsedData, setParsedData] = useState<ParsedData | null>(null);
  const [columnStats, setColumnStats] = useState<ColumnStat[] | null>(null);
  
  const [clusteringConfig, setClusteringConfig] = useState<ClusteringConfig>({
    scaling: SCALING_OPTIONS[0],
    dimensionalityReduction: DIMENSIONALITY_REDUCTION_OPTIONS[0],
    clusteringAlgorithm: CLUSTERING_ALGORITHMS[0],
    numClusters: DEFAULT_NUM_CLUSTERS,
  });
  
  const [clusteringResult, setClusteringResult] = useState<ClusteringResult | null>(null);
  const [ollamaInsights, setOllamaInsights] = useState<FullInsights | null>(null);
  
  const [appError, setAppError] = useState<string | null>(null);
  const [isLoadingData, setIsLoadingData] = useState<boolean>(false);
  const [isAnalyzing, setIsAnalyzing] = useState<boolean>(false);
  const [isGeneratingInsights, setIsGeneratingInsights] = useState<boolean>(false);

  const isFileUploaded = !!parsedData;
  const isAnalysisComplete = !!clusteringResult;

  const handleFileUpload = useCallback((name: string, data: ParsedData) => {
    setIsLoadingData(true);
    setAppError(null);
    setFileName(name);
    setParsedData(data);
    setClusteringResult(null); // Reset results on new file upload
    setOllamaInsights(null);

    if (data.rows.length > 0) {
      const stats = generateBasicStats(data);
      setColumnStats(stats);
    } else {
      setAppError("Uploaded CSV is empty or could not be parsed correctly.");
      setParsedData(null); // Clear invalid data
      setFileName(null);
    }
    setIsLoadingData(false);
  }, []);

  const handleConfigChange = useCallback((newConfig: Partial<ClusteringConfig>) => {
    setClusteringConfig(prev => ({ ...prev, ...newConfig }));
    setClusteringResult(null); // Config change invalidates current results
    setOllamaInsights(null);
  }, []);

  const generateInsightsWithOllama = useCallback(async (currentClusteringResult: ClusteringResult) => {
    setIsGeneratingInsights(true);
    setOllamaInsights(null); // Clear previous insights
    setAppError(null);

    const { config, plotData, parsedData: resultParsedData, columnStats: resultColumnStats, fileName: resultFileName } = currentClusteringResult;

    const clusterSummaries = Array.from({ length: config.numClusters }, (_, i) => {
      const pointsInCluster = plotData.filter(p => p.cluster === i);
      return `Cluster ${i}: ${pointsInCluster.length} data points.`;
    }).join('\n');
    
    const numericColumns = resultColumnStats.filter(cs => cs.type === 'numeric').map(cs => cs.name);
    const stringColumns = resultColumnStats.filter(cs => cs.type === 'string').map(cs => cs.name);

    const summaryPrompt = `
You are an AI data analysis assistant. Based on the following MOCK clustering results, provide a concise summary and then a detailed report.

Dataset & Analysis Overview (Mocked):
- File Name: ${resultFileName || 'N/A'}
- Total Rows: ${resultParsedData.rows.length}
- Total Columns: ${resultParsedData.headers.length}
- Numeric Columns: ${numericColumns.join(', ') || 'None'}
- Text/Categorical Columns: ${stringColumns.join(', ') || 'None'}
- Scaling: ${config.scaling}, Dimensionality Reduction: ${config.dimensionalityReduction}, Algorithm: ${config.clusteringAlgorithm}, Clusters: ${config.numClusters}
- Cluster Sizes: ${clusterSummaries}

Response Format:
Provide your response in two parts, separated by "---DETAILED REPORT---".

Part 1: Summary (JSON object)
Create a JSON object with the following structure:
{
  "theme": "A concise, overarching theme or primary insight discovered from the clusters (e.g., 'Possible High-Value Customer Segment Identified').",
  "plausibility": "A qualitative assessment of plausibility ('High', 'Medium', or 'Low').",
  "keyFactors": ["A list of 2-3 key characteristics or mock data features that might define this primary insight or the most significant cluster (e.g., 'High mock_feature_A values', 'Low mock_feature_B values')."]
}

---DETAILED REPORT---

Part 2: Detailed Report (Markdown)
Generate a markdown report covering:
1.  Hypothetical Cluster Profiles: For each cluster, suggest a plausible, distinct characteristic or theme.
2.  Potential Business Use Cases: Describe 2-3 general business insights or applications.
3.  Example Names for Clusters: Suggest a short, descriptive name for each cluster.
Acknowledge the mock nature of inputs implicitly by providing plausible interpretations.
`;

    try {
      const rawResponse = await ollamaService.generateText(summaryPrompt, DEFAULT_OLLAMA_MODEL);
      const parts = rawResponse.split("---DETAILED REPORT---");
      
      if (parts.length < 2) {
        throw new Error("Ollama response did not contain the '---DETAILED REPORT---' separator.");
      }

      let summaryJson: any;
      try {
        // Clean the JSON part: remove potential markdown fences and trim
        let jsonStr = parts[0].trim();
        const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
        const match = jsonStr.match(fenceRegex);
        if (match && match[2]) {
          jsonStr = match[2].trim();
        }
        summaryJson = JSON.parse(jsonStr);
      } catch (e: any) {
        console.error("Failed to parse summary JSON from Ollama:", e, "Raw JSON part:", parts[0]);
        throw new Error(`Failed to parse summary JSON from Ollama: ${e.message}. Ensure the Llama3 model provides the summary in valid JSON format as requested.`);
      }
      
      const detailedReport = parts[1].trim();

      setOllamaInsights({
        summary: {
          theme: summaryJson.theme || "N/A",
          plausibility: summaryJson.plausibility || "N/A",
          keyFactors: summaryJson.keyFactors || [],
        },
        detailedReport: detailedReport,
      });

    } catch (e: any) {
      console.error("Error generating insights with Ollama:", e);
      setAppError(`Insights generation failed: ${e.message}. Check Ollama status and model availability.`);
      setOllamaInsights(null); // Clear on error
    } finally {
      setIsGeneratingInsights(false);
    }
  }, []);


  const handleRunAnalysis = useCallback(async () => {
    if (!parsedData || !columnStats || !fileName) {
      setAppError("Data not available for analysis.");
      return;
    }
    setIsAnalyzing(true);
    setAppError(null);
    setClusteringResult(null); // Clear previous results
    setOllamaInsights(null);

    // Simulate analysis delay
    setTimeout(() => {
      try {
        const plotData = generateMockPlotData(parsedData, clusteringConfig, columnStats);
        const newClusteringResult = {
          plotData,
          config: clusteringConfig,
          columnStats,
          parsedData,
          fileName,
        };
        setClusteringResult(newClusteringResult);
        // Trigger insights generation after setting clustering result
        generateInsightsWithOllama(newClusteringResult);
      } catch (e: any) {
        setAppError(`Error during analysis: ${e.message}`);
      } finally {
        setIsAnalyzing(false);
      }
    }, 500);
  }, [parsedData, clusteringConfig, columnStats, fileName, generateInsightsWithOllama]);

  const handleReset = useCallback(() => {
    setFileName(null);
    setParsedData(null);
    setColumnStats(null);
    setClusteringConfig({
      scaling: SCALING_OPTIONS[0],
      dimensionalityReduction: DIMENSIONALITY_REDUCTION_OPTIONS[0],
      clusteringAlgorithm: CLUSTERING_ALGORITHMS[0],
      numClusters: DEFAULT_NUM_CLUSTERS,
    });
    setClusteringResult(null);
    setOllamaInsights(null);
    setAppError(null);
    setIsLoadingData(false);
    setIsAnalyzing(false);
    setIsGeneratingInsights(false);
  }, []);

  const leftPanelContent = (
    <div className="space-y-6 p-1">
      {!isFileUploaded && !isLoadingData && (
        <FileUpload onFileUpload={handleFileUpload} isLoading={isLoadingData} />
      )}
      {isLoadingData && <p className="text-center py-4">Loading data...</p>}
      {isFileUploaded && parsedData && columnStats && (
        <>
          <DataProfileCard 
            fileName={fileName!} 
            rowCount={parsedData.rows.length} 
            columnCount={parsedData.headers.length}
            columnStats={columnStats}
          />
          <AnalysisConfigControls
            config={clusteringConfig}
            onConfigChange={handleConfigChange}
            onRunAnalysis={handleRunAnalysis}
            onReset={handleReset}
            isAnalyzing={isAnalyzing || isGeneratingInsights}
            isFileUploaded={isFileUploaded}
          />
        </>
      )}
    </div>
  );

  const rightPanelContent = (
    <ResultsPanel
      isAnalysisComplete={isAnalysisComplete}
      isAnalyzing={isAnalyzing}
      isGeneratingInsights={isGeneratingInsights}
      clusteringResult={clusteringResult}
      ollamaInsights={ollamaInsights}
      fileName={fileName}
    />
  );

  return (
    <>
      <header className="bg-neutral-dark shadow-md">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl">
          <div className="flex items-center justify-between h-16">
            <h1 className="text-xl sm:text-2xl font-bold text-white">{APP_TITLE}</h1>
             {isFileUploaded && (
                <button 
                    onClick={handleReset}
                    className="text-sm text-neutral-light hover:text-white transition-colors"
                    title="Reset and upload new data"
                >
                    Start Over
                </button>
            )}
          </div>
        </div>
      </header>
      <main className="container mx-auto p-4 sm:p-6 lg:p-8 max-w-7xl mt-4">
        {appError && (
          <div className="mb-4">
            <Alert type="error" title="An Error Occurred" message={appError} />
          </div>
        )}
        <DashboardLayout
          leftPanel={leftPanelContent}
          rightPanel={rightPanelContent}
        />
      </main>
      <footer className="mt-12 text-center text-neutral-DEFAULT text-xs sm:text-sm py-6 border-t border-gray-200">
        <p>&copy; {new Date().getFullYear()} {APP_TITLE}. For demonstration purposes.</p>
        <p>Ollama API expected at: <code className="text-xs">{OLLAMA_API_URL}</code> using model: <code className="text-xs">{DEFAULT_OLLAMA_MODEL}</code></p>
      </footer>
    </>
  );
};

export default App;
