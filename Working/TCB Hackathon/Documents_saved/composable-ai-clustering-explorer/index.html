<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-T-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>AI Clustering Dashboard</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.3.2/papaparse.min.js"></script>
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            primary: {
              DEFAULT: '#3b82f6', // blue-500
              light: '#60a5fa', // blue-400
              dark: '#2563eb', // blue-600
            },
            secondary: '#10b981', // emerald-500
            neutral: {
              light: '#f3f4f6', // gray-100
              DEFAULT: '#6b7280', // gray-500
              dark: '#1f2937',  // gray-800
            }
          },
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
          },
        }
      }
    }
  </script>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
<script type="importmap">
{
  "imports": {
    "react-dom/": "https://esm.sh/react-dom@^18.2.0/",
    "react/": "https://esm.sh/react@^18.2.0/",
    "react": "https://esm.sh/react@^18.2.0",
    "recharts": "https://esm.sh/recharts@2.12.7",
    "@google/genai": "https://esm.sh/@google/genai@^1.2.0",
    "react-dom": "https://esm.sh/react-dom@^18.2.0"
  }
}
</script>
  <style>
    /* Basic scrollbar styling for a more modern look */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #c4c4c4;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    .dashboard-card {
      @apply bg-white shadow-lg rounded-lg overflow-hidden;
    }
  </style>
</head>
<body class="bg-neutral-light font-sans antialiased">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html><link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
