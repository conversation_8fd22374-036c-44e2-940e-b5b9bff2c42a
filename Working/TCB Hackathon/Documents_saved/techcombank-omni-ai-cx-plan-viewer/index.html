
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Techcombank Omni-AI CX Plan</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    /* Custom scrollbar for a cleaner look */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #c4c4c4;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #a1a1a1;
    }
    /* Basic body styling */
    body {
      font-family: 'Inter', sans-serif; /* A common clean sans-serif font */
    }
    /* Style for Recharts text */
    .recharts-text {
      fill: #4A5568; /* Equivalent to text-gray-700 */
      font-size: 0.875rem; /* text-sm */
    }
    .recharts-legend-item-text {
       fill: #4A5568 !important; /* text-gray-700 */
       font-size: 0.875rem !important; /* text-sm */
    }
  </style>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <script>
    tailwind.config = {
      theme: {
        extend: {
          colors: {
            'techcombank-red': '#E60000',
            'primary-dark': '#333333',
            'primary-light': '#F5F5F5',
            'secondary-gray': '#6B7280', // text-gray-500
            'border-gray': '#D1D5DB',   // border-gray-300
          },
          fontFamily: {
            sans: ['Inter', 'sans-serif'],
          },
        }
      }
    }
  </script>
<script type="importmap">
{
  "imports": {
    "react": "https://esm.sh/react@^19.1.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "recharts": "https://esm.sh/recharts@^2.15.3",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "vite": "https://esm.sh/vite@^6.3.5",
    "@vitejs/plugin-react": "https://esm.sh/@vitejs/plugin-react@^4.5.0"
  }
}
</script>
</head>
<body class="bg-primary-light">
  <div id="root"></div>
</body>
</html>
    <link rel="stylesheet" href="index.css">
<script src="index.tsx" type="module"></script>
