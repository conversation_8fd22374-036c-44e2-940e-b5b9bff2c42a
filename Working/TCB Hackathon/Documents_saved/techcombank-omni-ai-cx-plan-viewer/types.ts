
export enum SectionContentType {
  PARAGRAPH = 'paragraph',
  LIST = 'list', // bullet points
  ORDERED_LIST = 'ordered_list', // numbered list
  CODE = 'code', // { code: string, language?: string, caption?: string }
  TEAM_ROLES = 'team_roles', // { role: string, description: string }[]
  COMPONENTS_TOOLS = 'components_tools', // { category?: string, tools: string[] }[] or string[]
  DELIVERABLES = 'deliverables', // string[]
  OUTPUT_DESCRIPTION = 'output_description', // string
  CHART = 'chart', // for budget, timeline
  RISKS = 'risks', // { risk: string, mitigation: string }[]
  OUTCOMES = 'outcomes', // { metric: string, target: string }[]
  KEY_VALUE_LIST = 'key_value_list', // { key: string, value: string }[]
  SUB_HEADING_PARAGRAPH = 'sub_heading_paragraph', // { heading: string, text: string }
  IMAGE_PLACEHOLDER = 'image_placeholder', // { alt: string, width?: number, height?: number, caption?: string }
}

export interface CodeContent {
  code: string;
  language?: string;
  caption?: string;
}

export interface TeamRole {
  role: string;
  description: string;
}

export interface ComponentToolCategory {
  category: string;
  tools: string[];
}

export interface RiskItem {
  risk: string;
  mitigation: string;
}

export interface OutcomeItem {
  metric: string;
  target: string;
}

export interface KeyValueItem {
  key: string;
  value: string;
}

export interface SubHeadingParagraph {
  heading: string;
  text: string;
}

export interface ImagePlaceholder {
  alt: string;
  width?: number;
  height?: number;
  caption?: string;
}

export interface SubSectionContentBlock {
  type: SectionContentType;
  data: any; 
  title?: string; 
}

export interface SubSection {
  title: string;
  id?: string; // Optional id for deep linking within a section
  contentBlocks: SubSectionContentBlock[]; 
}

export interface PlanSection {
  id: string; 
  title: string;
  purpose?: string;
  subSections: SubSection[];
}

export interface TimelineEntry {
  months: string;
  tasks: string[];
}

export interface BudgetCategory {
  name: string;
  costRange: string; // e.g. "10-15"
  unit: string; // e.g. "billion VND"
  details?: string;
}

export interface BudgetItemForChart {
    name: string;
    avgCost: number; 
    costLabel: string;
}

// SVG Icon Prop Type
export interface IconProps {
  className?: string;
}
    