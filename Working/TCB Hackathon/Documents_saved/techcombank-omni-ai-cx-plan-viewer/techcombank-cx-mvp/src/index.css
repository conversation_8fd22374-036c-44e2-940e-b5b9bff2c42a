@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom scrollbar for a cleaner look */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #c4c4c4;
  border-radius: 4px;
}
::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* Basic body styling from original index.html */
body {
  font-family: 'Inter', sans-serif;
  /* bg-primary-light is already applied via class in index.html,
     but good to have base font here. */
}

/* Style for Recharts text from original index.html */
.recharts-text {
  fill: #4A5568; /* Equivalent to text-gray-700 */
  font-size: 0.875rem; /* text-sm */
}
.recharts-legend-item-text {
   fill: #4A5568 !important; /* text-gray-700 */
   font-size: 0.875rem !important; /* text-sm */
}
