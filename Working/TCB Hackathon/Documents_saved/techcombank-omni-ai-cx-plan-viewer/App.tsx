
import React, { useState, useEffect, useRef } from 'react';
import Navbar from './components/Navbar';
import SectionDisplay from './components/SectionDisplay';
import planData from './constants';
import { PlanSection } from './types';
import { AcademicCapIcon } from './components/IconComponents';

const App: React.FC = () => {
  const [currentSection, setCurrentSection] = useState<string>(planData[0]?.id || '');
  const sectionRefs = useRef<Record<string, HTMLElement | null>>({});

  useEffect(() => {
    planData.forEach(section => {
      sectionRefs.current[section.id] = document.getElementById(section.id);
    });

    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -60% 0px', // Adjust to trigger when section is more centered
      threshold: 0.1 // Small part of section visible
    };

    const observerCallback: IntersectionObserverCallback = (entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          setCurrentSection(entry.target.id);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);
    Object.values(sectionRefs.current).forEach(ref => {
      if (ref) observer.observe(ref);
    });

    // Initial check for hash
    const hash = window.location.hash.substring(1);
    if (hash && sectionRefs.current[hash]) {
       handleNavigate(hash);
    }


    return () => {
      Object.values(sectionRefs.current).forEach(ref => {
        if (ref) observer.unobserve(ref);
      });
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleNavigate = (id: string) => {
    const element = sectionRefs.current[id];
    if (element) {
      const offset = 80; // Approximate height of sticky navbar + some padding
      const bodyRect = document.body.getBoundingClientRect().top;
      const elementRect = element.getBoundingClientRect().top;
      const elementPosition = elementRect - bodyRect;
      const offsetPosition = elementPosition - offset;
      
      window.scrollTo({
        top: offsetPosition,
        behavior: 'smooth'
      });
      // Update URL hash without adding to history stack too much,
      // but allow back/forward to work for sections.
      // Using history.pushState allows for cleaner URLs than just hash.
      // However, the prompt says "cannot update the URL path, except for the hash string".
      // So, we must stick to window.location.hash.
      window.location.hash = id;
      setCurrentSection(id); // Ensure immediate feedback for active nav item
    }
  };
  
  const navSections = planData.map(s => ({ id: s.id, title: s.title }));

  return (
    <div className="min-h-screen bg-primary-light text-primary-dark">
      <Navbar sections={navSections} currentSection={currentSection} onNavigate={handleNavigate} />
      
      <header className="bg-techcombank-red text-white py-10 px-4 sm:px-6 lg:px-8 text-center shadow-lg">
        <div className="max-w-4xl mx-auto">
          <AcademicCapIcon className="w-16 h-16 mx-auto mb-4 text-white opacity-80"/>
          <h1 className="text-4xl font-bold tracking-tight sm:text-5xl">
            Techcombank Omni-AI CX Technology Implementation Plan
          </h1>
          <p className="mt-6 text-xl leading-8 text-red-100">
            A comprehensive strategy to enhance customer experience through cutting-edge AI and data-driven solutions.
          </p>
        </div>
      </header>

      <main className="max-w-5xl mx-auto p-4 sm:p-6 lg:p-8 mt-8">
        {planData.map((section: PlanSection) => (
          <SectionDisplay key={section.id} section={section} />
        ))}
      </main>

      <footer className="bg-gray-800 text-white py-8 text-center mt-12">
        <p>&copy; {new Date().getFullYear()} Techcombank. All Rights Reserved (Conceptual Plan).</p>
        <p className="text-sm text-gray-400 mt-1">Generated for demonstration purposes.</p>
      </footer>
    </div>
  );
};

export default App;
    