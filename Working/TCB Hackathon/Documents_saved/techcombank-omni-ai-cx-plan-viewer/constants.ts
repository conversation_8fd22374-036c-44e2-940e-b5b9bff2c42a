
import { PlanSection, SectionContentType, TimelineEntry, BudgetCategory, KeyValueItem } from './types';

const planData: PlanSection[] = [
  {
    id: 'data-layer',
    title: '1. Data Layer Implementation',
    purpose: 'Set up a streaming database and Customer Data Platform (CDP) to collect and unify real-time customer data from omnichannel touchpoints (mobile app, website, ATM, branches, call centers).',
    subSections: [
      {
        title: 'Components and Tools',
        contentBlocks: [
          {
            type: SectionContentType.COMPONENTS_TOOLS,
            data: [
              { category: 'Customer Data Platform (CDP)', tools: ['Segment or Salesforce CDP to unify customer profiles.'] },
              { category: 'Streaming Database', tools: ['Apache Kafka for real-time data ingestion', 'Cassandra for storage.'] },
              { category: 'Feedback Data Store', tools: ['Elasticsearch for unstructured feedback (e.g., surveys, X posts).'] },
            ]
          }
        ]
      },
      {
        title: 'Implementation Steps',
        contentBlocks: [
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            title: 'Data Source Integration (Month 1-2)',
            data: { heading: 'Configure Apache Kafka:', text: 'Deploy Kafka on AWS (e.g., Amazon MSK) with 3 brokers for high availability. Set up topics for each channel: mobile_app_events, website_events, atm_events, branch_events, call_center_logs.'}
          },
          {
            type: SectionContentType.CODE,
            data: { code: 'bootstrap.servers=broker1:9092,broker2:9092,broker3:9092\nnum.partitions=10\nreplication.factor=3', language: 'properties', caption: 'Example Kafka Configuration' }
          },
          {
            type: SectionContentType.PARAGRAPH,
            data: 'Integrate channel systems (e.g., mobile app, ATM) with Kafka producers using REST APIs or SDKs (e.g., Kafka Python client).'
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Set up CDP:', text: 'Choose Segment for its ease of integration with banking systems. Configure Segment to ingest data from Kafka topics and unify into customer profiles (e.g., user ID, transactions, feedback). Define schema: customer_id, channel, timestamp, event_type (e.g., login, transaction), event_details.'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Feedback Data Store:', text: 'Deploy Elasticsearch on AWS (Amazon OpenSearch Service) for storing unstructured feedback. Create an index (customer_feedback) for surveys, call transcripts, and X posts. Use Logstash to ingest social media feedback from X via its API.'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            title: 'Data Cleaning and Enrichment (Month 2-3)',
            data: { heading: 'Pipeline Implementation:', text: 'Implement a data pipeline using Apache Spark on Databricks to clean data (e.g., remove duplicates, handle missing values). Enrich data with metadata (e.g., customer segment: individual, SME, VIP). Store cleaned data in Cassandra for structured data and Elasticsearch for unstructured data.'}
          },
          {
            type: SectionContentType.CODE,
            data: { 
              code: `from pyspark.sql import SparkSession
spark = SparkSession.builder.appName("DataCleaning").getOrCreate()
df = spark.readStream.format("kafka").option("kafka.bootstrap.servers", "broker1:9092").option("subscribe", "mobile_app_events").load()
# Example: Filter out null event_details and add a default segment
# In a real scenario, segment would be derived from more complex logic or data
from pyspark.sql.functions import lit 
cleaned_df = df.filter(df.event_details.isNotNull()).withColumn("segment", lit("individual")) 
# Ensure keyspace and table exist in Cassandra
# cleaned_df.writeStream.format("cassandra").option("keyspace", "techcombank").option("table", "customer_events").start()
print("Spark job configured to stream to Cassandra (actual writeStream commented out for demo)")`, 
              language: 'python', 
              caption: 'Example Spark Job (Python)' 
            }
          }
        ]
      },
      {
        title: 'Team Roles',
        contentBlocks: [
          {
            type: SectionContentType.TEAM_ROLES,
            data: [
              { role: 'Data Engineers', description: 'Set up Kafka, Cassandra, and Elasticsearch.' },
              { role: 'Data Architects', description: 'Design CDP schema and data pipelines.' },
              { role: 'DevOps', description: 'Configure AWS infrastructure (MSK, OpenSearch).' },
            ]
          }
        ]
      },
      {
        title: 'Deliverables',
        contentBlocks: [
          {
            type: SectionContentType.DELIVERABLES,
            data: [
              'Unified customer profiles in CDP with real-time data from all channels.',
              'Streaming pipeline processing 100,000 events/day.',
              'Feedback index with 10,000+ daily entries from surveys and X.',
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'ai-pipeline',
    title: '2. Modular AI Pipeline Implementation',
    purpose: 'Develop and deploy composable AI modules for Behavioral Clustering, Demand Prediction, Churn Analytics, Sentiment Analysis, and AI Chatbot using specified ML models and tools.',
    subSections: [
      {
        title: 'Components and Tools',
        contentBlocks: [
          {
            type: SectionContentType.COMPONENTS_TOOLS,
            data: [
              { category: 'Behavioral Clustering', tools: ['K-means and DBSCAN on Databricks (Scikit-learn).'] },
              { category: 'Demand Prediction/Churn Analytics', tools: ['Random Forest and XGBoost on MLflow (Databricks).'] },
              { category: 'Sentiment Analysis', tools: ['Hugging Face BERT or LLaMA on Databricks.'] },
              { category: 'AI Chatbot', tools: ['Grok or LLaMA with FastAPI for API integration.'] },
            ]
          }
        ]
      },
      {
        title: 'Implementation Steps - Behavioral Clustering Module (Month 3-4)',
        id: 'ai-behavioral-clustering',
        contentBlocks: [
          { 
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Data Preparation:', text: 'Extract features from CDP: transaction frequency, channel usage (app vs. branch), average transaction value. Use Spark on Databricks to preprocess data (e.g., normalize features).' }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Model Development (K-means):', text: 'Implement K-means for general segmentation (e.g., individuals, SMEs, VIPs).' }
          },
          {
            type: SectionContentType.CODE,
            data: {
              code: `from sklearn.cluster import KMeans
import pandas as pd
# Assuming customer_features.csv is prepared and available
# data = pd.read_csv("customer_features.csv") 
# Example DataFrame creation for demonstration
data = pd.DataFrame({
    'customer_id': range(1, 101),
    'transaction_freq': [10, 12, 5, 20, 22, 15, 8, 9, 18, 17] * 10, # Example data
    'channel_usage': [0.8, 0.7, 0.9, 0.3, 0.2, 0.5, 0.85, 0.75, 0.4, 0.35] * 10 # Example data
})
kmeans = KMeans(n_clusters=3, random_state=42, n_init='auto') # Added n_init
clusters = kmeans.fit_predict(data[["transaction_freq", "channel_usage"]])
data["segment"] = clusters`,
              language: 'python',
              caption: 'K-means Clustering'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Model Development (DBSCAN):', text: 'Use DBSCAN to identify outliers (e.g., VIPs with unusual behavior).' }
          },
          {
            type: SectionContentType.CODE,
            data: {
              code: `from sklearn.cluster import DBSCAN
# Using the same 'data' DataFrame from K-means example
dbscan = DBSCAN(eps=0.5, min_samples=5)
outliers = dbscan.fit_predict(data[["transaction_freq", "channel_usage"]])
# Outliers are typically labeled -1 by DBSCAN
data["is_outlier_dbscan"] = outliers == -1`,
              language: 'python',
              caption: 'DBSCAN for Outlier Detection'
            }
          },
           {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Deployment:', text: 'Deploy models on Databricks using MLflow for versioning. Store segment labels in CDP for use in personalization.' }
          },
          {
            type: SectionContentType.OUTPUT_DESCRIPTION,
            data: 'Customer segments (e.g., 60% individuals, 30% SMEs, 10% VIPs).'
          }
        ]
      },
      {
        title: 'Implementation Steps - Demand Prediction/Churn Analytics Module (Month 4-6)',
        id: 'ai-demand-churn',
        contentBlocks: [
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Data Preparation:', text: 'Extract features: transaction history, channel interactions, customer demographics. Label data for churn (e.g., 1 for customers who left, 0 for active).' }
            },
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Model Development:', text: 'Train Random Forest and XGBoost models using MLflow on Databricks. Use MLflow to compare model performance (e.g., accuracy, AUC).' }
            },
            {
                type: SectionContentType.CODE,
                data: {
                    code: `from xgboost import XGBClassifier
# from mlflow import log_metric, log_model # Assuming MLflow is set up
# Example data (replace with actual data loading and splitting)
# X_train, y_train, X_test, y_test = load_and_split_data() 

# model = XGBClassifier(n_estimators=100, random_state=42)
# model.fit(X_train, y_train)
# accuracy = model.score(X_test, y_test)
# log_metric("accuracy", accuracy)
# log_model(model, "xgboost_model")
print("XGBoost model training and MLflow logging snippet (conceptual)")`,
                    language: 'python',
                    caption: 'XGBoost with MLflow (Conceptual)'
                }
            },
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Deployment:', text: 'Deploy models as REST APIs via MLflow’s serving feature. Example endpoint: /predict_churn?customer_id=123 returns churn probability.' }
            },
            {
                type: SectionContentType.OUTPUT_DESCRIPTION,
                data: 'Churn risk scores (e.g., 0.75 for high-risk SMEs) and demand predictions (e.g., 80% likelihood of loan application).'
            }
        ]
      },
      {
        title: 'Implementation Steps - Sentiment Analysis Module (Month 5-7)',
        id: 'ai-sentiment-analysis',
        contentBlocks: [
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Data Preparation:', text: 'Pull feedback data from Elasticsearch (surveys, X posts, call transcripts). Preprocess text (e.g., tokenize, remove stop words) using Spark NLP.' }
            },
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Model Development:', text: 'Fine-tune Hugging Face BERT for sentiment classification (positive, negative, neutral). Deploy on Databricks with distributed computing for large-scale feedback.' }
            },
            {
                type: SectionContentType.CODE,
                data: {
                    code: `from transformers import pipeline
# Ensure model is downloaded or accessible
# sentiment_analyzer = pipeline("sentiment-analysis", model="bert-base-multilingual-cased")
# feedback = ["App khó sử dụng", "Dịch vụ tuyệt vời"]
# results = sentiment_analyzer(feedback)
# Output: [{"label": "NEGATIVE", "score": 0.95}, {"label": "POSITIVE", "score": 0.90}]
print("Hugging Face BERT sentiment analysis snippet (conceptual, model loading may take time)")
print('Example Output: [{"label": "NEGATIVE", "score": 0.95}, {"label": "POSITIVE", "score": 0.90}]')`,
                    language: 'python',
                    caption: 'Sentiment Analysis with Hugging Face BERT'
                }
            },
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Deployment:', text: 'Expose sentiment scores via API for real-time monitoring. Store results in CDP for journey mapping and optimization.' }
            },
            {
                type: SectionContentType.OUTPUT_DESCRIPTION,
                data: 'Sentiment scores and pain point insights (e.g., 30% negative feedback on app navigation).'
            }
        ]
      },
      {
        title: 'Implementation Steps - AI Chatbot Module (Month 3-5)',
        id: 'ai-chatbot',
        contentBlocks: [
            {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Model Development:', text: 'Fine-tune Grok or LLaMA on banking-specific queries (e.g., “How to apply for a loan?”) using Databricks. Use Hugging Face’s Transformers for model training.' }
            },
            {
                type: SectionContentType.CODE,
                data: {
                    code: `from transformers import AutoModelForCausalLM, AutoTokenizer
# model = AutoModelForCausalLM.from_pretrained("meta-llama/LLaMA-7B") # Large model
# tokenizer = AutoTokenizer.from_pretrained("meta-llama/LLaMA-7B")
# input_text = "Hướng dẫn mở tài khoản"
# inputs = tokenizer(input_text, return_tensors="pt")
# response = model.generate(**inputs)
# print(tokenizer.decode(response[0]))
print("LLaMA model generation snippet (conceptual, requires significant resources)")`,
                    language: 'python',
                    caption: 'LLaMA Chatbot Model (Conceptual)'
                }
            },
             {
                type: SectionContentType.SUB_HEADING_PARAGRAPH,
                data: { heading: 'Deployment:', text: 'Host chatbot as a REST API using FastAPI on AWS. Integrate with mobile app, website, and call center via WebSocket for real-time interaction.' }
            },
            {
                type: SectionContentType.CODE,
                data: {
                    code: `from fastapi import FastAPI
app = FastAPI()
# Mock response for demonstration
# In reality, this would call the fine-tuned LLaMA model
mock_model_response = "Để mở tài khoản, bạn cần CMND/CCCD..."

@app.post("/chatbot")
async def chatbot(query: str):
    # response = model.generate(tokenizer(query, return_tensors="pt")) # Actual model call
    # return {"response": tokenizer.decode(response[0])}
    return {"response": f"Echoing your query for demo: {query}. A real answer would be: {mock_model_response}"}

# To run: uvicorn filename:app --reload
# This is a conceptual FastAPI snippet.`,
                    language: 'python',
                    caption: 'FastAPI Chatbot Endpoint (Conceptual)'
                }
            },
            {
                type: SectionContentType.OUTPUT_DESCRIPTION,
                data: 'Real-time responses reducing wait times (e.g., 90% of queries resolved in under 10 seconds).'
            }
        ]
      },
      {
        title: 'Team Roles',
        contentBlocks: [
          {
            type: SectionContentType.TEAM_ROLES,
            data: [
              { role: 'Data Scientists', description: 'Develop and fine-tune ML models (K-means, XGBoost, BERT).' },
              { role: 'AI Engineers', description: 'Deploy models and APIs on Databricks and FastAPI.' },
              { role: 'Data Engineers', description: 'Build data pipelines for AI modules.' },
            ]
          }
        ]
      },
      {
        title: 'Deliverables',
        contentBlocks: [
          {
            type: SectionContentType.DELIVERABLES,
            data: [
              'Behavioral segments for 13.8M customers (e.g., 8M individuals, 4M SMEs, 1.8M VIPs).',
              'Churn predictions for 90% of high-risk customers.',
              'Sentiment analysis for 10,000+ daily feedback entries.',
              'Chatbot handling 1,000+ queries/hour with 85% accuracy.',
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'application-layer',
    title: '3. Application Layer Implementation',
    purpose: 'Integrate AI outputs into customer-facing systems (mobile app, website, ATM, branches, call centers) and internal tools for seamless omnichannel experiences.',
    subSections: [
      {
        title: 'Components and Tools',
        contentBlocks: [
          {
            type: SectionContentType.COMPONENTS_TOOLS,
            data: [
              { category: 'Omnichannel Integration Platform', tools: ['Salesforce or FastAPI-based microservices.'] },
              { category: 'Backend Services', tools: ['Python Flask or FastAPI on AWS.'] },
              { category: 'Internal Dashboards', tools: ['Databricks Dashboards for staff.'] },
            ]
          }
        ]
      },
      {
        title: 'Implementation Steps',
        id: 'applayer-steps',
        contentBlocks: [
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Omnichannel Integration (Month 4-6):', text: 'Deploy Salesforce or a custom microservices platform using FastAPI. Create APIs to deliver AI outputs (e.g., churn scores, recommendations) to channels. Ensure real-time synchronization using Kafka for consistent data across channels.'}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `from fastapi import FastAPI
app = FastAPI()
# Mock function to simulate fetching recommendation
def get_recommendation_from_ml_model(customer_id: str):
    # In a real app, query MLflow model or other recommendation engine
    if customer_id == "123":
        return "Savings Plan X"
    elif customer_id == "456":
        return "Investment Portfolio Y"
    return "General Offer Z"

@app.get("/recommendation/{customer_id}")
async def get_recommendation(customer_id: str):
    recommendation = get_recommendation_from_ml_model(customer_id)
    return {"customer_id": customer_id, "recommendation": recommendation}`,
                language: 'python',
                caption: 'FastAPI Recommendation Endpoint'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Backend Services (Month 5-7):', text: 'Host backend on AWS ECS (Elastic Container Service) for scalability. Integrate with mobile app/website using REST APIs and WebSocket for chatbot. Example: Push personalized offers to app based on clustering results. Configure load balancing for high availability (e.g., AWS ALB).'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Internal Dashboards (Month 6-8):', text: 'Use Databricks Dashboards to display AI outputs for CX team and branch managers. Example dashboard: Churn risk scores, segment distribution, sentiment trends.'}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import plotly.express as px
import pandas as pd
# Example data for dashboard
df = pd.DataFrame({
    "customer_id": ["123", "456", "789", "101"], 
    "churn_risk": [0.75, 0.20, 0.90, 0.10],
    "segment": ["SME", "Individual", "SME", "VIP"]
})
# fig = px.bar(df, x="customer_id", y="churn_risk", title="Churn Risk by Customer", color="segment")
# fig.show() # In a Databricks notebook, this would render the plot
print("Plotly bar chart for churn risk (conceptual for Databricks dashboard)")`,
                language: 'python',
                caption: 'Plotly Churn Risk Dashboard Snippet'
            }
          }
        ]
      },
      {
        title: 'Team Roles',
        contentBlocks: [
          {
            type: SectionContentType.TEAM_ROLES,
            data: [
              { role: 'Backend Developers', description: 'Build FastAPI services and integrate with channels.' },
              { role: 'DevOps', description: 'Deploy and manage backend on AWS ECS.' },
              { role: 'Data Analysts', description: 'Design Databricks Dashboards.' },
            ]
          }
        ]
      },
      {
        title: 'Deliverables',
        contentBlocks: [
          {
            type: SectionContentType.DELIVERABLES,
            data: [
              'APIs serving 10,000+ requests/hour across channels.',
              'Consistent data across app, website, and ATM for 95% of customers.',
              'Internal dashboards for 100+ staff users.',
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'visualization-layer',
    title: '4. Visualization Layer Implementation',
    purpose: 'Deliver AI-driven insights (journey maps, behavioral clusters, touchpoint optimizations) to staff and customers via Databricks Dashboards and a Vietnamese-language Streamlit interface.',
    subSections: [
      {
        title: 'Components and Tools',
        contentBlocks: [
          {
            type: SectionContentType.COMPONENTS_TOOLS,
            data: [
              { category: 'Databricks Dashboards', tools: ['Plotly for internal visualizations (journey maps, clusters, KPIs).'] },
              { category: 'Streamlit Interface', tools: ['Vietnamese-language app for staff/customer-facing visuals.'] },
            ]
          }
        ]
      },
      {
        title: 'Implementation Steps - Databricks Dashboards (Month 6-8)',
        id: 'viz-databricks',
        contentBlocks: [
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Setup:', text: 'Configure Databricks workspace on AWS with PySpark for data processing. Connect to CDP and AI pipeline outputs via JDBC.'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Journey Maps Visualization:', text: 'Use Plotly to create flow diagrams showing touchpoints and pain points.'}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import plotly.graph_objects as go
# fig = go.Figure(data=[go.Sankey(
#     node=dict(label=["Login", "Loan Application", "Approval", "Drop-off"]),
#     link=dict(source=[0, 1, 0, 1], target=[1, 2, 3, 3], value=[800, 500, 200, 300]) # Example values
# )])
# fig.update_layout(title_text="Customer Journey Sankey Diagram", font_size=10)
# fig.show()
print("Plotly Sankey diagram for customer journey (conceptual)")`,
                language: 'python',
                caption: 'Plotly Journey Map (Sankey)'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Behavioral Clusters Visualization:', text: 'Scatter plots of customer segments.'}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import plotly.express as px
import pandas as pd
# df_clusters = pd.DataFrame({ # Assume df_clusters is loaded from CDP/AI output
#     "transaction_freq": [10, 20, 5, 30, 25], 
#     "channel_usage": [0.8, 0.5, 0.9, 0.2, 0.3], 
#     "segment": ["Individual", "SME", "Individual", "VIP", "SME"]
# })
# fig = px.scatter(df_clusters, x="transaction_freq", y="channel_usage", color="segment", title="Behavioral Clusters")
# fig.show()
print("Plotly scatter plot for behavioral clusters (conceptual)")`,
                language: 'python',
                caption: 'Plotly Behavioral Clusters'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Optimization Recommendations:', text: 'Bar charts for KPI improvements (e.g., NPS, churn rate). Access: Restrict dashboards to CX team and managers via Databricks RBAC.'}
          }
        ]
      },
      {
        title: 'Implementation Steps - Streamlit Interface (Vietnamese) (Month 7-9)',
        id: 'viz-streamlit',
        contentBlocks: [
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Setup:', text: 'Develop Streamlit app using Python, hosted on AWS EC2 or Google Cloud Run. Use streamlit-i18n for Vietnamese localization.'}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import streamlit as st
# import streamlit_i18n # For localization - setup needed
# st.set_page_config(page_title="Techcombank CX Dashboard") # Basic config
# st.title("Bảng Điều Khiển Trải Nghiệm Khách Hàng") # Vietnamese title
print("Streamlit app setup snippet (conceptual)")`,
                language: 'python',
                caption: 'Streamlit App Setup (Vietnamese)'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Behavioral Clustering Chart (Streamlit):', text: ''}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import streamlit as st
import plotly.express as px
import pandas as pd
# # df_clusters = pd.read_csv("clusters.csv") # Load data
# # For demo, create a sample DataFrame
# df_clusters = pd.DataFrame({
#     "transaction_freq": [10, 20, 5, 30, 25, 12, 22, 8], 
#     "channel_usage": [0.8, 0.5, 0.9, 0.2, 0.3, 0.7, 0.4, 0.85], 
#     "segment": ["Individual", "SME", "Individual", "VIP", "SME", "Individual", "SME", "VIP"]
# })
# st.subheader("Phân Cụm Hành Vi Khách Hàng")
# fig = px.scatter(df_clusters, x="transaction_freq", y="channel_usage", color="segment", title="Phân Cụm Hành Vi Khách Hàng")
# st.plotly_chart(fig)
print("Streamlit behavioral clustering chart snippet (conceptual)")`,
                language: 'python',
                caption: 'Streamlit Behavioral Clustering Chart'
            }
          },
           {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Customer Journey Map (Streamlit):', text: ''}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import streamlit as st
import plotly.graph_objects as go
# st.subheader("Hành Trình Khách Hàng")
# fig_sankey = go.Figure(data=[go.Sankey( # Same Sankey as Databricks example
#     node=dict(label=["Đăng nhập", "Đăng ký vay", "Phê duyệt", "Bỏ dở"]),
#     link=dict(source=[0, 1, 0, 1], target=[1, 2, 3, 3], value=[800, 500, 200, 300])
# )])
# fig_sankey.update_layout(title_text="Hành Trình Khách Hàng", font_size=10)
# st.plotly_chart(fig_sankey)
print("Streamlit customer journey map snippet (conceptual)")`,
                language: 'python',
                caption: 'Streamlit Customer Journey Map'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Touchpoint Optimization Heatmap (Streamlit):', text: ''}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `import streamlit as st
import seaborn as sns
import matplotlib.pyplot as plt
import numpy as np
# st.subheader("Tối Ưu Điểm Chạm")
# # Example heatmap data (e.g., friction scores)
# data_heatmap = np.array([[0.9, 0.2, 0.5], [0.3, 0.8, 0.1], [0.4, 0.6, 0.7]]) 
# touchpoints = ['App Login', 'Website FAQ', 'Branch Visit']
# kpis = ['High Friction', 'Medium Friction', 'Low Friction']

# fig, ax = plt.subplots()
# sns.heatmap(data_heatmap, annot=True, cmap="YlOrRd", ax=ax, 
#             xticklabels=kpis, yticklabels=touchpoints)
# ax.set_title("Điểm Ma Sát Các Điểm Chạm")
# st.pyplot(fig)
print("Streamlit touchpoint optimization heatmap snippet (conceptual)")`,
                language: 'python',
                caption: 'Streamlit Touchpoint Optimization Heatmap'
            }
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Access:', text: 'Staff: Secure login via OAuth 2.0 for CX team to view analytics. Customers: Public-facing version with personalized insights (e.g., financial dashboards for SMEs).'}
          }
        ]
      },
      {
        title: 'Team Roles',
        contentBlocks: [
          {
            type: SectionContentType.TEAM_ROLES,
            data: [
              { role: 'Data Visualization Engineers', description: 'Build Databricks and Streamlit visuals.' },
              { role: 'UI/UX Designers', description: 'Design Vietnamese-language Streamlit interface.' },
              { role: 'Frontend Developers', description: 'Deploy Streamlit app.' },
            ]
          }
        ]
      },
      {
        title: 'Deliverables',
        contentBlocks: [
          {
            type: SectionContentType.DELIVERABLES,
            data: [
              'Databricks Dashboards for 100+ staff, visualizing journey maps and KPIs.',
              'Streamlit app in Vietnamese, serving 10,000+ daily users (staff and customers).',
              'Interactive charts for clustering, journey maps, and optimization.',
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'supporting-infrastructure',
    title: '5. Supporting Infrastructure Implementation',
    purpose: 'Ensure scalability, security, and monitoring for the AI pipeline and visualization layers.',
    subSections: [
      {
        title: 'Components and Tools',
        contentBlocks: [
          {
            type: SectionContentType.COMPONENTS_TOOLS,
            data: [
              { category: 'Cloud Platform', tools: ['AWS for hosting.'] },
              { category: 'API Gateway', tools: ['AWS API Gateway for data exchange.'] },
              { category: 'Security', tools: ['AWS IAM, encryption, OAuth 2.0.'] },
              { category: 'Monitoring', tools: ['Prometheus, Grafana, MLflow.'] },
            ]
          }
        ]
      },
      {
        title: 'Implementation Steps',
        id: 'infra-steps',
        contentBlocks: [
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Cloud Setup (Month 1-2):', text: 'Deploy Databricks, Kafka, and Cassandra on AWS. Configure VPC with private subnets for security. Use AWS EKS for containerized FastAPI services.'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'API Gateway (Month 3-4):', text: 'Set up AWS API Gateway to manage AI module APIs (e.g., /predict_churn, /chatbot). Configure rate limiting and authentication (OAuth 2.0).'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Security (Month 1-3):', text: 'Encrypt data at rest (AES-256) and in transit (TLS 1.3). Implement AWS IAM roles for fine-grained access control. Ensure compliance with Vietnam’s PDPL (Personal Data Protection Law).'}
          },
          {
            type: SectionContentType.SUB_HEADING_PARAGRAPH,
            data: { heading: 'Monitoring (Month 4-6):', text: 'Deploy Prometheus and Grafana for system metrics (e.g., API latency, model performance). Use MLflow to track model metrics (e.g., accuracy, inference time).'}
          },
          {
            type: SectionContentType.CODE,
            data: {
                code: `Panel: API Latency
Metric: http_request_duration_seconds
Query: sum(rate(http_request_duration_seconds_sum[5m])) by (handler) / sum(rate(http_request_duration_seconds_count[5m])) by (handler)
Visualization: Time Series
Legend: {{handler}}`,
                language: 'yaml', 
                caption: 'Example Grafana Panel Configuration (Conceptual)'
            }
          }
        ]
      },
      {
        title: 'Team Roles',
        contentBlocks: [
          {
            type: SectionContentType.TEAM_ROLES,
            data: [
              { role: 'DevOps', description: 'Configure AWS infrastructure and monitoring.' },
              { role: 'Security Engineers', description: 'Implement encryption and compliance measures.' },
            ]
          }
        ]
      },
      {
        title: 'Deliverables',
        contentBlocks: [
          {
            type: SectionContentType.DELIVERABLES,
            data: [
              'Scalable AWS infrastructure handling 1M+ daily transactions.',
              'Secure APIs with 99.9% uptime.',
              'Monitoring dashboards tracking 100+ metrics.',
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'timeline-roadmap',
    title: '6. Timeline and Roadmap',
    purpose: 'A phased approach to deliver the Omni-AI CX capabilities.',
    subSections: [
      {
        title: 'Project Phases',
        id: 'timeline-phases',
        contentBlocks: [
          {
            type: SectionContentType.KEY_VALUE_LIST,
            data: [
              { key: 'Month 1-3', value: 'Data Layer setup (Kafka, CDP, Elasticsearch), initial Chatbot deployment.' },
              { key: 'Month 4-6', value: 'Behavioral Clustering, Demand Prediction, Churn Analytics modules, Omnichannel integration.' },
              { key: 'Month 7-9', value: 'Sentiment Analysis module, Streamlit interface, Databricks Dashboards.' },
              { key: 'Month 10-12', value: 'Full rollout, A/B testing, KPI monitoring, and optimization.' },
            ] as KeyValueItem[]
          }
        ]
      }
    ]
  },
  {
    id: 'budget-estimate',
    title: '7. Budget Estimate',
    purpose: 'Estimated budget allocation for various components of the project.',
    subSections: [
      {
        title: 'Cost Breakdown',
        id: 'budget-breakdown',
        contentBlocks: [
          {
            type: SectionContentType.CHART,
            data: {
                type: 'budget', 
                chartData: [
                    { name: 'Cloud Infrastructure', costRange: '10-15', unit: 'billion VND', details: 'AWS, Databricks licensing and usage.' },
                    { name: 'AI Development', costRange: '15-25', unit: 'billion VND', details: 'Model training, Databricks, MLflow, specialized AI tools.' },
                    { name: 'Visualization Tools', costRange: '5-10', unit: 'billion VND', details: 'Streamlit hosting, Databricks Dashboards, potential licensing.' },
                    { name: 'Team (Annual)', costRange: '~10', unit: 'billion VND', details: 'Approx. 50 engineers (data scientists, AI engineers, DevOps).' },
                ] as BudgetCategory[]
            }
          },
          {
            type: SectionContentType.PARAGRAPH, 
            data: "The budget includes costs for cloud services like AWS and Databricks, AI model development and training, visualization tool licensing and hosting, and the annual cost for a team of approximately 50 specialized engineers."
          }
        ]
      }
    ]
  },
  {
    id: 'risks-mitigation',
    title: '8. Risks and Mitigation',
    purpose: 'Identifying potential risks and outlining strategies to mitigate them.',
    subSections: [
      {
        title: 'Potential Challenges',
        id: 'risks-challenges',
        contentBlocks: [
          {
            type: SectionContentType.RISKS,
            data: [
              { risk: 'Data integration delays.', mitigation: 'Prioritize Kafka setup and test with sample data in Month 1.' },
              { risk: 'Model underperformance (e.g., low clustering accuracy).', mitigation: 'Use MLflow to compare K-means vs. DBSCAN and iterate models. Allocate time for multiple model iterations.' },
              { risk: 'Customer resistance to chatbot.', mitigation: 'Fine-tune Grok/LLaMA with Vietnamese banking queries, ensure extensive UAT, and provide human fallback options with clear escalation paths.' },
              { risk: 'Regulatory Compliance (PDPL).', mitigation: 'Engage legal and compliance teams early. Implement robust data governance and consent management frameworks. Conduct regular audits.' },
              { risk: 'Scalability issues with increased data volume.', mitigation: 'Design for scalability from the outset using services like AWS MSK, Cassandra, and ECS. Implement auto-scaling and load testing.' },
            ]
          }
        ]
      }
    ]
  },
  {
    id: 'expected-outcomes',
    title: '9. Expected Outcomes',
    purpose: 'Key performance indicators (KPIs) and targets to measure the success of the implementation.',
    subSections: [
      {
        title: 'Key Performance Indicators (KPIs)',
        id: 'outcomes-kpis',
        contentBlocks: [
          {
            type: SectionContentType.OUTCOMES,
            data: [
              { metric: 'Net Promoter Score (NPS)', target: 'Increase by 10-15 points within 12 months.' },
              { metric: 'Customer Churn Rate', target: 'Reduce by 20% for individuals/SMEs, 30% for VIPs.' },
              { metric: 'Digital Adoption Rate', target: 'Increase to 80% for individual customers.' },
              { metric: 'Pain Point Resolution (Complaints)', target: 'Reduce complaints by 25% via sentiment-driven optimizations.' },
            ]
          }
        ]
      }
    ]
  }
];

export default planData;
