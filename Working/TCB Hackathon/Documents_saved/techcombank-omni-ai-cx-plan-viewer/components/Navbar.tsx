
import React from 'react';
import { PlanSection } from '../types';
import { 
  DataLayerIcon, AiPipelineIcon, ApplicationLayerIcon, VisualizationIcon, 
  InfrastructureIcon, TimelineIcon, BudgetIcon, RisksIcon, OutcomesIcon
} from './IconComponents';

interface NavbarProps {
  sections: Pick<PlanSection, 'id' | 'title'>[];
  currentSection: string;
  onNavigate: (id: string) => void;
}

const SectionIcon: React.FC<{ id: string, className?: string }> = ({ id, className }) => {
  const iconProps = { className: className || "w-5 h-5 mr-3" };
  switch (id) {
    case 'data-layer': return <DataLayerIcon {...iconProps} />;
    case 'ai-pipeline': return <AiPipelineIcon {...iconProps} />;
    case 'application-layer': return <ApplicationLayerIcon {...iconProps} />;
    case 'visualization-layer': return <VisualizationIcon {...iconProps} />;
    case 'supporting-infrastructure': return <InfrastructureIcon {...iconProps} />;
    case 'timeline-roadmap': return <TimelineIcon {...iconProps} />;
    case 'budget-estimate': return <BudgetIcon {...iconProps} />;
    case 'risks-mitigation': return <RisksIcon {...iconProps} />;
    case 'expected-outcomes': return <OutcomesIcon {...iconProps} />;
    default: return null;
  }
};

const Navbar: React.FC<NavbarProps> = ({ sections, currentSection, onNavigate }) => {
  return (
    <nav className="sticky top-0 z-50 bg-white shadow-md">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <span className="text-2xl font-bold text-techcombank-red">Techcombank</span>
              <span className="text-2xl font-bold text-primary-dark ml-1">Omni-AI CX</span>
            </div>
          </div>
          <div className="hidden md:block">
            <div className="ml-10 flex items-baseline space-x-4">
              {sections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => onNavigate(section.id)}
                  className={`px-3 py-2 rounded-md text-sm font-medium transition-colors duration-150
                    ${currentSection === section.id 
                      ? 'bg-techcombank-red text-white' 
                      : 'text-gray-700 hover:bg-gray-100 hover:text-techcombank-red'
                    }`}
                >
                  {section.title.split(' ')[0]} {/* Show first word for brevity */}
                </button>
              ))}
            </div>
          </div>
        </div>
      </div>
      {/* Mobile Nav - scrollspy effect implies main content scrolling, so full mobile menu might be less critical for this specific app type.
          However, if desired, a dropdown could be added here. For now, focusing on desktop nav. */}
    </nav>
  );
};

export default Navbar;
    