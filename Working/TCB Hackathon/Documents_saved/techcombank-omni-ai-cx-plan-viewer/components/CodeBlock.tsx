
import React from 'react';
import { CodeBracketIcon } from './IconComponents';

interface CodeBlockProps {
  code: string;
  language?: string;
  caption?: string;
}

const CodeBlock: React.FC<CodeBlockProps> = ({ code, language, caption }) => {
  const langClass = language ? `language-${language}` : '';
  return (
    <div className="my-4 bg-gray-800 rounded-lg shadow-lg overflow-hidden">
      {(language || caption) && (
        <div className="flex justify-between items-center px-4 py-2 bg-gray-700 text-gray-300 text-xs font-semibold">
          <div className="flex items-center">
             <CodeBracketIcon className="w-4 h-4 mr-2 text-techcombank-red"/>
            {language && <span className="uppercase ">{language}</span>}
          </div>
          {caption && <span className="italic">{caption}</span>}
        </div>
      )}
      <pre className={`p-4 text-sm text-gray-100 overflow-x-auto ${langClass}`}>
        <code>{code.trim()}</code>
      </pre>
    </div>
  );
};

export default CodeBlock;
    