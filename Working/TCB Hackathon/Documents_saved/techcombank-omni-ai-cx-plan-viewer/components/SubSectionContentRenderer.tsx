
import React from 'react';
import { 
  SubSection<PERSON>ontent<PERSON>lock, SectionContentType, CodeContent, TeamRole, 
  ComponentToolCategory, RiskItem, OutcomeItem, KeyValueItem, SubHeadingParagraph, ImagePlaceholder
} from '../types';
import CodeBlock from './CodeBlock';
import { CheckCircleIcon, UserGroupIcon, CogIcon, ListBulletIcon, DocumentTextIcon, EyeIcon } from './IconComponents';

interface SubSectionContentRendererProps {
  contentBlock: SubSectionContentBlock;
}

const SubSectionContentRenderer: React.FC<SubSectionContentRendererProps> = ({ contentBlock }) => {
  const { type, data, title } = contentBlock;

  const renderContent = () => {
    switch (type) {
      case SectionContentType.PARAGRAPH:
        return <p className="text-gray-700 leading-relaxed">{data as string}</p>;
      
      case SectionContentType.SUB_HEADING_PARAGRAPH:
        const shpData = data as SubHeadingParagraph;
        return (
          <div>
            <h5 className="text-md font-semibold text-primary-dark mt-3 mb-1">{shpData.heading}</h5>
            <p className="text-gray-700 leading-relaxed">{shpData.text}</p>
          </div>
        );

      case SectionContentType.LIST:
        return (
          <ul className="list-disc list-inside space-y-1 text-gray-700 pl-4">
            {(data as string[]).map((item, index) => (
              <li key={index} className="leading-relaxed">{item}</li>
            ))}
          </ul>
        );
      
      case SectionContentType.ORDERED_LIST:
        return (
          <ol className="list-decimal list-inside space-y-1 text-gray-700 pl-4">
            {(data as string[]).map((item, index) => (
              <li key={index} className="leading-relaxed">{item}</li>
            ))}
          </ul >
        );

      case SectionContentType.CODE:
        const codeData = data as CodeContent;
        return <CodeBlock code={codeData.code} language={codeData.language} caption={codeData.caption} />;

      case SectionContentType.TEAM_ROLES:
        return (
          <div className="space-y-3">
            {(data as TeamRole[]).map((role, index) => (
              <div key={index} className="p-3 bg-indigo-50 rounded-md border border-indigo-200">
                <h5 className="font-semibold text-indigo-700 flex items-center"><UserGroupIcon className="w-5 h-5 mr-2 text-indigo-500" />{role.role}</h5>
                <p className="text-sm text-indigo-600 ml-7">{role.description}</p>
              </div>
            ))}
          </div>
        );
      
      case SectionContentType.COMPONENTS_TOOLS:
        if (Array.isArray(data) && data.length > 0 && typeof data[0] === 'string') {
          // Simple list of tools
          return (
            <div className="flex flex-wrap gap-2">
              {(data as string[]).map((tool, index) => (
                <span key={index} className="px-3 py-1 text-sm bg-teal-100 text-teal-700 rounded-full shadow-sm flex items-center">
                  <CogIcon className="w-4 h-4 mr-1.5 text-teal-500" /> {tool}
                </span>
              ))}
            </div>
          );
        }
        return (
          <div className="space-y-3">
            {(data as ComponentToolCategory[]).map((categoryItem, index) => (
              <div key={index} className="p-3 bg-sky-50 rounded-md border border-sky-200">
                <h5 className="font-semibold text-sky-700 flex items-center"><CogIcon className="w-5 h-5 mr-2 text-sky-500" />{categoryItem.category}</h5>
                <div className="flex flex-wrap gap-2 mt-2 pl-7">
                  {categoryItem.tools.map((tool, toolIndex) => (
                     <span key={toolIndex} className="px-3 py-1 text-xs bg-sky-100 text-sky-700 rounded-full shadow-sm">
                       {tool}
                     </span>
                  ))}
                </div>
              </div>
            ))}
          </div>
        );

      case SectionContentType.DELIVERABLES:
        return (
          <ul className="space-y-2">
            {(data as string[]).map((item, index) => (
              <li key={index} className="flex items-start text-gray-700">
                <CheckCircleIcon className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="leading-relaxed">{item}</span>
              </li>
            ))}
          </ul>
        );
      
      case SectionContentType.OUTPUT_DESCRIPTION:
        return (
          <div className="p-3 bg-purple-50 border border-purple-200 rounded-md mt-2">
            <p className="text-sm text-purple-700 font-medium flex items-center">
              <EyeIcon className="w-5 h-5 mr-2 text-purple-500" /> Output:
            </p>
            <p className="text-sm text-purple-600 ml-7">{data as string}</p>
          </div>
        );

      case SectionContentType.RISKS:
        return (
          <div className="space-y-4">
            {(data as RiskItem[]).map((item, index) => (
              <div key={index} className="p-4 bg-red-50 border border-red-200 rounded-lg shadow-sm">
                <h5 className="font-semibold text-red-700">Risk: {item.risk}</h5>
                <p className="text-sm text-red-600 mt-1"><strong>Mitigation:</strong> {item.mitigation}</p>
              </div>
            ))}
          </div>
        );

      case SectionContentType.OUTCOMES:
        return (
          <div className="grid md:grid-cols-2 gap-4">
            {(data as OutcomeItem[]).map((item, index) => (
              <div key={index} className="p-4 bg-green-50 border border-green-200 rounded-lg shadow-sm">
                <h5 className="font-semibold text-green-700">{item.metric}</h5>
                <p className="text-2xl font-bold text-green-600 mt-1">{item.target}</p>
              </div>
            ))}
          </div>
        );
      
      case SectionContentType.KEY_VALUE_LIST:
         return (
          <ul className="space-y-2">
            {(data as KeyValueItem[]).map((item, index) => (
              <li key={index} className="p-2 bg-gray-50 rounded-md border border-gray-200">
                <span className="font-semibold text-primary-dark">{item.key}:</span>
                <span className="text-gray-700 ml-2">{item.value}</span>
              </li>
            ))}
          </ul>
        );
      
      case SectionContentType.IMAGE_PLACEHOLDER:
        const imgData = data as ImagePlaceholder;
        return (
          <div className="my-4 text-center">
            <img 
              src={`https://picsum.photos/${imgData.width || 600}/${imgData.height || 400}?grayscale&random=${Math.random()}`} 
              alt={imgData.alt} 
              className="mx-auto rounded-lg shadow-md border border-gray-300"
              width={imgData.width || 600}
              height={imgData.height || 400}
            />
            {imgData.caption && <p className="text-sm text-gray-600 mt-2 italic">{imgData.caption}</p>}
          </div>
        );

      default:
        return <p className="text-red-500">Unsupported content type: {type}</p>;
    }
  };

  const getIconForTitle = (titleStr?: string) => {
    if (!titleStr) return null;
    if (titleStr.toLowerCase().includes('components and tools')) return <CogIcon className="w-5 h-5 mr-2 text-techcombank-red" />;
    if (titleStr.toLowerCase().includes('implementation steps')) return <ListBulletIcon className="w-5 h-5 mr-2 text-techcombank-red" />;
    if (titleStr.toLowerCase().includes('team roles')) return <UserGroupIcon className="w-5 h-5 mr-2 text-techcombank-red" />;
    if (titleStr.toLowerCase().includes('deliverables')) return <CheckCircleIcon className="w-5 h-5 mr-2 text-techcombank-red" />;
    if (titleStr.toLowerCase().includes('output')) return <EyeIcon className="w-5 h-5 mr-2 text-techcombank-red" />;
    return <DocumentTextIcon className="w-5 h-5 mr-2 text-techcombank-red" />;
  }

  return (
    <div className="mb-6">
      {title && (
        <h4 className="text-lg font-semibold text-primary-dark mb-2 flex items-center">
          {getIconForTitle(title)}
          {title}
        </h4>
      )}
      {renderContent()}
    </div>
  );
};

export default SubSectionContentRenderer;

    