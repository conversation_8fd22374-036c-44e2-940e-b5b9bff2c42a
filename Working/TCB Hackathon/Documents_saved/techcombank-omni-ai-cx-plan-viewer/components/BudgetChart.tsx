
import React from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, <PERSON>Axis, CartesianGrid, Tooltip, Legend, ResponsiveContainer, Cell } from 'recharts';
import { BudgetItemForChart, BudgetCategory } from '../types';

interface BudgetChartProps {
  data: BudgetCategory[];
}

const parseCostRange = (costRange: string): { min: number, max: number, avg: number } => {
  if (costRange.includes('~')) {
    const val = parseFloat(costRange.replace('~', ''));
    return { min: val, max: val, avg: val };
  }
  const parts = costRange.split('-').map(p => parseFloat(p));
  if (parts.length === 2) {
    return { min: parts[0], max: parts[1], avg: (parts[0] + parts[1]) / 2 };
  }
  return { min: 0, max: 0, avg: 0 }; // Should not happen with valid data
};

const BudgetChart: React.FC<BudgetChartProps> = ({ data }) => {
  const chartData: BudgetItemForChart[] = data.map(item => {
    const parsedCost = parseCostRange(item.costRange);
    return {
      name: item.name,
      avgCost: parsedCost.avg,
      costLabel: `${item.costRange} ${item.unit}`,
    };
  });

  const colors = ['#E60000', '#FF5C5C', '#FF8282', '#FFB3B3']; // Techcombank red shades

  return (
    <div className="p-4 bg-white rounded-lg shadow h-96">
      <h3 className="text-lg font-semibold text-primary-dark mb-4">Budget Allocation (Average Estimated Cost)</h3>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={chartData} layout="vertical" margin={{ top: 5, right: 30, left: 100, bottom: 5 }}>
          <CartesianGrid strokeDasharray="3 3" stroke="#e0e0e0" />
          <XAxis type="number" unit=" billion VND" stroke="#4A5568" />
          <YAxis dataKey="name" type="category" width={150} stroke="#4A5568" interval={0} />
          <Tooltip
            formatter={(value: number, name: string, props) => [`${props.payload.costLabel}`, "Cost"]}
            cursor={{ fill: 'rgba(230, 230, 230, 0.5)' }} 
          />
          <Legend wrapperStyle={{ color: '#4A5568' }} />
          <Bar dataKey="avgCost" name="Average Estimated Cost (Billion VND)" fill="#E60000" barSize={30}>
             {chartData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={colors[index % colors.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default BudgetChart;
    