
import React from 'react';
import { PlanSection, SectionContentType } from '../types';
import SubSectionContentRenderer from './SubSectionContentRenderer';
import BudgetChart from './BudgetChart'; // Import BudgetChart
import { 
  DataLayerIcon, AiPipelineIcon, ApplicationLayerIcon, VisualizationIcon, 
  InfrastructureIcon, TimelineIcon, BudgetIcon, RisksIcon, OutcomesIcon, ChartBarIcon
} from './IconComponents';


interface SectionDisplayProps {
  section: PlanSection;
}

const SectionIcon: React.FC<{ id: string, className?: string }> = ({ id, className }) => {
  const iconProps = { className: className || "w-8 h-8 mr-3 text-techcombank-red" };
  switch (id) {
    case 'data-layer': return <DataLayerIcon {...iconProps} />;
    case 'ai-pipeline': return <AiPipelineIcon {...iconProps} />;
    case 'application-layer': return <ApplicationLayerIcon {...iconProps} />;
    case 'visualization-layer': return <VisualizationIcon {...iconProps} />;
    case 'supporting-infrastructure': return <InfrastructureIcon {...iconProps} />;
    case 'timeline-roadmap': return <TimelineIcon {...iconProps} />;
    case 'budget-estimate': return <BudgetIcon {...iconProps} />;
    case 'risks-mitigation': return <RisksIcon {...iconProps} />;
    case 'expected-outcomes': return <OutcomesIcon {...iconProps} />;
    default: return null;
  }
};

const SectionDisplay: React.FC<SectionDisplayProps> = ({ section }) => {
  return (
    <section id={section.id} className="mb-12 p-6 bg-white rounded-xl shadow-lg">
      <header className="mb-6 border-b border-border-gray pb-4">
        <h2 className="text-3xl font-bold text-techcombank-red flex items-center">
          <SectionIcon id={section.id} />
          {section.title}
        </h2>
        {section.purpose && (
          <p className="mt-2 text-lg text-secondary-gray italic">{section.purpose}</p>
        )}
      </header>

      {section.subSections.map((subSection, index) => (
        <div key={index} id={subSection.id || `${section.id}-sub-${index}`} className="mb-8 p-4 border border-gray-200 rounded-lg bg-gray-50/50 shadow-sm">
          <h3 className="text-xl font-semibold text-primary-dark mb-4 pb-2 border-b border-gray-300">
            {subSection.title}
          </h3>
          {subSection.contentBlocks.map((contentBlock, cbIndex) => (
            <div key={cbIndex} className="mb-4 last:mb-0">
              {/* Special handling for Budget Chart */}
              {section.id === 'budget-estimate' && contentBlock.type === SectionContentType.CHART && contentBlock.data?.type === 'budget' ? (
                 <div className="mt-4">
                   <h4 className="text-lg font-semibold text-primary-dark mb-2 flex items-center">
                     <ChartBarIcon className="w-5 h-5 mr-2 text-techcombank-red" />
                     Budget Visualization
                   </h4>
                   <BudgetChart data={contentBlock.data.chartData} />
                 </div>
              ) : (
                <SubSectionContentRenderer contentBlock={contentBlock} />
              )}
            </div>
          ))}
        </div>
      ))}
    </section>
  );
};

export default SectionDisplay;
    