import csv
from pymongo import MongoClient

# MongoDB connection
client = MongoClient("*************************************************************************************************************")

# Connect to DB and Collection
db = client["knowledge_graph"]
collection = db["deposits"]

# Output CSV file
filename = "knowledge_graph_deposits_50K.csv"

# Field names to extract (including nested)
fields = [
    "_id",
    "walletAddress",
    "lendingPoolAddress",
    "depositLogs.1675817528.tokens.******************************************",
    "depositLogs.1675817528.valueInUSD",
    "depositTokens.******************************************",
    "totalNumberOfDeposit",
    "totalAmountOfDepositInUSD",
    "highestDepositInUSD",
    "lowestDepositInUSD",
    "averageDepositInUSD"
]

# Projection for top-level fields (nested accessed manually)
projection = {
    "_id": 1,
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "depositLogs": 1,
    "depositTokens": 1,
    "totalNumberOfDeposit": 1,
    "totalAmountOfDepositInUSD": 1,
    "highestDepositInUSD": 1,
    "lowestDepositInUSD": 1,
    "averageDepositInUSD": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# Write to CSV
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(fields)  # Header row

    for doc in cursor:
        row = [
            doc.get("_id", ""),
            doc.get("walletAddress", ""),
            doc.get("lendingPoolAddress", ""),
            doc.get("depositLogs", {}).get("1675817528", {}).get("tokens", {}).get("******************************************", ""),
            doc.get("depositLogs", {}).get("1675817528", {}).get("valueInUSD", ""),
            doc.get("depositTokens", {}).get("******************************************", ""),
            doc.get("totalNumberOfDeposit", ""),
            doc.get("totalAmountOfDepositInUSD", ""),
            doc.get("highestDepositInUSD", ""),
            doc.get("lowestDepositInUSD", ""),
            doc.get("averageDepositInUSD", "")
        ]
        writer.writerow(row)

print(f"✅ Exported 50,000 records to '{filename}' from 'knowledge_graph.deposits'")
