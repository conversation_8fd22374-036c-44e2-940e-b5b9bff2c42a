from pymongo import MongoClient
import pandas as pd

# MongoDB connection
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["repays"]

# Fetch documents
documents = collection.find({}, {
    "_id": 1,
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "repayLogs": 1,
    "totalAmountOfRepayInUSD": 1,
    "totalNumberOfRepay": 1,
    "highestRepayInUSD": 1,
    "lowestRepayInUSD": 1,
    "averageRepayInUSD": 1
}).limit(1000000)

rows = []

# Flatten nested repay logs
for doc in documents:
    base = {
        "_id": doc.get("_id"),
        "walletAddress": doc.get("walletAddress"),
        "lendingPoolAddress": doc.get("lendingPoolAddress"),
        "totalAmountOfRepayInUSD": doc.get("totalAmountOfRepayInUSD"),
        "totalNumberOfRepay": doc.get("totalNumberOfRepay"),
        "highestRepayInUSD": doc.get("highestRepayInUSD"),
        "lowestRepayInUSD": doc.get("lowestRepayInUSD"),
        "averageRepayInUSD": doc.get("averageRepayInUSD")
    }

    repayLogs = doc.get("repayLogs", {})
    for ts, entry in repayLogs.items():
        if "valueInUSD" in entry:
            rows.append({**base, "timestamp": int(ts), "type": "repayValue", "token": None, "value": entry["valueInUSD"]})

        for token, value in entry.get("tokens", {}).items():
            rows.append({**base, "timestamp": int(ts), "type": "repayToken", "token": token, "value": value})

# Convert to DataFrame
df = pd.DataFrame(rows)
df.to_csv("1M_knowledge_graph_repays.csv", index=False)
