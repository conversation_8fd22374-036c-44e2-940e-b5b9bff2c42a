from pymongo import MongoClient
import pandas as pd
import time
from pymongo.errors import OperationFailure, NetworkTimeout, ServerSelectionTimeoutError

def export_dex_events_batch():
    """Export DEX events in batches with retry logic to handle network issues"""

    # --- MongoDB connection without timeout constraints ---
    def create_connection():
        return MongoClient(
            "*************************************************************************************************************",
            maxPoolSize=5,
            retryWrites=True,
            retryReads=True
        )

    try:
        client = create_connection()
        # Test connection
        client.admin.command('ping')
        print("✅ Connected to MongoDB successfully")

    except (ServerSelectionTimeoutError, NetworkTimeout) as e:
        print(f"❌ Failed to connect to MongoDB: {e}")
        return

    db = client["ethereum_blockchain_etl"]
    collection = db["dex_events"]

    # --- Projection fields ---
    projection = {
        "_id": 1,
        "block_number": 1,
        "amount0_in": 1,
        "amount0_out": 1,
        "amount1_in": 1,
        "amount1_out": 1,
        "block_timestamp": 1,
        "contract_address": 1,
        "event_type": 1,
        "log_index": 1,
        "sender": 1,
        "to": 1,
        "topic": 1,
        "transaction_hash": 1,
        "type": 1,
        "wallet": 1,
    }

    # --- Process in smaller batches with retry logic ---
    batch_size = 50000   # Reduced to 50K per batch for better reliability
    total_target = 1000000  # 1M total
    num_batches = total_target // batch_size
    max_retries = 3

    all_data = []
    successful_batches = 0

    print(f"📊 Processing {total_target:,} documents in {num_batches} batches of {batch_size:,} each...")

    for batch_num in range(num_batches):
        batch_success = False

        for retry in range(max_retries):
            try:
                if retry > 0:
                    print(f"🔄 Retry {retry}/{max_retries-1} for batch {batch_num + 1}/{num_batches}...")
                    # Recreate connection on retry
                    client.close()
                    time.sleep(5)  # Wait before retry
                    client = create_connection()
                    db = client["ethereum_blockchain_etl"]
                    collection = db["dex_events"]
                else:
                    print(f"🔄 Processing batch {batch_num + 1}/{num_batches}...")

                # Create pipeline for this batch
                pipeline = [
                    {"$sample": {"size": batch_size}},
                    {"$project": projection}
                ]

                # Execute aggregation without timeout
                cursor = collection.aggregate(pipeline)

                # Convert to list and extend our data
                batch_data = list(cursor)
                all_data.extend(batch_data)

                print(f"✅ Batch {batch_num + 1} completed: {len(batch_data):,} documents")
                successful_batches += 1
                batch_success = True
                break

            except (OperationFailure, NetworkTimeout) as e:
                print(f"⚠️ Network/Operation error for batch {batch_num + 1}, retry {retry + 1}: {e}")
                if retry == max_retries - 1:
                    print(f"❌ Failed batch {batch_num + 1} after {max_retries} retries")
                continue
            except Exception as e:
                print(f"❌ Unexpected error in batch {batch_num + 1}, retry {retry + 1}: {e}")
                if retry == max_retries - 1:
                    print(f"❌ Failed batch {batch_num + 1} after {max_retries} retries")
                continue

        if batch_success:
            # Small delay between successful batches
            time.sleep(2)

    print(f"📊 Successfully completed {successful_batches}/{num_batches} batches")

    if not all_data:
        print("❌ No data was retrieved. Exiting.")
        return

    print(f"📊 Total documents retrieved: {len(all_data):,}")

    # --- Convert to DataFrame ---
    try:
        df = pd.DataFrame(all_data)
        print(f"✅ DataFrame created with {len(df):,} rows and {len(df.columns)} columns")

        # --- Export to CSV ---
        output_filename = "1M_ethereum_blockchain_dex_events.csv"
        df.to_csv(output_filename, index=False)
        print(f"✅ Exported {len(df):,} documents to {output_filename}")

    except Exception as e:
        print(f"❌ Error creating DataFrame or exporting CSV: {e}")

    finally:
        # Close connection
        client.close()
        print("🔌 MongoDB connection closed")

if __name__ == "__main__":
    export_dex_events_batch()
