from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["ethereum_blockchain_etl"]
collection = db["dex_events"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "block_number": 1,
    "amount0_in": 1,
    "amount0_out": 1,
    "amount1_in": 1,
    "amount1_out": 1,
    "block_timestamp": 1,
    "contract_address": 1,
    "event_type": 1,
    "log_index": 1,
    "sender": 1,
    "to": 1,
    "topic": 1,
    "transaction_hash": 1,
    "type": 1,
    "wallet": 1,
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}            # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("ethereum_blockchain_dex_events_50k.csv", index=False)
print("✅ Exported 50,000 documents to ethereum_blockchain_lending_events_50k.csv")
