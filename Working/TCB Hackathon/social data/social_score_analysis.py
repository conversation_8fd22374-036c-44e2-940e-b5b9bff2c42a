import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
import shap
import warnings
warnings.filterwarnings('ignore')

# Data preprocessing
def preprocess_data(df):
    # Convert created to datetime
    df['created'] = pd.to_datetime(df['created'])
    
    # Calculate account age in days
    df['account_age_days'] = (pd.Timestamp.now(tz='UTC') - df['created']).dt.days
    
    # Calculate engagement metrics
    df['engagement_rate'] = (df['favouritesCount'] + df['statusesCount']) / df['followersCount'].replace(0, 1)
    df['followers_friends_ratio'] = df['followersCount'] / df['friendsCount'].replace(0, 1)
    
    # Create features for social score
    features = [
        'favouritesCount', 'friendsCount', 'listedCount', 'mediaCount',
        'followersCount', 'statusesCount', 'account_age_days',
        'engagement_rate', 'followers_friends_ratio'
    ]
    
    # Handle missing values
    df[features] = df[features].fillna(0)
    
    return df, features

# Calculate social score
def calculate_social_score(df, features):
    # Scale the features
    scaler = StandardScaler()
    X = scaler.fit_transform(df[features])
    
    # Train a Random Forest model
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    y = df['followersCount']  # Using followers as a proxy for social influence
    model.fit(X, y)
    
    # Calculate social score
    df['social_score'] = model.predict(X)
    
    # Normalize social score to 0-100 range
    df['social_score'] = ((df['social_score'] - df['social_score'].min()) / 
                         (df['social_score'].max() - df['social_score'].min()) * 100)
    
    return df, model, scaler

# Create visualizations
def create_visualizations(df, features, model):
    # Set style
    plt.style.use('seaborn-v0_8')
    
    # 1. Social Score Distribution
    plt.figure(figsize=(10, 6))
    sns.histplot(data=df, x='social_score', bins=50)
    plt.title('Distribution of Social Scores')
    plt.xlabel('Social Score')
    plt.ylabel('Count')
    plt.savefig('social_score_distribution.png')
    plt.close()
    
    # 2. Correlation Matrix
    plt.figure(figsize=(12, 8))
    correlation_matrix = df[['social_score', 'followersCount', 'favouritesCount', 
                           'statusesCount', 'engagement_rate']].corr()
    sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
    plt.title('Feature Correlation Matrix')
    plt.savefig('correlation_matrix.png')
    plt.close()
    
    # 3. Top Features Impact
    plt.figure(figsize=(10, 6))
    feature_importance = pd.DataFrame({
        'feature': features,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    sns.barplot(data=feature_importance, x='importance', y='feature')
    plt.title('Feature Importance in Social Score')
    plt.xlabel('Importance')
    plt.ylabel('Feature')
    plt.savefig('feature_importance.png')
    plt.close()

# SHAP Analysis
def perform_shap_analysis(model, X, features):
    # Calculate SHAP values
    explainer = shap.TreeExplainer(model)
    shap_values = explainer.shap_values(X)
    
    # Summary plot
    plt.figure(figsize=(10, 6))
    shap.summary_plot(shap_values, X, feature_names=features, show=False)
    plt.title('SHAP Feature Importance')
    plt.savefig('shap_summary.png')
    plt.close()
    
    # Dependence plots for top features
    for feature in features[:3]:
        plt.figure(figsize=(10, 6))
        shap.dependence_plot(feature, shap_values, X, feature_names=features, show=False)
        plt.title(f'SHAP Dependence Plot for {feature}')
        plt.savefig(f'shap_dependence_{feature}.png')
        plt.close()

def main():
    # Read the data
    df = pd.read_csv('twitter_users_social_scoring_50K.csv')
    
    # Preprocess data
    df, features = preprocess_data(df)
    
    # Calculate social score
    df, model, scaler = calculate_social_score(df, features)
    
    # Create visualizations
    create_visualizations(df, features, model)
    
    # Perform SHAP analysis
    X = scaler.transform(df[features])
    perform_shap_analysis(model, X, features)
    
    # Save results
    df.to_csv('twitter_users_with_scores.csv', index=False)
    
    print("Analysis complete! Check the generated visualizations and the updated CSV file.")

if __name__ == "__main__":
    main() 