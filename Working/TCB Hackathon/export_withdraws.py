from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["withdraws"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "withdrawLogs.1677482363.tokens.******************************************": 1,
    "withdrawLogs.1677482363.valueInUSD": 1,
    "withdrawLogs.1677483275.tokens.******************************************": 1,
    "withdrawLogs.1677483275.valueInUSD": 1,
    "withdrawLogs.1717240919.tokens.******************************************": 1,
    "withdrawLogs.1717240919.valueInUSD": 1,
    "withdrawLogs.1717889795.tokens.******************************************": 1,
    "withdrawLogs.1717889795.valueInUSD": 1,
    "totalNumberOfWithdraw": 1,
    "totalAmountOfWithdrawInUSD": 1,
    "highestWithdrawInUSD": 1,
    "lowestWithdrawInUSD": 1,
    "averageWithdrawInUSD": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load to DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("knowledge_graph_withdraws_50K.csv", index=False)
print("✅ Exported 50,000 documents to withdraws_export_50k.csv")
