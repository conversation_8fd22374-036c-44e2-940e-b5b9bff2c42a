import csv
from pymongo import MongoClient

# MongoDB connection
client = MongoClient("*************************************************************************************************************")

# Connect to DB and Collection
db = client["knowledge_graph"]
collection = db["borrows"]

# Define the output CSV file
filename = "full_knowledge_graph_borrows_1M.csv"

# Set up projection
projection = {
    "_id": 1,
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "borrowLogs": 1,
    "borrowTokens": 1,
    "totalNumberOfBorrow": 1,
    "totalAmountOfBorrowInUSD": 1,
    "highestBorrowInUSD": 1,
    "lowestBorrowInUSD": 1,
    "averageBorrowInUSD": 1
}

# --- Sample 1000,000 documents ---
pipeline = [
    {"$sample": {"size": 1000000}},
    {"$project": projection}
]

cursor = collection.aggregate(pipeline)

# Prepare CSV fields
csv_headers = [
    "walletAddress",
    "lendingPoolAddress",
    "timestamp",
    "tokenAddress",
    "valueInUSD",
    "totalNumberOfBorrow",
    "totalAmountOfBorrowInUSD",
    "highestBorrowInUSD",
    "lowestBorrowInUSD",
    "averageBorrowInUSD"
]

# Write to CSV
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(csv_headers)

    for doc in cursor:
        wallet = doc.get("walletAddress", "")
        pool = doc.get("lendingPoolAddress", "")
        stats = [
            doc.get("totalNumberOfBorrow", ""),
            doc.get("totalAmountOfBorrowInUSD", ""),
            doc.get("highestBorrowInUSD", ""),
            doc.get("lowestBorrowInUSD", ""),
            doc.get("averageBorrowInUSD", "")
        ]

        borrow_logs = doc.get("borrowLogs", {})
        for timestamp, log in borrow_logs.items():
            value = log.get("valueInUSD", "")
            tokens = log.get("tokens", {})
            for token_address, token_data in tokens.items():
                row = [
                    wallet,
                    pool,
                    timestamp,
                    token_address,
                    value,
                    *stats
                ]
                writer.writerow(row)

print(f"✅ Exported 1,000,000 documents to '{filename}'")
