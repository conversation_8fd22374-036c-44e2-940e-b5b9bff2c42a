import csv
from pymongo import MongoClient

# MongoDB connection
client = MongoClient("*************************************************************************************************************")

# Select the database and collection
db = client["knowledge_graph"]
collection = db["liquidates"]

# Output CSV file
filename = "knowledge_graph_liquidiates_50K.csv"

# Fields to export
fields = [
    "_id",
    "debtBuyerWallet",
    "liquidatedWallet",
    "liquidationLogs.debtBuyerWallet.1676012980.collateralAmount",
    "liquidationLogs.debtBuyerWallet.1676012980.collateralAsset",
    "liquidationLogs.debtBuyerWallet.1676012980.collateralAssetInUSD",
    "liquidationLogs.debtBuyerWallet.1676012980.protocol",
    "liquidationLogs.liquidatedWallet.1676012980.debtAmount",
    "liquidationLogs.liquidatedWallet.1676012980.debtAsset",
    "liquidationLogs.liquidatedWallet.1676012980.debtAssetInUSD",
    "liquidationLogs.liquidatedWallet.1676012980.protocol"
]

# Projection (include only top fields; nested accessed manually)
projection = {
    "_id": 1,
    "debtBuyerWallet": 1,
    "liquidatedWallet": 1,
    "liquidationLogs": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# Write to CSV
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(fields)  # Write header

    for doc in cursor:
        row = [
            doc.get("_id", ""),
            doc.get("debtBuyerWallet", ""),
            doc.get("liquidatedWallet", ""),
            doc.get("liquidationLogs", {}).get("debtBuyerWallet", {}).get("1676012980", {}).get("collateralAmount", ""),
            doc.get("liquidationLogs", {}).get("debtBuyerWallet", {}).get("1676012980", {}).get("collateralAsset", ""),
            doc.get("liquidationLogs", {}).get("debtBuyerWallet", {}).get("1676012980", {}).get("collateralAssetInUSD", ""),
            doc.get("liquidationLogs", {}).get("debtBuyerWallet", {}).get("1676012980", {}).get("protocol", ""),
            doc.get("liquidationLogs", {}).get("liquidatedWallet", {}).get("1676012980", {}).get("debtAmount", ""),
            doc.get("liquidationLogs", {}).get("liquidatedWallet", {}).get("1676012980", {}).get("debtAsset", ""),
            doc.get("liquidationLogs", {}).get("liquidatedWallet", {}).get("1676012980", {}).get("debtAssetInUSD", ""),
            doc.get("liquidationLogs", {}).get("liquidatedWallet", {}).get("1676012980", {}).get("protocol", "")
        ]
        writer.writerow(row)

print(f"✅ Exported 10,000 records to '{filename}' from 'knowledge_graph.liquidiates'")
