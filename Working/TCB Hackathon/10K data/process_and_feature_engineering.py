import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.impute import SimpleImputer
import glob
import os
import json
from datetime import datetime

class DataProcessor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.minmax_scaler = MinMaxScaler()
        self.label_encoder = LabelEncoder()
        self.imputer = SimpleImputer(strategy='mean')
        
    def clean_data(self, df):
        # Remove duplicates
        df = df.drop_duplicates()
        
        # Convert column names to lowercase and replace spaces with underscores
        df.columns = df.columns.str.lower().str.replace(' ', '_')
        
        # Handle missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        # Fill numeric missing values with mean
        if len(numeric_cols) > 0:
            df[numeric_cols] = self.imputer.fit_transform(df[numeric_cols])
        
        # Fill categorical missing values with mode
        for col in categorical_cols:
            df[col] = df[col].fillna(df[col].mode()[0])
        
        return df
    
    def normalize_numeric_data(self, df):
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 0:
            df[numeric_cols] = self.scaler.fit_transform(df[numeric_cols])
        return df
    
    def encode_categorical_data(self, df):
        categorical_cols = df.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            df[col] = self.label_encoder.fit_transform(df[col].astype(str))
        return df
    
    def create_features(self, df, file_name):
        features = {}
        
        # Basic statistical features for numeric columns
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        for col in numeric_cols:
            features[f'{col}_mean'] = df[col].mean()
            features[f'{col}_std'] = df[col].std()
            features[f'{col}_min'] = df[col].min()
            features[f'{col}_max'] = df[col].max()
            features[f'{col}_median'] = df[col].median()
            
            # Create quartile features
            q1, q3 = df[col].quantile([0.25, 0.75])
            features[f'{col}_q1'] = q1
            features[f'{col}_q3'] = q3
            features[f'{col}_iqr'] = q3 - q1
            
            # Create outlier detection features
            lower_bound = q1 - 1.5 * (q3 - q1)
            upper_bound = q3 + 1.5 * (q3 - q1)
            features[f'{col}_outliers_count'] = ((df[col] < lower_bound) | (df[col] > upper_bound)).sum()
        
        # Categorical features
        categorical_cols = df.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            features[f'{col}_unique_count'] = df[col].nunique()
            features[f'{col}_mode'] = df[col].mode()[0]
            
        # Save features to JSON
        output_file = f'features_{os.path.splitext(file_name)[0]}.json'
        with open(output_file, 'w') as f:
            json.dump(features, f, indent=4)
        
        return features
    
    def process_file(self, file_path):
        print(f"\nProcessing {file_path}...")
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        print(f"Original shape: {df.shape}")
        
        # Clean the data
        df = self.clean_data(df)
        print(f"Shape after cleaning: {df.shape}")
        
        # Create features
        features = self.create_features(df, os.path.basename(file_path))
        
        # Normalize numeric data
        df = self.normalize_numeric_data(df)
        
        # Encode categorical data
        df = self.encode_categorical_data(df)
        
        # Save processed data
        output_file = f'processed_{os.path.basename(file_path)}'
        df.to_csv(output_file, index=False)
        print(f"Processed data saved to {output_file}")
        
        return df, features

def main():
    processor = DataProcessor()
    csv_files = glob.glob('*.csv')
    
    # Process each file
    for file in csv_files:
        if not file.startswith('processed_') and not file.startswith('consolidated_'):
            try:
                df, features = processor.process_file(file)
                print(f"\nFeatures created for {file}:")
                for key, value in features.items():
                    print(f"{key}: {value}")
            except Exception as e:
                print(f"Error processing {file}: {str(e)}")

if __name__ == "__main__":
    main() 