import pandas as pd
import numpy as np
import glob
import os
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_knowledge_graph_files():
    # Get all knowledge graph files
    csv_files = glob.glob('knowledge_graph_*.csv')
    
    # Create a dictionary to store wallet addresses from each file
    wallet_sets = {}
    total_wallets = set()
    
    print("Analyzing wallet address distribution across files...")
    print("\nFile Analysis:")
    print("-" * 50)
    
    for file in csv_files:
        # Read the CSV file
        df = pd.read_csv(file)
        file_name = os.path.splitext(os.path.basename(file))[0]
        
        # Find wallet column
        wallet_col = None
        for col in df.columns:
            if col.lower() in ['wallet', 'wallet_address', 'address', 'user_address', 'user_wallet']:
                wallet_col = col
                break
        
        if wallet_col:
            # Get unique wallet addresses
            wallets = set(df[wallet_col].unique())
            wallet_sets[file_name] = wallets
            total_wallets.update(wallets)
            
            print(f"\n{file_name}:")
            print(f"Total rows: {len(df)}")
            print(f"Unique wallet addresses: {len(wallets)}")
    
    # Calculate overlaps
    print("\nWallet Address Overlap Analysis:")
    print("-" * 50)
    
    # Create overlap matrix
    overlap_matrix = pd.DataFrame(index=wallet_sets.keys(), columns=wallet_sets.keys())
    
    for file1 in wallet_sets:
        for file2 in wallet_sets:
            overlap = len(wallet_sets[file1].intersection(wallet_sets[file2]))
            overlap_matrix.loc[file1, file2] = overlap
    
    print("\nOverlap Matrix (number of common wallet addresses):")
    print(overlap_matrix)
    
    # Calculate percentage overlaps
    print("\nPercentage Overlap Matrix:")
    percentage_matrix = pd.DataFrame(index=wallet_sets.keys(), columns=wallet_sets.keys())
    
    for file1 in wallet_sets:
        for file2 in wallet_sets:
            if len(wallet_sets[file1]) > 0:
                percentage = (len(wallet_sets[file1].intersection(wallet_sets[file2])) / len(wallet_sets[file1])) * 100
                percentage_matrix.loc[file1, file2] = f"{percentage:.2f}%"
    
    print(percentage_matrix)
    
    # Create visualization
    plt.figure(figsize=(12, 8))
    # Convert overlap matrix to numeric type
    overlap_matrix_numeric = overlap_matrix.astype(float)
    sns.heatmap(overlap_matrix_numeric, annot=True, cmap='YlOrRd', fmt='.0f')
    plt.title('Wallet Address Overlap Between Files')
    plt.tight_layout()
    plt.savefig('wallet_overlap_heatmap.png')
    print("\nOverlap heatmap saved as 'wallet_overlap_heatmap.png'")
    
    # Analyze the consolidated file
    if os.path.exists('knowledge_graph_profiles.csv'):
        print("\nAnalyzing consolidated file...")
        print("-" * 50)
        
        df = pd.read_csv('knowledge_graph_profiles.csv')
        
        # Find wallet column
        wallet_col = None
        for col in df.columns:
            if col.lower() in ['wallet', 'wallet_address', 'address', 'user_address', 'user_wallet']:
                wallet_col = col
                break
        
        if wallet_col:
            print(f"\nConsolidated File Statistics:")
            print(f"Total unique wallet addresses: {len(df[wallet_col].unique())}")
            print(f"Total rows: {len(df)}")
            
            # Analyze unknown values
            unknown_counts = {}
            for col in df.columns:
                if col != wallet_col:
                    unknown_count = (df[col] == 'unknown').sum() if df[col].dtype == 'object' else 0
                    if unknown_count > 0:
                        unknown_counts[col] = unknown_count
            
            if unknown_counts:
                print("\nColumns with 'unknown' values:")
                for col, count in unknown_counts.items():
                    percentage = (count / len(df)) * 100
                    print(f"{col}: {count} unknown values ({percentage:.2f}%)")
            else:
                print("\nNo 'unknown' values found in the consolidated file.")

if __name__ == "__main__":
    analyze_knowledge_graph_files() 