import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import glob
import os
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor, RandomForestClassifier
import json

class FeatureVisualizer:
    def __init__(self):
        self.output_dir = 'feature_visualizations'
        if not os.path.exists(self.output_dir):
            os.makedirs(self.output_dir)
        
    def plot_numeric_distributions(self, df, file_name):
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        
        for col in numeric_cols:
            plt.figure(figsize=(10, 6))
            
            # Create subplot with 2 plots
            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))
            
            # Distribution plot
            sns.histplot(data=df, x=col, ax=ax1)
            ax1.set_title(f'Distribution of {col}')
            
            # Box plot
            sns.boxplot(data=df, y=col, ax=ax2)
            ax2.set_title(f'Box Plot of {col}')
            
            plt.tight_layout()
            plt.savefig(f'{self.output_dir}/{file_name}_{col}_distribution.png')
            plt.close()
    
    def plot_categorical_distributions(self, df, file_name):
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        for col in categorical_cols:
            plt.figure(figsize=(12, 6))
            value_counts = df[col].value_counts().head(10)  # Top 10 categories
            
            sns.barplot(x=value_counts.index, y=value_counts.values)
            plt.title(f'Top 10 Categories in {col}')
            plt.xticks(rotation=45, ha='right')
            plt.tight_layout()
            plt.savefig(f'{self.output_dir}/{file_name}_{col}_distribution.png')
            plt.close()
    
    def plot_correlation_matrix(self, df, file_name):
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        if len(numeric_cols) > 1:
            plt.figure(figsize=(12, 8))
            correlation_matrix = df[numeric_cols].corr()
            
            sns.heatmap(correlation_matrix, annot=True, cmap='coolwarm', center=0)
            plt.title(f'Correlation Matrix for {file_name}')
            plt.tight_layout()
            plt.savefig(f'{self.output_dir}/{file_name}_correlation_matrix.png')
            plt.close()
    
    def plot_feature_importance(self, df, file_name):
        # Separate numeric and categorical features
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        categorical_cols = df.select_dtypes(include=['object']).columns
        
        # Create a copy of the dataframe for feature importance
        df_importance = df.copy()
        
        # Encode categorical variables
        for col in categorical_cols:
            df_importance[col] = pd.Categorical(df_importance[col]).codes
        
        # Remove any remaining non-numeric columns
        df_importance = df_importance.select_dtypes(include=[np.number])
        
        if len(df_importance.columns) > 1:
            # Choose target variable (last column)
            X = df_importance.iloc[:, :-1]
            y = df_importance.iloc[:, -1]
            
            # Fit Random Forest
            model = RandomForestRegressor(n_estimators=100, random_state=42)
            model.fit(X, y)
            
            # Get feature importance
            importance = pd.DataFrame({
                'feature': X.columns,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            # Plot feature importance
            plt.figure(figsize=(12, 6))
            sns.barplot(data=importance, x='importance', y='feature')
            plt.title(f'Feature Importance for {file_name}')
            plt.tight_layout()
            plt.savefig(f'{self.output_dir}/{file_name}_feature_importance.png')
            plt.close()
            
            # Save feature importance to JSON
            importance_dict = dict(zip(importance['feature'], importance['importance']))
            with open(f'{self.output_dir}/{file_name}_feature_importance.json', 'w') as f:
                json.dump(importance_dict, f, indent=4)
    
    def visualize_file(self, file_path):
        print(f"\nVisualizing {file_path}...")
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        
        # Generate visualizations
        self.plot_numeric_distributions(df, file_name)
        self.plot_categorical_distributions(df, file_name)
        self.plot_correlation_matrix(df, file_name)
        self.plot_feature_importance(df, file_name)
        
        print(f"Visualizations saved in {self.output_dir} directory")

def main():
    visualizer = FeatureVisualizer()
    csv_files = glob.glob('*.csv')
    
    # Process each file
    for file in csv_files:
        if not file.startswith('processed_') and not file.startswith('consolidated_'):
            try:
                visualizer.visualize_file(file)
            except Exception as e:
                print(f"Error visualizing {file}: {str(e)}")

if __name__ == "__main__":
    main() 