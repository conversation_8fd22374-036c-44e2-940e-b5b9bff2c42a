import pandas as pd
import glob
import os

def consolidate_csv_files():
    # Get all CSV files in the current directory
    csv_files = glob.glob('*.csv')
    
    # Create an empty list to store dataframes
    dfs = []
    
    # Read each CSV file and store its name as a new column
    for file in csv_files:
        try:
            df = pd.read_csv(file)
            # Add a source column to track which file the data came from
            df['source_file'] = file
            dfs.append(df)
            print(f"Successfully read {file}")
        except Exception as e:
            print(f"Error reading {file}: {str(e)}")
    
    if not dfs:
        print("No CSV files were successfully read!")
        return
    
    # Combine all dataframes
    consolidated_df = pd.concat(dfs, ignore_index=True)
    
    # Save the consolidated dataframe to a new CSV file
    output_file = 'consolidated_data.csv'
    consolidated_df.to_csv(output_file, index=False)
    print(f"\nConsolidated data saved to {output_file}")
    print(f"Total number of rows: {len(consolidated_df)}")
    print(f"Total number of columns: {len(consolidated_df.columns)}")
    print("\nColumn names in the consolidated file:")
    for col in consolidated_df.columns:
        print(f"- {col}")

if __name__ == "__main__":
    consolidate_csv_files() 