import pandas as pd
import numpy as np
import glob
import os
from collections import defaultdict

class KnowledgeGraphProfileCreator:
    def __init__(self):
        self.wallet_columns = ['wallet', 'wallet_address', 'address', 'user_address', 'user_wallet']
        self.output_file = 'knowledge_graph_profiles.csv'
        
    def find_wallet_column(self, df):
        """Find the column that contains wallet addresses in a dataframe"""
        for col in df.columns:
            if col.lower() in self.wallet_columns:
                return col
        return None
    
    def process_file(self, file_path):
        """Process a single knowledge graph file"""
        print(f"\nProcessing {file_path}...")
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        file_name = os.path.splitext(os.path.basename(file_path))[0]
        
        # Find wallet column
        wallet_col = self.find_wallet_column(df)
        if wallet_col is None:
            print(f"No wallet column found in {file_path}")
            return None
        
        # Create a new dataframe with all columns
        result_df = df.copy()
        
        # Rename columns to include source file (except wallet column)
        rename_dict = {col: f"{file_name}_{col}" for col in result_df.columns if col != wallet_col}
        result_df = result_df.rename(columns=rename_dict)
        
        return result_df
    
    def create_knowledge_graph_profiles(self):
        """Create consolidated knowledge graph profiles"""
        # Get only knowledge graph CSV files
        csv_files = glob.glob('knowledge_graph_*.csv')
        
        if not csv_files:
            print("No knowledge graph files found!")
            return
        
        print(f"Found {len(csv_files)} knowledge graph files:")
        for file in csv_files:
            print(f"- {file}")
        
        # Process each file
        dfs = []
        for file in csv_files:
            try:
                df = self.process_file(file)
                if df is not None:
                    dfs.append(df)
            except Exception as e:
                print(f"Error processing {file}: {str(e)}")
        
        if not dfs:
            print("No data to process!")
            return
        
        # Merge all dataframes on wallet address
        final_df = dfs[0]
        for df in dfs[1:]:
            # Find common wallet column
            wallet_col1 = self.find_wallet_column(final_df)
            wallet_col2 = self.find_wallet_column(df)
            
            if wallet_col1 and wallet_col2:
                print(f"\nMerging with {df.columns[0]}")
                print(f"Current shape: {final_df.shape}")
                print(f"New data shape: {df.shape}")
                
                final_df = pd.merge(final_df, df, 
                                  left_on=wallet_col1, 
                                  right_on=wallet_col2, 
                                  how='outer')
                
                # Drop duplicate wallet columns
                if wallet_col1 != wallet_col2:
                    final_df = final_df.drop(columns=[wallet_col2])
                
                print(f"Shape after merge: {final_df.shape}")
        
        # Clean up the final dataframe
        # Fill missing values with appropriate defaults
        numeric_cols = final_df.select_dtypes(include=[np.number]).columns
        categorical_cols = final_df.select_dtypes(include=['object']).columns
        
        # Fill numeric missing values with 0
        final_df[numeric_cols] = final_df[numeric_cols].fillna(0)
        
        # Fill categorical missing values with 'unknown'
        final_df[categorical_cols] = final_df[categorical_cols].fillna('unknown')
        
        # Save the consolidated dataframe
        final_df.to_csv(self.output_file, index=False)
        print(f"\nKnowledge graph profiles saved to {self.output_file}")
        print(f"Total number of unique users: {len(final_df)}")
        print(f"Total number of features: {len(final_df.columns)}")
        
        # Print column summary
        print("\nColumn Summary:")
        for col in final_df.columns:
            if col != wallet_col1:
                non_null = final_df[col].count()
                null_count = final_df[col].isnull().sum()
                print(f"- {col}:")
                print(f"  - Non-null values: {non_null}")
                print(f"  - Null values: {null_count}")
                print(f"  - Data type: {final_df[col].dtype}")
        
        # Print data quality metrics
        print("\nData Quality Metrics:")
        print(f"Total missing values: {final_df.isnull().sum().sum()}")
        print(f"Duplicate wallet addresses: {final_df[wallet_col1].duplicated().sum()}")
        
        # Print sample of the data
        print("\nSample of consolidated data (first 5 rows):")
        print(final_df.head())
        
        # Save column information to a separate file
        column_info = pd.DataFrame({
            'column_name': final_df.columns,
            'data_type': final_df.dtypes,
            'non_null_count': final_df.count(),
            'null_count': final_df.isnull().sum()
        })
        column_info.to_csv('knowledge_graph_columns_info.csv', index=False)
        print("\nColumn information saved to knowledge_graph_columns_info.csv")

def main():
    creator = KnowledgeGraphProfileCreator()
    creator.create_knowledge_graph_profiles()

if __name__ == "__main__":
    main() 