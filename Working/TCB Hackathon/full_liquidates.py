import csv
from pymongo import MongoClient

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")

# --- Connect to DB and collection ---
db = client["knowledge_graph"]
collection = db["liquidates"]

# --- Output CSV filename ---
filename = "1M_knowledge_graph_liquidates.csv"

# --- CSV headers ---
csv_headers = [
    "_id",
    "debtBuyerWallet",
    "liquidatedWallet",
    "timestamp",
    "collateralAmount",
    "collateralAsset",
    "collateralAssetInUSD",
    "collateralProtocol",
    "debtAmount",
    "debtAsset",
    "debtAssetInUSD",
    "debtProtocol"
]

# --- Projection to reduce data transfer ---
projection = {
    "_id": 1,
    "debtBuyerWallet": 1,
    "liquidatedWallet": 1,
    "liquidationLogs": 1
}

# --- Sample 1M documents ---
pipeline = [
    {"$sample": {"size": 1000000}},
    {"$project": projection}
]

cursor = collection.aggregate(pipeline)

# --- Write to CSV ---
with open(filename, "w", newline="", encoding="utf-8") as file:
    writer = csv.writer(file)
    writer.writerow(csv_headers)

    for doc in cursor:
        _id = doc.get("_id", "")
        buyer = doc.get("debtBuyerWallet", "")
        victim = doc.get("liquidatedWallet", "")
        logs = doc.get("liquidationLogs", {})

        buyer_logs = logs.get("debtBuyerWallet", {})
        victim_logs = logs.get("liquidatedWallet", {})

        timestamps = set(buyer_logs.keys()).union(victim_logs.keys())

        for ts in timestamps:
            b_log = buyer_logs.get(ts, {})
            v_log = victim_logs.get(ts, {})

            row = [
                _id,
                buyer,
                victim,
                ts,
                b_log.get("collateralAmount", ""),
                b_log.get("collateralAsset", ""),
                b_log.get("collateralAssetInUSD", ""),
                b_log.get("protocol", ""),
                v_log.get("debtAmount", ""),
                v_log.get("debtAsset", ""),
                v_log.get("debtAssetInUSD", ""),
                v_log.get("protocol", "")
            ]
            writer.writerow(row)

print(f"✅ Exported 1M documents from 'full_knowledge_graph.liquidates' to '{filename}'")
