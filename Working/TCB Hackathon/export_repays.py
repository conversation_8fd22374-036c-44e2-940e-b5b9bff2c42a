from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["repays"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "walletAddress": 1,
    "lendingPoolAddress": 1,
    "repayLogs.1675927952.tokens.******************************************": 1,
    "repayLogs.1675927952.valueInUSD": 1,
    "totalAmountOfRepayInUSD": 1,
    "totalNumberOfRepay": 1,
    "highestRepayInUSD": 1,
    "lowestRepayInUSD": 1,
    "averageRepayInUSD": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("knowledge_graph_repays_50K.csv", index=False)
print("✅ Exported first 50,000 documents to repays_export_50k.csv")
