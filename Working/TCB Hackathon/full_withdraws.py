from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["withdraws"]

# --- Flattening ---
flattened_data = []

# Limit to 1M documents
for doc in collection.find().limit(1000000):
    withdraw_logs = doc.get("withdrawLogs", {})

    for timestamp, log_data in withdraw_logs.items():
        tokens = log_data.get("tokens", {})
        for token_addr, token_value in tokens.items():
            flattened_data.append({
                "_id": doc.get("_id"),
                "walletAddress": doc.get("walletAddress"),
                "lendingPoolAddress": doc.get("lendingPoolAddress"),
                "timestamp": int(timestamp),
                "token": token_addr,
                "tokenValue": token_value,
                "valueInUSD": log_data.get("valueInUSD"),
                "totalNumberOfWithdraw": doc.get("totalNumberOfWithdraw"),
                "totalAmountOfWithdrawInUSD": doc.get("totalAmountOfWithdrawInUSD"),
                "highestWithdrawInUSD": doc.get("highestWithdrawInUSD"),
                "lowestWithdrawInUSD": doc.get("lowestWithdrawInUSD"),
                "averageWithdrawInUSD": doc.get("averageWithdrawInUSD"),
            })

# --- Export to CSV ---
df = pd.DataFrame(flattened_data)
df.to_csv("1M_knowledge_graph_withdraws.csv", index=False)
print(f"✅ Exported {len(df)} rows to 1M wallets ata.csv")
