from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient(
    "*************************************************************************************************************",
    serverSelectionTimeoutMS=60000,
    socketTimeoutMS=60000
)
db = client["knowledge_graph"]
collection = db["wallets"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "chainId": 1,
    "address": 1,
    "lastUpdatedAt": 1,
    "flagged": 1,
    "dailyAllTransactions.1689120000": 1,
    "dailyAllTransactions.1689465600": 1,
    "theFirstToHash": 1,
    "theFirstToTimestamp": 1,
    "theFirstFromHash": 1,
    "theFirstFromTimestamp": 1,
    "type": 1
}

# --- Optimized aggregation pipeline with $match ---
pipeline = [
    {"$match": {"flagged": 1}},               # Filter down the dataset by flag
    {"$sample": {"size": 1000000}},             # Then take a random sample
    {"$project": projection}                  # Only keep necessary fields
]

# --- Run aggregation ---
cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("1M_knowledge_graph_wallets.csv", index=False)
print("✅ Exported 1M sampled documents to knowledge_graph_wallets_50K.csv")
