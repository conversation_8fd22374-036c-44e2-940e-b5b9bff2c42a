from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["smart_contracts"]

# --- Projection for smart contract fields (including token fields if available) ---
projection = {
    "_id": 1,
    "address": 1,
    "chainId": 1,
    "keyABI": 1,
    "name": 1,
    "imgUrl": 1,
    "tags": 1,
    "createdAt": 1,
    "numberOfLastDayCalls": 1,
    "numberOfLastDayActiveUsers": 1,
    "numberOfDailyCalls": 1,
    "numberOfDailyActiveUsers": 1,
    "symbol": 1,
    "decimals": 1,
    "idCoingecko": 1,
    "tokenHealth": 1,
    "price": 1,
    "marketCap": 1,
    "tradingVolume": 1,
    "totalSupply": 1,
    "tokenDailyTransfers": 1,
    "categories": 1,
    "priceChangeLogs": 1,
    "marketCapChangeLogs": 1,
    "tradingVolumeChangeLogs": 1,
    "numberOfHolders": 1,
    "socialAccounts": 1
}

# --- Optional filter: only smart contracts with "token" in tags ---
filter_query = {
    "tags": {"$in": ["token"]}
}

# --- Sample 50,000 smart contracts (token type) ---
pipeline = [
    {"$match": filter_query},
    {"$sample": {"size": 50000}},
    {"$project": projection}
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("addon_smart_contracts_token_50k.csv", index=False)
print("✅ Exported 50,000 smart contracts (tokens) to ethereum_smart_contracts_token_50k.csv")
