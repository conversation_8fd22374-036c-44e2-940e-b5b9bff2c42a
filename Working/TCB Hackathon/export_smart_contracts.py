from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["smart_contracts"]

# --- Projection fields ---
projection = {
    "_id": 1,
    "address": 1,
    "abi": 1,
    "tags.token": 1,
    "name": 1,
    "symbol": 1,
    "decimals": 1,
    "categories.Ethereum Ecosystem": 1,
    "marketCap": 1,
    "totalSupply": 1,
    "tradingVolume": 1,
    "price": 1,
    "numberOfHolders": 1,
    "lastUpdatedAt": 1,
    "keyABI": 1,
    "idCoingecko": 1,
    "imgUrl": 1,
    "chainId": 1,
    "priceChangeLogs.1676050546": 1,
    "priceChangeLogs.1677569743": 1,
    "numberOfLastDayCalls": 1,
    "numberOfDailyCalls.1688601600": 1,
    "numberOfThisMonthCalls": 1,
    "numberOfLastDayActiveUsers": 1,
    "numberOfDailyActiveUsers.1688601600": 1,
    "tokenDailyTransfers.1688601600": 1
}

# --- Randomly sample 50,000 documents using aggregation ---
pipeline = [
    {"$sample": {"size": 50000}},       # Randomly sample
    {"$project": projection}     # Apply projection
]

cursor = collection.aggregate(pipeline)

# --- Load into DataFrame ---
df = pd.DataFrame(list(cursor))

# --- Export to CSV ---
df.to_csv("knowledge_graph_smart_contracts_50K.csv", index=False)
print("✅ Exported first 50,000 documents to smart_contracts_export_50k.csv")
