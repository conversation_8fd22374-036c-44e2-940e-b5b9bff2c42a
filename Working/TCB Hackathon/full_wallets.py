from pymongo import MongoClient
import pandas as pd

# --- MongoDB connection ---
client = MongoClient("*************************************************************************************************************")
db = client["knowledge_graph"]
collection = db["wallets"]

# --- Discover all unique timestamps in dailyAllTransactions ---
unique_timestamps = set()

print("Scanning for all available timestamps...")
for doc in collection.find().limit(1000000):
    daily_all_tx = doc.get("dailyAllTransactions", {})
    if isinstance(daily_all_tx, dict):
        unique_timestamps.update(daily_all_tx.keys())

timestamps = sorted(unique_timestamps)  # Optional: sort for consistency
print("Discovered timestamps:", timestamps)

# --- Document extraction and flattening ---
flattened_data = []

for doc in collection.find().limit(1000000):
    daily_all_tx = doc.get("dailyAllTransactions", {})

    for ts in timestamps:
        if ts in daily_all_tx:
            flattened_data.append({
                "_id": doc.get("_id"),
                "chainId": doc.get("chainId"),
                "address": doc.get("address"),
                "lastUpdatedAt": doc.get("lastUpdatedAt"),
                "flagged": doc.get("flagged"),
                "timestamp": ts,
                "dailyAllTransactions": daily_all_tx.get(ts),
                "theFirstToHash": doc.get("theFirstToHash"),
                "theFirstToTimestamp": doc.get("theFirstToTimestamp"),
                "theFirstFromHash": doc.get("theFirstFromHash"),
                "theFirstFromTimestamp": doc.get("theFirstFromTimestamp"),
                "type": doc.get("type")
            })

# --- Save to CSV ---
df = pd.DataFrame(flattened_data)
df.to_csv("1M_wallets_daily_transactions.csv", index=False)
print("Exported", len(df), "rows to wallets_daily_transactions_1M.csv")
